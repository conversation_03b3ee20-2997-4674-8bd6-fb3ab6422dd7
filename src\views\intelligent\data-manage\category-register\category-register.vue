<template>
  <div class="flex h-full w-full flex-col">
    <h2 class="flex h-[60px] items-center bg-w pl-5 text-xl font-semibold">目录注册</h2>

    <el-container class="flex h-0 flex-1 p-5">
      <el-aside class="mr-[10px] rounded bg-w">
        <CatalogList v-model="catalogId" @get-name="getCatalogName" />
      </el-aside>

      <el-main class="rounded bg-w">
        <div class="flex h-full flex-col">
          <div class="flex justify-between px-10 pt-5">
            <el-button class="add-button" type="primary" @click="onAdd"> 添加目录 </el-button>
            <!-- <div>
              <el-input v-model="search" placeholder="请输入关键字">
                <template #prepend>
                  <el-select v-model="searchPre" style="width: 100px">
                    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </template>
                <template #append>
                  <el-button :icon="Search" @click="onSearch" />
                </template>
              </el-input>
            </div> -->
          </div>

          <div class="mt-4 h-0 flex-1 px-10">
            <el-table v-loading="loading" :data="tableData" style="width: 100%" height="100%" class="c-table-header">
              <el-table-column prop="title" label="目录名称" />
              <el-table-column prop="type" label="目录分类" />
              <el-table-column prop="description" label="目录摘要" show-overflow-tooltip />
              <!-- <el-table-column prop="notes" label="目录备注">
                <template #default="{ row }">
                  <span class="status" :class="statusClass[row.state]">{{ statusText[row.state] }}</span>
                </template>
              </el-table-column> -->
              <el-table-column label="操作" width="150">
                <template #default="{ row }">
                  <el-button v-if="row.state !== 1" link type="primary" @click="onView(row)"> 查看 </el-button>
                  <el-button v-if="![2, 3, 4].includes(row.state)" link type="primary" @click="onEdit(row)">
                    编辑
                  </el-button>
                  <!-- <el-button v-if="![2, 4].includes(row.state)" link type="primary" @click="onAudit(row)">
                    送审
                  </el-button> -->
                  <el-popconfirm title="确定删除此项？" @confirm="onCancellation(row)">
                    <template #reference>
                      <el-button link type="primary"> 删除 </el-button>
                    </template>
                  </el-popconfirm>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-main>
    </el-container>
  </div>

  <RegisterDrawer v-model="showDrawer" :data="data" :readonly="readonly" @success="onSuccess" />
</template>

<script setup>
  import { ElMessage } from 'element-plus';
  import { getChildCatalogue, deleteCatalogue } from '@/api/index';
  import { Search } from '@element-plus/icons-vue';
  import RegisterDrawer from '../components/RegisterDrawer.vue';
  import CatalogList from '../components/CatalogList.vue';

  const catalogId = ref('');
  watchEffect(() => {
    if (catalogId.value) {
      fetchData();
    }
  });
  const catalogName = ref('');
  const getCatalogName = (value) => {
    catalogName.value = value;
  };

  const searchPre = ref('目录代码');
  const search = ref('');
  const options = [
    {
      value: '目录代码',
      label: '目录代码',
    },
    {
      value: '目录名称',
      label: '目录名称',
    },
    {
      value: '目录摘要',
      label: '目录摘要',
    },
  ];
  const onSearch = () => {};

  const loading = ref(false);
  const tableData = ref([
    // {
    //   id: 1,
    //   code: '4563223568',
    //   name: '目录名称1',
    //   abstract: '信息资源',
    //   state: 1,
    // },
  ]);
  async function fetchData() {
    try {
      loading.value = true;
      const { data } = await getChildCatalogue(catalogId.value);
      tableData.value = data;
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }
  const statusText = {
    1: '待送审',
    2: '已送审',
    3: '审核未通过',
    4: '已发布',
  };
  const statusClass = {
    1: 'status-gray',
    2: 'status-blue',
    3: 'status-red',
    4: 'status-green',
  };
  const onAudit = () => {};
  const onCancellation = async (row) => {
    try {
      loading.value = true;
      await deleteCatalogue(row.id, true);
      ElMessage({ type: 'success', message: '删除成功' });
      fetchData();
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  };

  let data = ref({ id: '', parentId: '', parentName: '' });
  let readonly = ref(false);
  const showDrawer = ref(false);
  const onAdd = () => {
    data.value = {
      id: '',
      parentId: catalogId.value,
      parentName: catalogName.value,
    };
    readonly.value = false;
    showDrawer.value = true;
  };
  const onView = (item) => {
    data.value = {
      id: item.id,
      parentId: catalogId.value,
      parentName: catalogName.value,
    };
    readonly.value = true;
    showDrawer.value = true;
  };
  const onEdit = (item) => {
    data.value = {
      id: item.id,
      parentId: catalogId.value,
      parentName: catalogName.value,
    };
    readonly.value = false;
    showDrawer.value = true;
  };
  const onSuccess = () => {
    fetchData();
  };
</script>

<style scoped lang="scss">
  .el-main {
    --el-main-padding: 0;
  }

  .status::before {
    content: '';
    display: inline-block;
    border-radius: 999px;
    width: 6px;
    height: 6px;
    margin-right: 6px;
    position: relative;
    top: -2px;
  }
  .status-green::before {
    background: var(--el-color-success);
  }
  .status-gray::before {
    background: $color-tip-text;
  }
  .status-blue::before {
    background: $color-blue;
  }
  .status-red::before {
    background: var(--el-color-danger);
  }
</style>
