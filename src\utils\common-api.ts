import { getUserInfor, findEnrollmentByUserId } from '@/api/index';
import { useUsers } from '@/store/user-info.js';

export async function fetchUser() {
  const store = useUsers();
  const [res1, res2] = await Promise.all([
    getUserInfor(store.user.username, { userName: store.user.username }),
    findEnrollmentByUserId(store.user.id, 1, 1, 1000, {} as any),
  ]);
  const data: any = res1.data;
  const lockFlag = data?.rltSybole?.lockFlag ?? '1';
  store.setLockFlag(lockFlag);
  if (data?.roleVOList && data.roleVOList.length) {
    store.setRole(data.roleVOList.map((item: any) => item.roleCode));
  } else {
    store.setRole([]);
  }
  store.setUserType(data.userType);

  const data2: any = res2.data;
  if (data2?.content && data2?.content.length) {
    store.setRegisterId(data2.content[data2.content.length - 1].id);
  }
}
