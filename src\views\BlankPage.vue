<template>
  <div class="blank-page"></div>
</template>

<script setup lang="ts">
  import { useRouter } from 'vue-router';
  const router = useRouter();
  import { useUsers } from '@/store/index';
  const store = useUsers();

  // 空白页，无逻辑
  onMounted(() => {
    router.replace({ name: store.loggedHome });
  });
</script>

<style scoped>
  .blank-page {
    width: 100vw;
    height: 100vh;
    background: #fff;
  }
</style>
