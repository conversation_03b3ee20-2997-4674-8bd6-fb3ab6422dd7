<template>
  <el-dialog v-model="modelValue" title="成员管理" width="80%">
    <div v-if="!readonly" class="mb-3">
      <el-button type="primary" @click="onAdd"> 新增 </el-button>
      <el-button type="danger" :disabled="!selectedRows.length" @click="onBatchDel"> 批量删除 </el-button>
    </div>
    <el-table
      v-loading="loading"
      :data="tableData"
      style="width: 100%"
      class="c-table-header"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="userName" label="用户名" />
      <el-table-column prop="name" label="姓名" />
      <!-- <el-table-column prop="roleName" label="角色" />
      <el-table-column prop="rltTime.createTime" label="加入时间" /> -->
      <!-- <el-table-column prop="org" label="研究机构" />
      <el-table-column prop="leadResearcher" label="首席研究员" />
      <el-table-column label="合作者">
        <template #default="{ row }">
          <div v-for="(item, i) in row.cooperator" :key="i">
            <span>{{ item.name }}</span>
            <span v-if="row.deputy == i">(代表)</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="orgName" label="机构官方名称" />
      <el-table-column prop="orgContact" label="机构联系人" /> -->
      <el-table-column v-if="!readonly" label="操作" width="80">
        <template #default="{ row }">
          <el-popconfirm title="确定删除此项？" @confirm="onDel(row)">
            <template #reference>
              <el-button link type="primary">删除</el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>

  <TeamCreatedAdd v-model="showAdd" :team-id="id" />
</template>

<script setup lang="ts">
  import { findTeamUserRoleVOByTeamId, deleteTeamUserById } from '@/api';
  import TeamCreatedAdd from './TeamCreatedAdd.vue';
  import { ElMessage } from 'element-plus';

  const modelValue = defineModel<boolean>({ required: true });
  interface Props {
    id: number | string;
    readonly?: boolean;
  }
  const props = defineProps<Props>();

  const loading = ref(false);
  const tableData = ref<TeamUserRoleVO[]>([]);
  const selectedRows = ref<TeamUserRoleVO[]>([]);

  async function fetchData() {
    try {
      loading.value = true;
      const { data } = await findTeamUserRoleVOByTeamId(Number(props.id), {} as any);
      tableData.value = data || [];
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  const showAdd = ref(false);
  const onAdd = () => {
    showAdd.value = true;
    modelValue.value = false;
  };
  const onDel = async (row: TeamUserRoleVO) => {
    try {
      loading.value = true;
      await deleteTeamUserById([row.teamUserId!]);
      ElMessage({ type: 'success', message: '删除成功' });
      fetchData();
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  };

  const handleSelectionChange = (rows: TeamUserRoleVO[]) => {
    selectedRows.value = rows;
  };

  const onBatchDel = async () => {
    if (!selectedRows.value.length) return;
    try {
      loading.value = true;
      const ids = selectedRows.value.map((row) => row.teamUserId!);
      await deleteTeamUserById(ids);
      ElMessage({ type: 'success', message: '批量删除成功' });
      fetchData();
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  };

  watch(modelValue, (value) => {
    if (value) {
      fetchData();
    }
  });
  watch(showAdd, (value) => {
    if (!value) {
      modelValue.value = true;
    }
  });
</script>
