<template>
  <div class="flex h-full flex-col">
    <div class="h-0 flex-1">
      <el-scrollbar v-loading="loading" height="100%">
        <!-- <ul v-if="tableData.length" class="overflow-hidden px-5 pt-5">
          <li v-for="(item, index) in tableData" :key="index" class="mb-5 rounded bg-w px-7 py-4">
            <div class="flex items-center justify-between">
              <span>{{ item.teamName }}</span>

              <el-popconfirm v-if="!readonly" title="确定删除？" @confirm="onDel(item)">
                <template #reference>
                  <div class="flex cursor-pointer items-center text-sm text-tip">
                    <el-icon color="#939899">
                      <Delete />
                    </el-icon>
                    <span class="ml-2">删除团队</span>
                  </div>
                </template>
              </el-popconfirm>
            </div>

            <el-form label-position="right" label-width="100" :model="item" class="mt-5">
              <div class="flex items-start justify-center">
                <div class="flex w-[200px] items-center pt-1">
                  <div class="h-[13px] w-[3px] bg-p" />
                  <span class="ml-2">选择机构</span>
                </div>
                <el-form-item label="研究机构:">
                  <el-input v-model="item.org" :disabled="readonly" style="width: 480px" />
                </el-form-item>
              </div>

              <div class="mt-5 flex items-start justify-center">
                <div class="flex w-[200px] items-center pt-1">
                  <div class="h-[13px] w-[3px] bg-p" />
                  <span class="ml-2">人员配置</span>
                </div>
                <el-form-item label="首席研究员:">
                  <el-input v-model="item.leadResearcher" :disabled="readonly" style="width: 480px" />
                </el-form-item>
              </div>

              <div class="flex justify-center">
                <div class="w-[200px]" />
                <el-form-item label="合作者:" label-width="150">
                  <ul class="flex flex-col">
                    <div
                      v-for="(cooperatorItem, cooperatorIndex) in item.cooperator"
                      :key="cooperatorIndex"
                      class="relative"
                    >
                      <el-input v-model="cooperatorItem.name" :disabled="readonly" style="width: 480px" class="mb-4" />
                      <el-checkbox
                        v-if="item.deputy === cooperatorIndex"
                        :checked="true"
                        label="代表"
                        :disabled="readonly"
                        class="absolute right-[-16px] top-[-5px]"
                      />
                    </div>
                  </ul>
                </el-form-item>
              </div>

              <div class="flex items-start justify-center">
                <div class="flex w-[200px] items-center pt-1">
                  <div class="h-[13px] w-[3px] bg-p" />
                  <span class="ml-2">材料转让协议信息</span>
                </div>
                <el-form-item label="机构官方名称:">
                  <el-input v-model="item.orgName" :disabled="readonly" style="width: 480px" />
                </el-form-item>
              </div>

              <div class="flex justify-center">
                <div class="w-[200px]" />
                <el-form-item label="机构联系人:">
                  <el-input v-model="item.orgContact" :disabled="readonly" style="width: 480px" />
                </el-form-item>
              </div>
            </el-form>
          </li>
        </ul> -->
        <div class="px-5 pt-5">
          <div class="bg-w rounded p-4">
            <div class="mb-4 flex">
              <el-button type="primary" @click="onAdd"> 添加团队 </el-button>
            </div>

            <el-table ref="multipleTableRef" :data="tableData" style="width: 100%" class="c-table-header">
              <el-table-column type="index" width="55" />
              <el-table-column prop="teamName" label="团队名称" min-width="100px" />
              <el-table-column label="操作" width="150">
                <template #default="{ row }">
                  <el-button link type="primary" @click="onTeam(row)">团队成员</el-button>
                  <el-popconfirm title="确定删除此项？" @confirm="onDel(row)">
                    <template #reference>
                      <el-button link type="primary">删除</el-button>
                    </template>
                  </el-popconfirm>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-scrollbar>
    </div>

    <!-- <div v-if="!readonly" class="action-footer">
      <el-button type="primary" @click="onSubmit"> 保存 </el-button>
    </div> -->
  </div>

  <TeamInfoDialog v-model="showDialog" :org-id="id" :added-teams="tableData" @success="onSuccess" />
  <TeamCreatedMember :id="teamId" v-model="showMember" readonly />
</template>

<script setup lang="ts">
  import TeamCreatedMember from '@/views/logged-layout/user-center/my-team/TeamCreatedMember.vue';
  import TeamInfoDialog from './TeamInfoDialog.vue';
  import { deleteApplicationTeamById, findApplicationTeamByAppId } from '@/api';
  import { ElMessage } from 'element-plus';
  const props = defineProps({
    state: {
      type: String,
      default: '',
    },
    id: String,
  });

  const readonly = computed(() => {
    return ['待审批', '审核通过'].includes(props.state);
  });
  const showMember = ref(false);
  const teamId = ref(0);

  const tableData = ref<any[]>([
    // {
    //   org: 'XXXXXXX大学',
    //   leadResearcher: '<EMAIL>',
    //   cooperator: [{ name: '<EMAIL>' }, { name: '<EMAIL>' }, { name: '<EMAIL>' }],
    //   deputy: 0,
    //   orgName: 'XXXXXXXXXX机构',
    //   orgContact: '<EMAIL>',
    // },
  ]);

  const loading = ref(false);
  async function fetchData() {
    try {
      loading.value = true;
      const { data } = await findApplicationTeamByAppId(Number(props.id));
      tableData.value = data || [];
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  const onDel = async (item: ApplicationTeamVO) => {
    try {
      loading.value = true;
      await deleteApplicationTeamById([item.applicationTeamId!]);
      ElMessage({ type: 'success', message: '删除成功' });
      fetchData();
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  };

  const onTeam = (item: ApplicationTeamVO) => {
    showMember.value = true;
    teamId.value = item.applicationTeamId?.teamId || 0;
  };

  const showDialog = ref(false);
  const onAdd = () => {
    showDialog.value = true;
  };
  const onSuccess = () => {
    fetchData();
  };

  const onSubmit = () => {};

  onBeforeMount(() => {
    fetchData();
  });
</script>
