<template>
  <div class="flex h-full w-full flex-col overflow-hidden">
    <h2 class="flex h-[60px] cursor-pointer items-center bg-w pl-5 text-xl font-semibold" @click="onBack">
      <el-icon class="mr-2" color="#939899">
        <ArrowLeft />
      </el-icon>
      审批
    </h2>

    <div class="m-5 flex flex-1 justify-center rounded bg-w pt-10">
      <div class="w-[600px]">
        <el-form ref="formRef" label-position="left" label-width="120px" :model="formLabelAlign" :rules="rules">
          <el-form-item label="信息资源分类">
            <div class="absolute -left-72 flex items-center justify-start">
              <div class="mr-2 h-[12px] w-[2.5px] bg-p" />
              <span class="text-base font-semibold">目录分类</span>
            </div>
            <el-input v-model="formLabelAlign.classify" disabled />
          </el-form-item>
          <el-form-item label="信息资源代码">
            <div class="absolute -left-72 flex items-center justify-start">
              <div class="mr-2 h-[12px] w-[2.5px] bg-p" />
              <span class="text-base font-semibold">目录信息</span>
            </div>
            <el-input v-model="formLabelAlign.code" disabled />
          </el-form-item>
          <el-form-item label="信息资源名称">
            <el-input v-model="formLabelAlign.name" disabled />
          </el-form-item>
          <el-form-item label="信息资源摘要">
            <el-input v-model="formLabelAlign.abstract" type="textarea" :rows="4" disabled />
          </el-form-item>
          <el-form-item class="mb-10" label="更新周期">
            <el-input v-model="formLabelAlign.period" disabled />
          </el-form-item>
          <el-form-item label="审核意见" prop="opinion">
            <div class="absolute -left-72 top-0 flex items-center justify-start">
              <div class="mr-2 h-[12px] w-[2.5px] bg-p" />
              <span class="text-base font-semibold">目录审批</span>
            </div>
            <el-input
              v-model="formLabelAlign.opinion"
              placeholder="请输入审核意见"
              maxlength="300"
              type="textarea"
              :rows="4"
              show-word-limit
            />
          </el-form-item>
          <el-form-item label="常用审核意见" prop="commonOpinion">
            <el-radio-group v-model="formLabelAlign.commonOpinion">
              <el-radio label="1"> 通过 </el-radio>
              <el-radio label="2"> 不通过 </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSave"> 审核 </el-button>
            <el-button @click="onBack"> 取消 </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { reactive } from 'vue';
  import { useRouter } from 'vue-router';
  let router = useRouter();

  const props = defineProps({
    id: { type: String },
  });
  const onBack = () => {
    router.back();
  };

  let formLabelAlign = reactive({
    classify: '',
    code: '',
    name: '',
    abstract: '',
    period: '',
    opinion: '',
    commonOpinion: '',
  });
  const rules = reactive({
    opinion: [{ required: true, message: '请输入审核意见', trigger: 'blur' }],
    commonOpinion: [{ required: true, message: '请选择常用审核意见', trigger: 'blur' }],
  });
  const formRef = ref();
  const onSave = () => {
    formRef.value.validate((valid) => {
      if (valid) {
        router.replace({ name: 'IntelligentAudit' });
      }
    });
  };
</script>
