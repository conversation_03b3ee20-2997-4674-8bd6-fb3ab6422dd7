<template>
  <div class="flex flex-col justify-start rounded-lg bg-w pb-10 pl-6 pr-10 pt-4">
    <div class="flex justify-between">
      <h4 class="text-lg font-bold">待办事项</h4>
      <div class="cursor-pointer text-xs" @click="onMore">
        <span class="mr-1 text-tip">审批中心</span>
        <el-icon :size="10">
          <ArrowRightBold color="#939899" :size="20" />
        </el-icon>
      </div>
    </div>

    <div class="mt-7 table">
      <el-table :data="tableData" class="c-table-header" height="100%">
        <el-table-column label="申请人" prop="name" />
        <el-table-column label="事项" prop="title" />
        <el-table-column label="部门" min-width="200px">
          <template #default="{ row }">
            <div class="flex items-center">
              <span>{{ row.department }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="职位" prop="posts" />
        <el-table-column label="状态">
          <template #default="scope">
            <div class="status-text" :class="getStatusClass(scope.row)">
              {{ scope.row.status }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="applicationDate" label="申请日期" />
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button link type="primary" @click="toTransact(row)"> 办理 </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup>
  import { useRouter } from 'vue-router';
  const router = useRouter();

  const tableData = ref([
    {
      id: 1,
      name: '张筱雨',
      title: '加入机构申请',
      department: 'XXXXX部门 / 技术部 / 开发组',
      posts: '开发工程师',
      status: '待审批',
      applicationDate: '2022-08-11 12:00:00',
    },
    {
      id: 2,
      name: '黎明',
      title: '加入机构申请',
      department: 'XXXXX部门 / 技术部 / 开发组',
      posts: '开发工程师',
      status: '待审批',
      applicationDate: '2022-08-11 12:00:00',
    },
  ]);
  const getStatusClass = (row) => {
    let className = '';
    switch (row.status) {
      case '待审批':
        className = 'status-to-start';
        break;
      // case '进行中':
      //   className = 'status-under-way';
      //   break;
      case '已审批':
        className = 'status-finished';
        break;
    }
    return className;
  };

  //办理
  const toTransact = (row) => {
    router.push({ name: 'OrgApprove', query: { id: row.id } });
  };
  //更多
  const onMore = () => {
    router.push({ name: 'OrgApprove' });
  };
</script>

<style lang="scss" scoped>
  .status-text::before {
    content: '';
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 6px;
    margin-right: 6px;
    position: relative;
    top: -2px;
  }

  .status-to-start::before {
    background: #2979ff;
  }

  .status-under-way::before {
    background: #24b383;
  }

  .status-finished::before {
    background: #939899;
  }
</style>
