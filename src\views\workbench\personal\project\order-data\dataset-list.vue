<template>
  <div v-loading="loading" class="px-10 pt-5">
    <h2 class="mt-[25px] text-[28px] font-bold">{{ datasetName }}</h2>

    <el-table
      :key="'fieldTable' + datasetId"
      :data="subData"
      style="width: 100%"
      class="c-table-header mt-4 mb-4"
      ref="fieldTableRef"
      @selection-change="handleSelectionChange2"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="id" label="类别ID">
        <template #default="{ row }">
          <el-button link type="primary" @click="onDetail(row)">
            {{ row.id }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="tableName" label="量表英文名称" min-width="100px">
        <template #default="{ row }">
          {{ row.tableName }}<span v-if="row.visitPhase">（{{ row.visitPhase }}）</span>
        </template>
      </el-table-column>
      <el-table-column prop="tableChineseName" label="量表中文名称" min-width="100px">
        <template #default="{ row }">
          {{ row.tableChineseName }}<span v-if="row.visitPhaseChinese">（{{ row.visitPhaseChinese }}）</span>
        </template>
      </el-table-column>
      <el-table-column prop="fieldCount" label="数据字段" />
      <el-table-column prop="description" label="说明" />
    </el-table>
  </div>
</template>

<script setup lang="ts">
  import { findTablesByFileInforId } from '@/api/index';
  import { useDataBrowse } from '@/store/index';
  import { ElTable } from 'element-plus';
  import { useRouter } from 'vue-router';
  const router = useRouter();
  const storeBrowse = useDataBrowse();

  interface Props {
    datasetId: number;
    datasetName: string;
  }
  const props = defineProps<Props>();
  const emit = defineEmits<{ gotoTable: [id: number, name: string] }>();
  const loading = ref(false);
  const subData = ref<CBDDefTableVO[]>([]);
  const fieldTableRef = ref<InstanceType<typeof ElTable>>();

  const handleSelectionChange2 = (val: CBDDefTableVO[]) => {
    storeBrowse.setFileds(
      storeBrowse.dataBrowse.id,
      val.map((item) => item.id!)
    );
  };

  const setFiledSelection = () => {
    const fileds = storeBrowse.dataBrowse.fileds;
    const existItem = fileds.find((f) => f.id === storeBrowse.dataBrowse.id);
    if (existItem) {
      existItem.data.forEach((f) => {
        subData.value.forEach((v) => {
          if (v.id === f) {
            fieldTableRef.value?.toggleRowSelection(v, true);
          }
        });
      });
    }
  };

  const onDetail = (row: CBDDefTableVO) => {
    emit('gotoTable', row.id!, row.tableChineseName!);
  };

  async function fetchData() {
    try {
      loading.value = true;
      const { data } = await findTablesByFileInforId(+props.datasetId, 1, 999);
      subData.value = data?.content || [];
      nextTick(() => {
        setFiledSelection();
      });
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  watchEffect(() => {
    if (props.datasetId) {
      fetchData();
    }
  });
</script>

<style lang="scss" scoped>
  .c-table-header {
    :deep(.el-checkbox__inner) {
      border-color: #007f99;
    }
  }
</style>
