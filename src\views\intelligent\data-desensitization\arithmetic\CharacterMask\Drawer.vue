<template>
  <el-drawer v-model="drawer" class="relative" @close="onCancel">
    <template #header>
      <h4 class="text-m">{{ action }}字符遮盖脱敏</h4>
    </template>

    <div class="mb-4 flex items-center">
      <div class="h-[13px] w-[3px] bg-p" />
      <span class="ml-2">配置</span>
    </div>
    <el-form ref="formRef" label-position="top" :model="form" :rules="rules">
      <el-form-item label="名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入名称" maxlength="32" show-word-limit :disabled="readonly" />
      </el-form-item>
      <el-form-item label="类型" prop="type">
        <el-radio-group v-model="form.type" :disabled="readonly">
          <el-radio label="1"> 专用 </el-radio>
          <el-radio label="2"> 通用 </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="敏感词" prop="sensitive">
        <el-select v-model="form.sensitive" placeholder="请选择敏感词" style="width: 100%" :disabled="readonly">
          <el-option v-for="item in sensitiveOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="选择规则" prop="rule">
        <el-select v-model="form.rule" placeholder="请选择规则" style="width: 100%" :disabled="readonly">
          <el-option v-for="item in ruleOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="掩盖位置" prop="position">
        <TowNumber v-model="form.position" :disabled="readonly" />
      </el-form-item>
      <el-form-item label="掩盖方式" prop="way">
        <el-select v-model="form.way" placeholder="请选择掩盖方式" style="width: 100%" :disabled="readonly">
          <el-option v-for="item in wayOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="掩盖字符" prop="character">
        <el-radio-group v-model="form.character" :disabled="readonly">
          <el-radio label="1"> * </el-radio>
          <el-radio label="2"> # </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="算法级别" prop="level">
        <el-select v-model="form.level" placeholder="请选择算法级别" style="width: 100%" :disabled="readonly">
          <el-option v-for="item in levelOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
    </el-form>

    <div class="mb-4 mt-7 flex items-center">
      <div class="h-[13px] w-[3px] bg-p" />
      <span class="ml-2">测试</span>
    </div>
    <el-form ref="testRef" label-position="top" :model="testForm" :rules="testRules" hide-required-asterisk>
      <el-form-item label="输入原始数据" prop="originData">
        <div class="flex w-full">
          <el-input v-model="form.originData" class="mr-4 flex-1" placeholder="请输入原始数据" maxlength="300" />
          <el-button type="primary" plain @click="onTest"> 测试 </el-button>
        </div>
      </el-form-item>
      <el-form-item label="脱敏结果">
        <el-input v-model="result" type="textarea" disabled :rows="4" />
      </el-form-item>
    </el-form>

    <template v-if="!readonly" #footer>
      <el-button @click="onCancel"> 取消 </el-button>
      <el-button type="primary" @click="onSave"> 保存 </el-button>
    </template>
  </el-drawer>
</template>

<script setup>
  import TowNumber from '../components/TowNumber.vue';
  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
      required: true,
    },
    data: {
      type: Object,
    },
    readonly: {
      type: Boolean,
      default: false,
    },
  });

  const sensitiveOptions = ref([
    {
      value: '1',
      label: '身份证',
    },
  ]);
  const ruleOptions = ref([
    {
      value: '1',
      label: '保留前n后m',
    },
  ]);
  const wayOptions = ref([
    {
      value: '1',
      label: '固定字符',
    },
  ]);
  const levelOptions = ref([
    {
      value: '1',
      label: '中级',
    },
  ]);
  const form = reactive({
    name: '',
    type: '',
    sensitive: '',
    rule: '',
    position: '',
    way: '',
    character: '',
    level: '',
  });
  const rules = reactive({
    name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
    type: [{ required: true, message: '请选择类型', trigger: 'blur' }],
    sensitive: [{ required: true, message: '请选择敏感词', trigger: 'blur' }],
    rule: [{ required: true, message: '请选择规则', trigger: 'blur' }],
    position: [{ required: true, message: '请输入掩盖位置', trigger: 'blur' }],
    way: [{ required: true, message: '请选择掩盖方式', trigger: 'blur' }],
    character: [{ required: true, message: '请选择掩盖字符', trigger: 'blur' }],
    level: [{ required: true, message: '请选择算法级别', trigger: 'blur' }],
  });
  const testForm = reactive({
    originData: '',
  });
  const testRules = reactive({
    originData: [{ required: true, message: '请输入原始数据', trigger: 'blur' }],
  });
  const result = ref('');
  const testRef = ref();
  const onTest = () => {
    testRef.value.validate((valid) => {
      if (valid) {
        console.log('开测');
      }
    });
  };

  const action = computed(() => {
    return props.data?.id ? (props.readonly ? '查看' : '编辑') : '添加';
  });
  const drawer = ref(false);
  watchEffect(() => {
    drawer.value = props.modelValue;
  });
  watch(
    () => props.data,
    (value) => {
      for (const key in form) {
        form[key] = value[key];
      }
      if (action.value === '添加') {
        form.type = '1';
        form.position = '0,0';
        form.character = '1';
      }
    }
  );

  const emit = defineEmits(['update:modelValue', 'success']);
  const formRef = ref();
  const onCancel = () => {
    emit('update:modelValue', false);
    formRef.value.resetFields();
  };
  const onSave = () => {
    formRef.value.validate((valid) => {
      if (valid) {
        onCancel();
        emit('success');
      }
    });
  };
</script>
