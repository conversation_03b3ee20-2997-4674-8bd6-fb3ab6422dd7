<template>
  <el-dropdown-menu>
    <el-dropdown-item v-for="item in dropdownlist" :key="item.id" @click="changeMenu(item)">
      {{ item.text }}
    </el-dropdown-item>
  </el-dropdown-menu>
</template>

<script setup lang="ts">
  import { useRouter } from 'vue-router';
  const router = useRouter();

  let dropdownlist = [
    {
      id: 1,
      text: '主页',
      pathname: 'IntelligentHome',
    },
    // {
    //   id: 2,
    //   text: '数据目录管理',
    //   pathname: 'IntelligentRegister',
    // },
    {
      id: 3,
      text: '数据脱敏管理',
      pathname: 'DesensitizationManage',
    },
    {
      id: 4,
      text: '数据集管理',
      pathname: 'DatasetManage',
    },
  ];

  let changeMenu = (e) => {
    router.push({ name: e.pathname });
  };
</script>
