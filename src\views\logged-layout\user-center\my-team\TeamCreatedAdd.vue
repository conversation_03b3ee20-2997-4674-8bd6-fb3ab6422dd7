<template>
  <el-dialog v-model="modelValue" title="添加成员" width="80%" destroy-on-close>
    <div class="mb-4 flex gap-4">
      <el-input
        v-model="searchInput"
        style="width: 200px"
        placeholder="请输入用户名或姓名查找"
        clearable
        @clear="fetchData"
        @keyup.enter="fetchData"
      />
      <el-button type="primary" @click="fetchData">搜索</el-button>
    </div>

    <el-table
      ref="tableRef"
      v-loading="loading"
      :data="tableData"
      style="width: 100%"
      class="c-table-header"
      highlight-current-row
      row-key="id"
      @selection-change="handleChange"
    >
      <el-table-column type="selection" width="55" reserve-selection />
      <el-table-column prop="userName" label="账号" />
      <el-table-column prop="name" label="用户名称" />
      <el-table-column prop="nickName" label="昵称" />
      <el-table-column prop="phone" label="电话号码" />
      <el-table-column prop="userType" label="类型" />
    </el-table>

    <div class="flex justify-center pt-2">
      <el-pagination
        background
        layout="total, prev, pager, next, jumper"
        :page-size="pagination.pageSize"
        :total="total"
        @current-change="handleCurrentChange"
      />
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="onCancel">取消</el-button>
        <el-button type="primary" @click="onSave">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { findUserByIdNotIn_02, newOrUpdateTeamUser } from '@/api';
  import { ElMessage, ElTable } from 'element-plus';

  const modelValue = defineModel<boolean>({ required: true });
  interface Props {
    teamId: number | string;
  }
  const props = defineProps<Props>();

  const tableRef = ref<InstanceType<typeof ElTable>>();
  const loading = ref(false);
  const tableData = ref<any[]>([]);
  const searchInput = ref('');
  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  const total = ref(0);
  const handleCurrentChange = (e) => {
    pagination.page = e;
    fetchData(e);
  };
  async function fetchData(pageNum = 1) {
    try {
      loading.value = true;
      const { data } = await findUserByIdNotIn_02({
        teamId: +props.teamId,
        pageNum,
        pageSize: pagination.pageSize,
        searchInput: searchInput.value,
      });
      total.value = data?.totalElement || 0;
      pagination.page = pageNum;
      tableData.value = data?.content || [];
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  const selectedRows = ref<any[]>();
  const handleChange = (selection: any[]) => {
    selectedRows.value = selection;
  };

  const emit = defineEmits<{ success: [] }>();
  const onCancel = () => {
    modelValue.value = false;
  };
  async function onSave() {
    try {
      if (!selectedRows.value?.length) {
        ElMessage({ type: 'warning', message: '请先选择要添加到团队的用户' });
        return;
      }
      loading.value = true;
      const promises = selectedRows.value.map((user) => {
        const data: TeamUserDTO = {
          teamUserId: {
            teamId: +props.teamId,
            userId: user.id,
          },
        };
        return newOrUpdateTeamUser(data);
      });

      await Promise.all(promises);
      onCancel();
      emit('success');
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  watch(modelValue, (value) => {
    if (value) {
      fetchData();
    }
  });
</script>
