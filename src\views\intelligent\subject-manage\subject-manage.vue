<template>
  <div class="flex h-full flex-col bg-w">
    <h2 class="flex items-center px-[28px] pt-4 text-xl font-bold">专题库管理</h2>

    <el-tabs v-model="activeName" class="tabs mt-3 flex h-0 flex-1 flex-col">
      <el-tab-pane label="数据库管理" name="数据库管理">
        <TabDatabase />
      </el-tab-pane>
      <el-tab-pane label="表管理" name="表管理">
        <TabTable />
      </el-tab-pane>
      <el-tab-pane label="字段管理" name="字段管理">
        <TabField />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
  import TabDatabase from './components/TabDatabase.vue';
  import TabTable from './components/TabTable.vue';
  import TabField from './components/TabField.vue';
  const activeName = ref('数据库管理');
</script>

<style scoped lang="scss">
  .tabs {
    --el-border-color-light: transparent;

    :deep(.el-tabs__content) {
      height: 0;
      flex: 1;
      background: #f0f2f5;
    }

    :deep(.el-tabs__header) {
      margin: 0;
      padding-left: 28px;
      border-bottom: none;
    }

    .el-tab-pane {
      height: 100%;
    }
  }
</style>
