# CBD-UI 项目构建说明

## 构建命令

项目使用以下命令进行构建:

### 开发环境
- `npm run dev` - 启动开发服务器
- `npm run devhost` - 启动开发服务器并允许局域网访问

### 构建部署
- `npm run build` - 构建生产环境代码并打包

### 代码质量
- `npm run lint` - 运行 ESLint 检查并修复代码
- `npm run format` - 使用 Prettier 格式化代码

### 清理缓存
- `npm run clean:cache` - 清理构建缓存
- `npm run clean:lib` - 清理依赖包

### API 生成
- `npm run genswagger` - 生成 Swagger API 文档
- `npm run genmap` - 生成 API 映射
- `npm run genapi` - 生成完整的 API 代码
