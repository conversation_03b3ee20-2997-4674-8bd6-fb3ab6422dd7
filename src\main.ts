import './styles/common.scss';
import './assets/font/iconfont.css';
import './assets/font/iconfont';
import './assets/font2/iconfont.css';
import 'dayjs/locale/zh-cn';
import App from './App.vue';
import { createApp } from 'vue';
import { setupRouter } from './router/index';
import { setupStore } from './store/index';
import './permission';
const app = createApp(App);

import * as ElementPlusIconsVue from '@element-plus/icons-vue';
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}

import svgicon from '@/components/svgicon.vue';

app.component('svgicon', svgicon);

import ElementPlus from 'element-plus';
import './styles/element.scss'; //自定义主题
import zhCn from 'element-plus/dist/locale/zh-cn.mjs';
app.use(ElementPlus, {
  locale: zhCn,
});

setupRouter(app);
setupStore(app);

app.directive('disable-button', {
  mounted(el, binding) {
    el.addEventListener('click', () => {
      if (!el.disabled) {
        const countdown = binding.value || 2; // 获取倒计时秒数，默认为 2 秒
        el.disabled = true; // 禁用按钮
        // 显示倒计时文本
        const originalText = el.innerText;
        el.innerText = `${countdown} 秒`;
        let remainingTime = countdown;
        const updateText = () => {
          if (remainingTime > 0) {
            el.innerText = ` ${remainingTime} 秒`;
            remainingTime--;
            setTimeout(updateText, 1000);
          } else {
            el.disabled = false; // 倒计时结束，恢复按钮为可用状态
            el.innerText = originalText;
          }
        };
        updateText();
      }
    });
  },
});

app.mount('#app');
