//专题库管理员
export default {
  path: '/thematic',
  component: () => import('@/views/logged-layout/index.vue'),
  children: [
    //元数据管理
    {
      path: 'metadata',
      component: () => import('@/views/thematic/metadata.vue'),
      children: [
        //数据源
        {
          path: 'data-source',
          name: 'ThematicDataSource',
          component: () => import('@/views/thematic/data-source/data-source.vue'),
        },
        {
          path: 'data-source-add/:id?',
          name: 'ThematicDataSourceAdd',
          component: () => import('@/views/thematic/data-source/data-source-add.vue'),
          props: true,
        },
        //数据表
        {
          path: 'data-table',
          name: 'ThematicDataTable',
          component: () => import('@/views/thematic/data-table/data-table.vue'),
        },
        //数据字段
        {
          path: 'data-field/:dbId/:tableId',
          name: 'ThematicDataField',
          component: () => import('@/views/thematic/data-field/data-field.vue'),
          props: true,
        },
        //元数据
        {
          path: 'data-origin',
          name: 'ThematicData',
          component: () => import('@/views/thematic/metadata-manage/metadata-manage.vue'),
        },
        {
          path: 'data-origin-detail/:id',
          name: 'ThematicDataDetail',
          component: () => import('@/views/thematic/metadata-manage/metadata-details.vue'),
          props: true,
        },
        //医学数据管理
        {
          path: 'medical-manage',
          name: 'ThematicMedicalManage',
          component: () => import('@/views/thematic/medical-manage/medical-manage.vue'),
        },
      ],
    },
    //关联数据源
    {
      path: 'medical-connect/:id',
      name: 'ThematicMedicalConnect',
      component: () => import('@/views/thematic/medical-connect/medical-connect.vue'),
      props: true,
    },
  ],
};
