<template>
  <div class="flex h-full flex-col px-5 pb-5">
    <p class="flex items-center py-3 text-tip">
      <el-icon><InfoFilled /></el-icon>
      <span class="ml-2">利用自定义的字符串替换数据中匹配到的关键词，达到脱敏的效果</span>
    </p>

    <div class="flex h-0 flex-1 flex-col rounded bg-w pt-5">
      <div class="flex justify-between px-10">
        <div>
          <el-button type="primary" @click="onAdd"> 添加 </el-button>
          <el-popconfirm width="180" title="确定删除选中数据？" @confirm="onDel">
            <template #reference>
              <el-button :disabled="checkList.length <= 0"> 删除 </el-button>
            </template>
          </el-popconfirm>
        </div>
      </div>

      <div class="mt-3 h-0 w-full flex-1 px-10">
        <el-table
          height="100%"
          :data="tableData"
          style="width: 100%"
          class="c-table-header"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column label="名称" min-width="100px">
            <template #default="{ row }">
              <el-button link type="primary" @click="onView(row)">
                {{ row.name }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="sensitive" label="敏感词" />
          <el-table-column prop="keyword" label="关键字" />
          <el-table-column prop="maskCharacter" label="替换字符串" />
          <el-table-column prop="level" label="级别" />
          <el-table-column prop="date" label="提交时间" width="170" />
          <el-table-column prop="flag" label="标志" />
          <el-table-column prop="type" label="类型" />
          <el-table-column label="操作" width="120">
            <template #default="{ row }">
              <el-button link type="primary" @click="onEdit(row)"> 编辑 </el-button>
              <el-button link type="primary" @click="onTest(row)"> 测试 </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="pagination-bottom">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :page-size="pagination.pageSize"
          :total="tableData.length"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>

  <Drawer v-model="showDrawer" :data="drawerData" :readonly="readonly" @success="onSuccess" />
</template>

<script setup>
  import Drawer from './Drawer.vue';

  //表格
  const tableData = ref([
    {
      id: '1',
      name: '身份证映射替换',
      sensitive: '身份',
      keyword: '脑疾病',
      maskCharacter: '45',
      level: '高',
      date: '2022-03-22 12:00:00',
      flag: '专用',
      type: '自定义',
    },
  ]);
  const checkList = ref([]);
  const handleSelectionChange = (val) => {
    checkList.value = val;
  };

  //页码
  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  const handleCurrentChange = (e) => {
    pagination.page = e;
  };
  const handleSizeChange = (e) => {
    pagination.pageSize = e;
  };

  const showDrawer = ref(false);
  const readonly = ref(false);
  let drawerData = ref({});
  const onView = (row) => {
    drawerData.value = row;
    readonly.value = true;
    showDrawer.value = true;
  };
  const onAdd = () => {
    drawerData.value = {};
    readonly.value = false;
    showDrawer.value = true;
  };
  const onDel = (row) => {};
  const onEdit = (row) => {
    drawerData.value = row;
    readonly.value = false;
    showDrawer.value = true;
  };
  const onTest = (row) => {};
  const onSuccess = () => {};
</script>

<style lang="scss" scoped>
  .tabs {
    :deep(.el-tabs__header) {
      margin: 0;
    }
  }
</style>
