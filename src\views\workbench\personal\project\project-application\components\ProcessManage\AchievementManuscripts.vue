<template>
  <div class="mb-5 mt-5 flex items-center pr-7">
    <div class="w-0 flex-1 bg-baf pr-5 pt-[18px]">
      <div class="flex items-center pl-[28px]">
        <h5 class="title">手稿</h5>
        <span class="ml-5 text-sm text-tip">上传日期：2022-07-12</span>
      </div>

      <el-descriptions direction="vertical" class="mt-4 pl-10" :column="4">
        <el-descriptions-item label="申请ID"> 4563223568 </el-descriptions-item>
        <el-descriptions-item label="标题"> 脑疾病研究 </el-descriptions-item>
        <el-descriptions-item label="第一作者"> 张三 </el-descriptions-item>
        <el-descriptions-item label="杂志"> 国家脑疾病健康杂志 </el-descriptions-item>
        <el-descriptions-item label="出版年份"> 2020 </el-descriptions-item>
        <el-descriptions-item label="DOI"> 10.1039/c7ra08324f </el-descriptions-item>
        <el-descriptions-item label="Pubmed ID" :span="2"> 1245254564523 </el-descriptions-item>
        <el-descriptions-item label="文件">
          <FileList :list="fileList" />
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <div class="ml-4">
      <el-link type="primary" class="mr-5" :underline="false" @click="onEdit"> 编辑 </el-link>
      <el-popconfirm title="确定删除？" @confirm="onDel">
        <template #reference>
          <el-link type="primary" :underline="false"> 删除 </el-link>
        </template>
      </el-popconfirm>
    </div>
  </div>
</template>

<script setup>
  /* 手稿 */
  import FileList from '@/components/FileList.vue';
  const props = defineProps({
    data: {
      type: Object,
    },
  });
  const fileList = ref([
    {
      name: '文件示例文件示例.pdf',
      url: '',
    },
    {
      name: '文件示例.pdf',
      url: '',
    },
  ]);
  const emit = defineEmits(['edit', 'del']);
  const onEdit = () => {
    emit('edit');
  };
  const onDel = () => {
    emit('del');
  };
</script>

<style lang="scss" scoped>
  .title {
    &::before {
      content: '';
      display: inline-block;
      width: 3px;
      height: 12px;
      background: $color-primary;
      margin-right: 8px;
      position: relative;
      bottom: -1px;
    }
  }

  :deep(.el-descriptions) {
    .el-descriptions__body {
      background-color: transparent;
    }
    .el-descriptions__label {
      color: $color-tip-text;
    }
  }
</style>
