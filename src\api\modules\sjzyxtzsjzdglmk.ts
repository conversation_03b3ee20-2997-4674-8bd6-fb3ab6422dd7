/*
 * @OriginalName: 数据资源系统中数据字段管理模块
 * @Description: 医学数据字段管理
 */
import { request } from '@/utils/request';

/**
 * 上传原始文档
 * @description 根据原始文档信息记录的主键id，上传原始文档。
 */
export function uploadDocument(
  orginalDocumentId: number,
  data: { documentFile: string },
  params?: { orginalDocumentId: number }
) {
  return request<ROriginalDocumentVO>(`/medicalData/uploadOriginalDocument/${orginalDocumentId}`, {
    method: 'post',
    data,
    params,
  });
}

/**
 * 新建或更新医学数据字段
 * @description 根据medicalFieldVO中的信息，在数据库的MedicalField表中若已存在ID相同的记录，则更新表中的相应记录；不存在则添加一条新记录。医学字段数值类型，包含“文本”、“整数”、“实数”、"日期时间“、”组合“、”单分类“、”多分类“
 */
export function newOrUpdateMedicalFieldVO(data: MedicalFieldDTO) {
  return request<RMedicalFieldVO>(`/medicalData/newOrUpdateMedicalFieldVO`, {
    method: 'post',
    data,
  });
}

/**
 * 给医学字段添加或更新一个备选项
 * @description 根据医学字段的主键id，为该医学数据字段添加或更新一个备选项。alternativeDTO的id为0或null时，执行添加操作
 */
export function newOrUpdateAlternative(mfId: number, data: AlternativeDTO, params?: { mfId: number }) {
  return request<RAlternativeVO>(`/medicalData/newOrUpdateAlternative/${mfId}`, {
    method: 'post',
    data,
    params,
  });
}

/**
 * 查询医学数据字段
 * @description 按动态条件，获取满足相应条件的医学数据字段的基本信息。如果有主键，按主键精确查找，若无主键，则其它条件按与操作进行模糊查询。所有条件均为空时，返回全部记录。
 */
export function findMedicalFieldsByFileInforIdAndDynamicConditions(
  fileInforId: number,
  data: MedicalFieldBriefCriteria,
  params?: { fileInforId: number }
) {
  return request<RVOPage>(`/medicalData/findMedicalFieldsByFileInforIdAndDynamicConditions/${fileInforId}`, {
    method: 'post',
    data,
    params,
  });
}

/**
 * 查询医学数据字段
 * @description 按动态条件，获取满足相应条件的医学数据字段的基本信息。如果有主键，按主键精确查找，若无主键，则其它条件按与操作进行模糊查询。所有条件均为空时，返回全部记录。
 */
export function findMedicalFieldVOByCriteria(data: MedicalFieldCriteria) {
  return request<REntityVOPage>(`/medicalData/findMedicalFieldVOByCriteria`, {
    method: 'post',
    data,
  });
}

/**
 * 下载原始文档
 * @description 根据原始文档信息记录的主键id，数据库中下载原始文档。
 */
export function downloadDocument(orginalDocumentId: number, params?: { orginalDocumentId: number }) {
  return request(`/medicalData/downloadDocument/${orginalDocumentId}`, {
    method: 'post',
    params,
  });
}

/**
 * 下载脱敏的医学字段中的数据
 * @description 按医学字段记录的ID，数据脱敏后，把数据压缩成Zip类型的文件后，下载到客户端。
 */
export function downloadColumn(data: Array<number>) {
  return request(`/medicalData/downloadColumn`, {
    method: 'post',
    data,
  });
}

/**
 * 给医学字段添加或更新统计数据
 * @description ”文本型数据“，给该类型医学数据字段添加统计数据。
 */
export function addStatisticTxtMedicalFieldId(
  medicalFieldId: number,
  data: StatisticTxtDTO,
  params?: { medicalFieldId: number }
) {
  return request<R>(`/medicalData/addStatisticTxtMedicalFieldId/${medicalFieldId}`, {
    method: 'post',
    data,
    params,
  });
}

/**
 * 给医学字段添加或更新统计数据
 * @description ”整数型数据“，给该类型医学数据字段添加统计数据。
 */
export function addStatisticLongIntMedicalFieldId(
  medicalFieldId: number,
  data: StatisticLongIntDTO,
  params?: { medicalFieldId: number }
) {
  return request<R>(`/medicalData/addStatisticLongIntMedicalFieldId/${medicalFieldId}`, {
    method: 'post',
    data,
    params,
  });
}

/**
 * 给医学字段添加或更新统计数据
 * @description ”实数型数据“，给该类型医学数据字段添加统计数据。
 */
export function addStatisticFloatMedicalFieldId(
  medicalFieldId: number,
  data: StatisticFloatDTO,
  params?: { medicalFieldId: number }
) {
  return request<R>(`/medicalData/addStatisticFloatMedicalFieldId/${medicalFieldId}`, {
    method: 'post',
    data,
    params,
  });
}

/**
 * 给医学字段添加或更新统计数据
 * @description ”日期时间型数据“，给该类型医学数据字段添加统计数据。
 */
export function addStatisticDateTimeMedicalFieldId(
  medicalFieldId: number,
  data: StatisticDateTimeVO,
  params?: { medicalFieldId: number }
) {
  return request<R>(`/medicalData/addStatisticDateTimeMedicalFieldId/${medicalFieldId}`, {
    method: 'post',
    data,
    params,
  });
}

/**
 * 给医学字段添加或更新统计数据
 * @description ”单分类或多分类型数据“，给该类型医学数据字段添加统计数据。
 */
export function addStatisticCategoricalMedicalFieldId(
  medicalFieldId: number,
  data: StatisticCategoricalVO,
  params?: { medicalFieldId: number }
) {
  return request<R>(`/medicalData/addStatisticCategoricalMedicalFieldId/${medicalFieldId}`, {
    method: 'post',
    data,
    params,
  });
}

/**
 * 给医学字段添加一个出版文献
 * @description 根据医学字段的主键id，为该医学字段添加已出版的文献。publicationVO表示出版文献的信息。
 */
export function addPublication(mfId: number, data: PublicationVO, params?: { mfId: number }) {
  return request<RPublicationVO>(`/medicalData/addPublication/${mfId}`, {
    method: 'post',
    data,
    params,
  });
}

/**
 * 给医学字段添加或更新一个原始文档记录信息
 * @description 根据医学字段的主键id，为该医学字段添加原始文档信息。originalDocumentVO表示原始文档的信息。
 */
export function addOriginalDocument(mfId: number, data: OriginalDocumentVO, params?: { mfId: number }) {
  return request<ROriginalDocumentVO>(`/medicalData/addOriginalDocument/${mfId}`, {
    method: 'post',
    data,
    params,
  });
}

/**
 * 访问医学数据字段
 * @description 获取医学数据字段的基本信息
 */
export function getMedicalField(id: number, params?: { id: number }) {
  return request<RMedicalFieldVO>(`/medicalData/${id}`, {
    method: 'get',
    params,
  });
}

/**
 * 同步医学字段的记录数量
 * @description 用该医学字段的原始数据记录数量同步记录数量的值。
 */
export function synchronizeRecordCount(medicalFieldId: number, params?: { medicalFieldId: number }) {
  return request<RLong>(`/medicalData/synchronizeRecordCount/${medicalFieldId}`, {
    method: 'get',
    params,
  });
}

/**
 * 移除原始文档
 * @description 根据原始文档信息记录的主键id，从信息中移除原始文档。
 */
export function removeDocument(orginalDocumentId: number, params?: { orginalDocumentId: number }) {
  return request<ROriginalDocumentVO>(`/medicalData/removeDocument/${orginalDocumentId}`, {
    method: 'get',
    params,
  });
}

/**
 * 获取医学字段的概率分布数据
 * @description 以Id号，获取医学字段的显示概率分布的直方图数据。
 */
export function getProbabilityDistribution(medicalFieldId: number, params?: { medicalFieldId: number }) {
  return request<RListHistogramVO>(`/medicalData/getProbabilityDistribution/${medicalFieldId}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找一个医学字段的统计数据
 * @description 根据医学字段的主键id，查找统计数据记录。
 */
export function findStatisticByMedicalFieldId(medicalFieldId: number, params?: { medicalFieldId: number }) {
  return request<R>(`/medicalData/findStatisticByMedicalFieldId/${medicalFieldId}`, {
    method: 'get',
    params,
  });
}

/**
 * 检索一个医学字段的出版文献
 * @description 根据医学字段的主键id，返回医学数据字段的出版文献。
 */
export function findPublicationByMedicalFieldId(mfId: number, params?: { mfId: number }) {
  return request<RListPublicationVO>(`/medicalData/findPublicationByMedicalFieldId/${mfId}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找平台的统计数据
 * @description 查找整个平台的数据统计信息。
 */
export function findPlatStatistic() {
  return request<RPlatStatisticVO>(`/medicalData/findPlatStatistic`, {
    method: 'get',
  });
}

/**
 * 检索原始文档
 * @description 根据医学字段的主键id，返回医学数据字段的原始文档。
 */
export function findOriginalDocumentByMedicalFieldId(mfId: number, params?: { mfId: number }) {
  return request<RListOriginalDocumentVO>(`/medicalData/findOriginalDocumentByMedicalFieldId/${mfId}`, {
    method: 'get',
    params,
  });
}

/**
 * 查询医学数据字段的数据源
 * @description 按数据字段的ID，获取医学数据字段内所有原始数据字段所属的数据库、数据表的基本信息
 */
export function findDataSourceById(mdfId: number, params?: { mdfId: number }) {
  return request<RMedicalFieldDataSourceVO>(`/medicalData/findMedicalFieldVOByValueType/${mdfId}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找一个医学字段所属数据集文件
 * @description 根据医学字段的主键id，查找该字段所属的医学数据集文件。
 */
export function findFileInforByMedicalFieldId(medicalFieldId: number, params?: { medicalFieldId: number }) {
  return request<R>(`/medicalData/findFileInforByMedicalFieldId/${medicalFieldId}`, {
    method: 'get',
    params,
  });
}

/**
 * 访问医学数据字段所属类别
 * @description 根据医学字段的ID，返回医学数据字段所属的类别
 */
export function findCatalogueVoBymdfId(mdfId: number, params?: { mdfId: number }) {
  return request<RListCatalogueVO>(`/medicalData/findCatalogueVoBymdfId/${mdfId}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找关联的数据源中的字段
 * @description 根据MedicalField主键ID,查找和医学字段关联的原始数据字段管理信息
 */
export function findCBDDefFieldByMfId(mfId: number, params?: { mfId: number }) {
  return request<RListCBDDefFieldVO>(`/medicalData/findCBDDefFieldByMfId/${mfId}`, {
    method: 'get',
    params,
  });
}

/**
 * 检索一个医学字段的全部备选项
 * @description 根据医学字段的主键id，返回医学数据字段的全部备选项。
 */
export function findAlternativeByMedicalFieldId(mfId: number, params?: { mfId: number }) {
  return request<RListAlternativeVO>(`/medicalData/findAlternativeByMedicalFieldId/${mfId}`, {
    method: 'get',
    params,
  });
}

/**
 * 取消关联数据源中的字段
 * @description 根据MedicalField主键ID,和原始数据字段管理信息的ID，取消两者间的关联。
 */
export function disassociateCBDField(
  mfId: number,
  cfdFieldId: Array<number>,
  params?: { mfId: number; cfdFieldId: Array<number> }
) {
  return request<R>(`/medicalData/disassociateCBDField/${mfId}/${cfdFieldId}`, {
    method: 'get',
    params,
  });
}

/**
 * 访问医学数据字段
 * @description 获取医学数据字段的信息
 */
export function getMedicalFieldDetail(id: number, params?: { id: number }) {
  return request<RMedicalFieldDetailVO>(`/medicalData/detail/${id}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除一个医学字段的统计信息
 * @description 根据统计信息记录的主键id，删除该医学字段的统计信息记录。
 */
export function deleteStatistic(statisticId: number, params?: { statisticId: number }) {
  return request<R>(`/medicalData/deleteStatistic/${statisticId}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除医学字段的一个出版文献
 * @description 根据医学字段的主键id，删除pubId代表的一个出版文献。
 */
export function deletePublication(mfId: number, pubId: number, params?: { mfId: number; pubId: number }) {
  return request<R>(`/medicalData/deletePublication/${mfId}/${pubId}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除医学字段的一个原始文档
 * @description 根据医学字段的主键id，删除ordId代表的一个原始文档
 */
export function deleteOriginalDocument(mfId: number, ordId: number, params?: { mfId: number; ordId: number }) {
  return request<R>(`/medicalData/deleteOriginalDocument/${mfId}/${ordId}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除医学字段
 * @description 根据MedicalField主键ID,删除一条记录。
 */
export function deleteMedicalFieldById(mfId: number, params?: { mfId: number }) {
  return request<R>(`/medicalData/deleteMedicalFieldById/${mfId}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除医学字段的一个备选项
 * @description 根据医学字段的主键id，删除altId代表的一个备选项。
 */
export function deleteAlternative(mfId: number, altId: number, params?: { mfId: number; altId: number }) {
  return request<R>(`/medicalData/deleteAlternative/${mfId}/${altId}`, {
    method: 'get',
    params,
  });
}

/**
 * 关联数据源中的字段
 * @description 根据MedicalField主键ID,和原始数据字段管理信息的ID，建立两者间的关联。
 */
export function associateCBDField(
  mfId: number,
  cfdFieldId: Array<number>,
  params?: { mfId: number; cfdFieldId: Array<number> }
) {
  return request<RListCBDDefFieldVO>(`/medicalData/associateCBDField/${mfId}/${cfdFieldId}`, {
    method: 'get',
    params,
  });
}
