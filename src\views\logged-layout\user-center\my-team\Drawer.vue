<template>
  <el-drawer v-model="drawer" class="relative" @close="onCancel">
    <template #header>
      <h4 class="text-m">{{ action }}团队</h4>
    </template>

    <el-form ref="formRef" v-loading="formLoading" :model="form" label-width="80px" :rules="rules">
      <el-form-item label="团队名称" prop="name">
        <el-input v-model="form.name" :disabled="readonly" placeholder="请输入团队名称" />
      </el-form-item>
      <el-form-item label="电话号码" prop="phone">
        <el-input v-model="form.phone" :disabled="readonly" placeholder="请输入电话号码" />
      </el-form-item>
      <el-form-item label="邮箱" prop="email">
        <el-input v-model="form.email" :disabled="readonly" placeholder="请输入邮箱" />
      </el-form-item>
      <el-form-item label="通讯地址" prop="address">
        <el-input v-model="form.address" :disabled="readonly" placeholder="请输入通讯地址" />
      </el-form-item>
      <el-form-item label="团队说明" prop="description">
        <el-input
          v-model="form.description"
          :disabled="readonly"
          placeholder="请输入"
          type="textarea"
          :rows="4"
          show-word-limit
        />
      </el-form-item>
      <el-form-item label="备注" prop="note">
        <el-input v-model="form.note" :disabled="readonly" placeholder="请输入备注" type="textarea" :rows="4" />
      </el-form-item>
    </el-form>

    <template v-if="!readonly" #footer>
      <el-button @click="onCancel"> 取消 </el-button>
      <el-button type="primary" :loading="saveLoading" @click="onSave"> 保存 </el-button>
    </template>
  </el-drawer>
</template>

<script setup>
  import { newOrUpdateTeam, findTeamById } from '@/api/index';
  import { ElMessage } from 'element-plus';

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
      required: true,
    },
    id: {
      type: [String, Number],
    },
    readonly: {
      type: Boolean,
      default: false,
    },
  });
  const drawer = ref(false);
  let form = reactive({
    id: 0,
    name: '',
    phone: '',
    email: '',
    address: '',
    description: '',
    note: '',
  });
  watchEffect(() => {
    drawer.value = props.modelValue;
  });
  watch(
    () => props.modelValue,
    (value) => {
      if (value && props.id) {
        fetchData();
      }
    }
  );
  const action = computed(() => {
    return props.id ? (props.readonly ? '查看' : '编辑') : '新增';
  });

  const rules = reactive({
    name: [{ required: true, message: '请输入团队名称' }],
    phone: [{ required: true, message: '请输入电话号码' }],
    address: [{ required: true, message: '请输入通讯地址' }],
    email: [
      {
        required: true,
        type: 'email',
        message: '请输入有效的邮箱地址',
      },
    ],
  });

  const emit = defineEmits(['update:modelValue', 'success']);
  const formRef = ref();
  const saveLoading = ref(false);
  const onCancel = () => {
    emit('update:modelValue', false);
    formRef.value.resetFields();
  };
  const onSave = () => {
    formRef.value.validate(async (valid) => {
      if (valid) {
        try {
          saveLoading.value = true;
          form.id = props.id;
          await newOrUpdateTeam(form);
          ElMessage({ type: 'success', message: '保存成功' });
          onCancel();
          emit('success');
        } catch (error) {
          console.log(error);
        } finally {
          saveLoading.value = false;
        }
      }
    });
  };

  const formLoading = ref(false);
  async function fetchData() {
    try {
      formLoading.value = true;
      const { data } = await findTeamById(props.id);
      Object.assign(form, data);
    } catch (error) {
      console.log(error);
    } finally {
      formLoading.value = false;
    }
  }
</script>
