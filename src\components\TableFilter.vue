<template>
  <el-popover placement="bottom-end" :width="200" trigger="click">
    <template #reference>
      <el-button class="ml-3"> 重置 </el-button>
    </template>
    <div class="flex h-[285px] flex-col">
      <div class="flex h-[38px] items-center border-b border-border-light">
        <div class="flex-1 text-center">
          <a class="text-regular" @click="onCheckAll">全选</a>
        </div>
        <div class="flex-1 text-center">
          <a class="text-p" @click="onReset">重置</a>
        </div>
      </div>
      <el-scrollbar class="h-0 flex-1">
        <el-checkbox-group v-model="checkList" class="px-5">
          <el-checkbox v-for="(item, index) in options" :key="index" :label="item" />
        </el-checkbox-group>
      </el-scrollbar>
    </div>
  </el-popover>
</template>

<script setup>
  /* 表格筛选组件 */
  const props = defineProps({
    modelValue: {
      type: Array,
      required: true,
    },
    options: {
      type: Array,
    },
  });

  const checkList = ref([]);
  watch(
    () => props.modelValue,
    (value) => {
      checkList.value = value;
    },
    {
      immediate: true,
      deep: true,
    }
  );
  const emit = defineEmits(['update:modelValue']);
  watch(
    checkList,
    (value) => {
      emit('update:modelValue', value);
    },
    {
      deep: true,
    }
  );

  const onCheckAll = () => {
    checkList.value = props.options;
  };
  const onReset = () => {
    checkList.value = [];
  };
</script>

<style lang="scss">
  .el-popover.el-popper {
    padding: 0;
  }
</style>
