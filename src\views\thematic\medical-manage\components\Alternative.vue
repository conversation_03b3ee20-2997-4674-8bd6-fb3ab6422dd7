<template>
  <el-dialog v-model="show" title="备选项" width="80%" class="dialog-height" @close="onCancel">
    <div class="mb-4">
      <el-button type="primary" @click="onAdd"> 新增 </el-button>
      <el-button :disabled="checkList.length <= 0" :loading="batchLoading" @click="onBatchDel"> 批量删除 </el-button>
    </div>

    <el-table :data="tableData" style="width: 100%" class="c-table-header" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" />
      <el-table-column prop="altValue" label="备选值" min-width="100px" />
      <el-table-column prop="statisticValue" label="统计值" />
      <el-table-column label="操作" width="110">
        <template #default="{ row }">
          <el-button link type="primary" @click="onEdit(row)"> 编辑 </el-button>
          <el-popconfirm title="确定删除此项？" @confirm="onDel(row)">
            <template #reference>
              <el-button link type="primary"> 删除 </el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>

    <!-- <template #footer>
      <div class="dialog-footer">
        <el-button @click="onCancel">取消</el-button>
        <el-button type="primary" @click="onSave">确定</el-button>
      </div>
    </template> -->
  </el-dialog>

  <el-dialog v-model="showAdd" title="操作备选项" width="500px" @close="onAddClose">
    <el-form ref="formRef" :model="addForm" :rules="rules" label-width="80px">
      <el-form-item label="备选值" prop="altValue">
        <el-input v-model="addForm.altValue" placeholder="请输入备选值" />
      </el-form-item>
      <el-form-item label="统计值" prop="statisticValue">
        <el-input-number v-model="addForm.statisticValue" :min="0" />
      </el-form-item>
    </el-form>

    <template #footer>
      <span>
        <el-button @click="onAddClose">取消</el-button>
        <el-button type="primary" :loading="addLoading" @click="onAddConfirm">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
  import { newOrUpdateAlternative, findAlternativeByMedicalFieldId, deleteAlternative } from '@/api/index';
  import { ElMessageBox, ElMessage } from 'element-plus';
  import { nextTick } from 'vue';

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
    id: {
      type: [String, Number],
    },
  });
  const show = ref(false);
  watchEffect(() => {
    show.value = props.modelValue;
    if (show.value) {
      fetchData();
    }
  });

  const tableData = ref([
    // {
    //   name: '我的团队1',
    //   org: 'XXXXXXX大学',
    //   leadResearcher: '<EMAIL>',
    //   cooperator: [{ name: '<EMAIL>' }, { name: '<EMAIL>' }, { name: '<EMAIL>' }],
    //   deputy: 0,
    //   orgName: 'XXXXXXXXXX机构',
    //   orgContact: '<EMAIL>',
    // },
  ]);
  const checkList = ref([]);
  const handleSelectionChange = (val) => {
    checkList.value = val;
  };
  const batchLoading = ref(false);
  const onBatchDel = () => {
    ElMessageBox.confirm('确定删除所有选中项？', '操作提示', { type: 'warning' })
      .then(async () => {
        try {
          batchLoading.value = true;
          await Promise.all(checkList.value.map((row) => deleteAlternative(props.id, row.id)));
          ElMessage({ type: 'success', message: '删除成功' });
          fetchData();
        } catch (error) {
          console.log(error);
        } finally {
          batchLoading.value = false;
        }
      })
      .catch(() => {});
  };
  const onDel = async (row) => {
    try {
      await deleteAlternative(props.id, row.id);
      ElMessage({ type: 'success', message: '删除成功' });
      fetchData();
    } catch (error) {
      console.log(error);
    }
  };

  const formRef = ref();
  const addForm = reactive({
    id: 0,
    altValue: '',
    statisticValue: 0,
  });
  const rules = ref({
    altValue: [{ required: true, message: '不能为空' }],
  });
  const showAdd = ref(false);
  const onAdd = () => {
    addForm.id = 0;
    showAdd.value = true;
  };
  const addLoading = ref(false);
  const onAddConfirm = () => {
    formRef.value.validate(async (valid) => {
      try {
        if (valid) {
          addLoading.value = true;
          await newOrUpdateAlternative(props.id, addForm);
          showAdd.value = false;
          ElMessage({ type: 'success', message: '操作成功' });
          fetchData();
        }
      } catch (error) {
        console.log(error);
      } finally {
        addLoading.value = false;
      }
    });
  };
  const onAddClose = () => {
    showAdd.value = false;
    formRef.value.resetFields();
  };

  const onEdit = (row) => {
    showAdd.value = true;
    nextTick(() => {
      Object.assign(addForm, row);
    });
  };

  const emit = defineEmits(['update:modelValue', 'success']);
  const onCancel = () => {
    emit('update:modelValue', false);
  };

  async function fetchData() {
    try {
      const { data } = await findAlternativeByMedicalFieldId(props.id);
      tableData.value = data;
    } catch (error) {
      return Promise.reject(error);
    }
  }
</script>

<style lang="scss">
  .dialog-height {
    height: 75vh;
    overflow: auto;
    display: flex;
    flex-direction: column;

    .el-dialog__body {
      height: 0;
      flex: 1;
      display: flex;
      flex-direction: column;
    }
  }
</style>

<style lang="scss" scoped>
  .c-table-header {
    flex: 1;
    height: 0;
  }
</style>
