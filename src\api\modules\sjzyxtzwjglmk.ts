/*
 * @OriginalName: 数据资源系统中文件管理模块
 * @Description: 文件信息的创建、更新和查询、文件上传
 */
import { request } from '@/utils/request';

/**
 * 新建或更新文档记录，并上传文档
 * @description 先新建或更新文档记录，然后覆盖式上传文档。此接口的操作将覆盖原文档。
 */
export function uploadFile_12(data: { entityDTO: CommonFileDTO; file: string }) {
  return request<RCommonFileVO>(`/CommonFile/uploadFile`, {
    method: 'post',
    data,
  });
}

/**
 * 根据文件记录id，上传文档
 * @description 为文档记录，覆盖式上传文档。此接口的操作将覆盖原文档。
 */
export function uploadFile_13(fileId: number, data: { file: string }, params?: { fileId: number }) {
  return request<RCommonFileVO>(`/CommonFile/uploadFile/${fileId}`, {
    method: 'post',
    data,
    params,
  });
}

/**
 * 新建或更新数据表中的记录
 * @description 按tdto的信息，新建或更新数据表中的记录。
 */
export function newOrUpdateEntity_14(data: CommonFileDTO) {
  return request<RCommonFileVO>(`/CommonFile/newOrUpdateEntity`, {
    method: 'post',
    data,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条或多条数据记录。
 */
export function findEntityById_14(data: Array<number>) {
  return request<RListCommonFileVO>(`/CommonFile/findEntityByIdCollection`, {
    method: 'post',
    data,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一或多条记录。
 */
export function deleteEntityById_14(data: Array<number>) {
  return request<R>(`/CommonFile/deleteEntityByIdCollection`, {
    method: 'post',
    data,
  });
}

/**
 * 获取文档
 * @description 按文档记录的ID，以InputStream类型从系统中获取文件。
 */
export function getFileStream_5(commonFileId: number, params?: { commonFileId: number }) {
  return request(`/CommonFile/getFileStream/${commonFileId}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条或多条数据记录。
 */
export function findEntityById_43(idArray: Array<object>, params?: { idArray: Array<object> }) {
  return request<RListCommonFileVO>(`/CommonFile/findEntityByIdArray/${idArray}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条数据记录。
 */
export function findEntityById_44(id: number, params?: { id: number }) {
  return request<RCommonFileVO>(`/CommonFile/findEntityById/${id}`, {
    method: 'get',
    params,
  });
}

/**
 * 显示数据表中的全部记录
 * @description 分页显示数据表中的全部记录
 */
export function findAll_14(pageNum: number, pageSize: number, params?: { pageNum: number; pageSize: number }) {
  return request<R>(`/CommonFile/findAll/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一或多条记录。
 */
export function deleteEntityById_43(idArray: Array<object>, params?: { idArray: Array<object> }) {
  return request<R>(`/CommonFile/deleteEntityByIdArray/${idArray}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一条记录。
 */
export function deleteEntityById_44(id: number, params?: { id: number }) {
  return request<R>(`/CommonFile/deleteEntityById/${id}`, {
    method: 'get',
    params,
  });
}
