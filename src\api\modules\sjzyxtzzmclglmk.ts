/*
 * @OriginalName: 数据资源系统中证明材料管理模块
 * @Description: 用户注册证明材料的创建、更新和查询
 */
import { request } from '@/utils/request';

/**
 * 上传用户注册附属材料
 * @description 上传用户注册附属证明材料。此操作上传的材料仅仅存入系统，并未指定是哪个注册申请的的材料
 */
export function uploadAttachmentMaterial(data: {
  attachmentMaterialDTO: AttatchmentMaterialDTO;
  attachmentFile: string;
}) {
  return request<RAttatchmentMaterialVO>(`/attachmentMaterial/uploadAttachmentMaterial`, {
    method: 'post',
    data,
  });
}

/**
 * 上传用户注册附属文件
 * @description 上传用户注册附属证明文件。根据证明材料记录的ID，上传证明文件。此操作上传的材料仅仅存入系统，并未指定是哪个注册申请的的材料
 */
export function uploadAttachmentMaterial_1(
  attachmentMaterialId: number,
  data: { attachmentFile: string },
  params?: { attachmentMaterialId: number }
) {
  return request<RAttatchmentMaterialVO>(`/attachmentMaterial/uploadAttachmentMaterialId/${attachmentMaterialId}`, {
    method: 'post',
    data,
    params,
  });
}

/**
 * 新建或更新数据表中的记录
 * @description 按tdto的信息，新建或更新数据表中的记录。
 */
export function newOrUpdateEntity_7(data: AttatchmentMaterialDTO) {
  return request<RAttatchmentMaterialVO>(`/attachmentMaterial/newOrUpdateEntity`, {
    method: 'post',
    data,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条或多条数据记录。
 */
export function findEntityById_7(data: Array<number>) {
  return request<RListAttatchmentMaterialVO>(`/attachmentMaterial/findEntityByIdCollection`, {
    method: 'post',
    data,
  });
}

/**
 * 查找注册记录的附属材料
 * @description 根据注册记录主键ID,查找用户的注册时提供的附件材料。
 */
export function findAttachmentMaterialByEnrollmentId(
  enrollmentId: number,
  pageNum: number,
  pageSize: number,
  params?: { enrollmentId: number; pageNum: number; pageSize: number }
) {
  return request<RVOPageAttachmentMaterialAttatchmentMaterialVO>(
    `/attachmentMaterial/findAttachmentMaterialByEnrollmentId/${enrollmentId}/${pageNum}/${pageSize}`,
    {
      method: 'post',
      params,
    }
  );
}

/**
 * 查找注册记录的附属材料
 * @description 按动态条件，获取满足相应条件的附属材料的基本信息。各条件按与操作进行模糊查询。所有条件均为空时，返回全部记录。
 */
export function findAttachmentMaterialByCriteria(data: AttachmentMaterialCriteria) {
  return request<RVOPage>(`/attachmentMaterial/findAttachmentMaterialByCriteria`, {
    method: 'post',
    data,
  });
}

/**
 * 下载附属文件
 * @description 按附属材料记录A的ID，从数据库中下载附属文件。下载的文件名称限定用英文字符。
 */
export function downloadAttachmentMaterialFile(
  attachmentMaterialId: number,
  params?: { attachmentMaterialId: number }
) {
  return request(`/attachmentMaterial/downloadAttachmentMaterialFile/${attachmentMaterialId}`, {
    method: 'post',
    params,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一或多条记录。
 */
export function deleteEntityById_7(data: Array<number>) {
  return request<R>(`/attachmentMaterial/deleteEntityByIdCollection`, {
    method: 'post',
    data,
  });
}

/**
 * 为注册申请指定附件证明材料
 * @description 根据注册记录主键ID,指定或添加附件材料。
 */
export function setAttachmentMaterialForEnrollment(
  enrollmentId: number,
  attachmentMaterialId: Array<number>,
  params?: { enrollmentId: number; attachmentMaterialId: Array<number> }
) {
  return request<RListAttatchmentMaterialVO>(
    `/attachmentMaterial/setAttachmentMaterialForEnrollment/${enrollmentId}/${attachmentMaterialId}`,
    {
      method: 'get',
      params,
    }
  );
}

/**
 * 移除附属文件
 * @description 按AttachmentMaterial的ID，移除附属证明文件。
 */
export function removeAttachmentMaterial(attachmentMaterialId: number, params?: { attachmentMaterialId: number }) {
  return request<RAttatchmentMaterialVO>(`/attachmentMaterial/removeAttachmentMaterial/${attachmentMaterialId}`, {
    method: 'get',
    params,
  });
}

/**
 * 获取附属文件
 * @description 按附属材料记录A的ID，以字节类型从数据库获取附属文件。
 */
export function getAttachmentMaterialfile(attachmentMaterialId: number, params?: { attachmentMaterialId: number }) {
  return request<RByte>(`/attachmentMaterial/getAttachmentMaterialfile/${attachmentMaterialId}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条或多条数据记录。
 */
export function findEntityById_29(idArray: Array<object>, params?: { idArray: Array<object> }) {
  return request<RListAttatchmentMaterialVO>(`/attachmentMaterial/findEntityByIdArray/${idArray}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条数据记录。
 */
export function findEntityById_30(id: number, params?: { id: number }) {
  return request<RAttatchmentMaterialVO>(`/attachmentMaterial/findEntityById/${id}`, {
    method: 'get',
    params,
  });
}

/**
 * 显示数据表中的全部记录
 * @description 分页显示数据表中的全部记录
 */
export function findAll_7(pageNum: number, pageSize: number, params?: { pageNum: number; pageSize: number }) {
  return request<R>(`/attachmentMaterial/findAll/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一或多条记录。
 */
export function deleteEntityById_29(idArray: Array<object>, params?: { idArray: Array<object> }) {
  return request<R>(`/attachmentMaterial/deleteEntityByIdArray/${idArray}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一条记录。
 */
export function deleteEntityById_30(id: number, params?: { id: number }) {
  return request<R>(`/attachmentMaterial/deleteEntityById/${id}`, {
    method: 'get',
    params,
  });
}
