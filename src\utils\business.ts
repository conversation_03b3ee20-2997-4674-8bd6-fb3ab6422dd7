import type { RouterItem } from 'types/router';
import { background } from '@/router/background';
import personal from '@/router/workbench/personal';
import thematic from '@/router/thematic-library';
import intelligent from '@/router/intelligent';
import business from '@/router/business';

// 定义角色与路由的映射关系
const roleRouteMap: { [key: string]: RouterItem[] } = {
  ADMIN: [background],
  CUSTOMER: [personal],
  RESOURCE_OPERATOR: [intelligent],
  USER_OPERATOR: [business],
  THEME_OPERATOR: [thematic],
};

export function getRoutesByRoleCode(roleCode: string[]) {
  // 用于存储合并后的路由
  const routeSet = new Set<RouterItem>();
  // 遍历角色代码数组
  roleCode.forEach((code) => {
    // 根据角色代码获取对应的路由
    const roleRoutes = roleRouteMap[code] || roleRouteMap.default;
    // 将路由添加到 Set 中去重
    roleRoutes.forEach((route) => routeSet.add(route));
  });
  // 将 Set 转换为数组并返回
  return Array.from(routeSet);
}
