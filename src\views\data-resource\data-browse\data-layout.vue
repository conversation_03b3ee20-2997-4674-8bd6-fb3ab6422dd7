<template>
  <div class="flex h-full flex-col bg-baf">
    <div class="flex h-0 flex-1">
      <AsideBar :order-id="props.orderId" />

      <el-scrollbar height="100%" class="w-0 flex-1 bg-w text-sm">
        <Rank v-if="componentId === 'rank' && !props.orderId" />
        <DataType v-if="componentId === 'type'" :id="catalogId" @change-type="changeType" @goto-filed="gotoFiled" />
        <DataField v-if="componentId === 'field'" :id="catalogId" @goto-type="changeType" />
      </el-scrollbar>
    </div>

    <div v-if="isSelectMode" class="box-shadow flex h-[68px] items-center justify-center bg-w">
      <el-button type="primary" :loading="saveLoading" @click="onSave"> 保存 </el-button>
      <el-button @click="onQuit"> 退出 </el-button>
      <span class="ml-7 text-sm">
        订单编号：{{ code }}
        <!-- 总计{{ statistic.dataSetCount || 0 }}个数据集，{{
          statistic.medicalFieldCount || 0
        }}个字段（文本类型{{ statistic.txtFieldCount || 0 }}个，整数类型{{
          statistic.longFieldCount || 0
        }}个，实数类型{{ statistic.floatFieldCount || 0 }}个，日期类型{{
          statistic.dateTimeFieldCount || 0
        }}个，分类类别{{ statistic.categoricalFieldCount || 0 }}个） -->
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
  import Rank from './rank.vue';
  import DataType from './data-type.vue';
  import DataField from './data-field.vue';
  import AsideBar from './components/AsideBar.vue';
  import { useRouter } from 'vue-router';
  import { useDataBrowse } from '@/store/data-browse';
  import { findOrderItemVOByOrderId, newOrUpdateOrderItem } from '@/api';
  import { ElMessage } from 'element-plus';

  interface Props {
    orderId?: string;
    code?: string;
  }
  const props = defineProps<Props>();
  const router = useRouter();
  const store = useDataBrowse();
  const loading = ref(false);
  const saveLoading = ref(false);
  const componentId = ref('rank');
  const catalogId = ref('');
  const isSelectMode = computed(() => {
    return store.dataBrowse.isSelectMode;
  });
  const id = computed(() => {
    return store.dataBrowse.id;
  });

  const changeType = (id) => {
    catalogId.value = id;
    componentId.value = 'type';
    store.setData({ id });
  };

  const gotoFiled = (id) => {
    catalogId.value = id;
    componentId.value = 'field';
  };

  const onSave = async () => {
    if (!store.dataBrowse.fileds.length) {
      ElMessage({ type: 'warning', message: '请先展开左侧目录，选择数据' });
      return;
    }

    try {
      saveLoading.value = true;
      const orderId = +props.orderId!;
      const params: OrderItemDTO[] = [];
      store.dataBrowse.fileds.forEach((item) => {
        item.data.forEach((f) => {
          params.push({ orderItemId: { orderId, cbdDefTableId: f }, quantity: 1 });
        });
      });
      await newOrUpdateOrderItem(params);
      router.back();
    } catch (error) {
      console.log('🚀 ~ onSave ~ error:', error);
    } finally {
      saveLoading.value = false;
    }
  };
  const onQuit = () => {
    router.back();
  };

  async function fetchDetail() {
    try {
      loading.value = true;
      const [{ data }] = await Promise.all([
        findOrderItemVOByOrderId(+props.orderId!),
        // getOrderStatistic(+props.orderId!),
      ]);
      // tableData.value = data || [];
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  watch(
    id,
    (value) => {
      if (id.value === '') {
        componentId.value = 'rank';
      } else {
        catalogId.value = id.value;
        componentId.value = 'type';
      }
    },
    {
      immediate: true,
    }
  );

  watchEffect(() => {
    if (props.orderId) {
      fetchDetail();
    }
  });

  onBeforeUnmount(() => {
    store.setData({ isSelectMode: false, id: '', fileds: [] });
  });
</script>
