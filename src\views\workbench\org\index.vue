<template>
  <div class="relative flex w-full flex-col">
    <div class="h-[220px] bg-p">
      <div class="pl w-[400px] pl-[60px] pt-10">
        <span class="text-2xl font-bold text-w">{{ username }}，欢迎登录本系统！</span>
        <div class="mt-5" style="color: rgba(255, 255, 255, 0.7)">
          <p class="mb-2 text-sm">上次登录时间：2022-03-12 12：00</p>
          <p class="text-sm">上次登录地点：山东省济南市</p>
        </div>
      </div>
    </div>

    <div class="h-0 flex-1">
      <div class="allicon flex w-[400px] flex-wrap justify-between p-[50px]">
        <div class="icon-item" @click="gotoPage('OrgInfo')">
          <svgicon icon-name="icon-yingyongjigouguanli" />
          <span>机构信息</span>
        </div>
        <div class="icon-item" @click="gotoPage('OrgPersonManage')">
          <svgicon icon-name="icon-renyuanguanli" />
          <span>人员管理</span>
        </div>
        <div class="icon-item" @click="gotoPage('WorkbenchDataManage')">
          <svgicon color="#666" icon-name="icon-tongjitu" />
          <span>数据管理</span>
        </div>
        <div class="icon-item" @click="gotoPage('OrgApprove')">
          <svgicon icon-name="icon-a-shenpi2" />
          <span>审批中心</span>
        </div>
        <div class="icon-item" @click="gotoPage('WorkbenchBaseInfo')">
          <svgicon icon-name="icon-morentouxiang" />
          <span>用户中心</span>
        </div>
        <div class="icon-item" @click="gotoPage('WorkbenchMsg')">
          <svgicon icon-name="icon-naoling" />
          <span>消息中心</span>
        </div>
        <div class="icon-item" @click="gotoPage('')">
          <svgicon icon-name="icon-wangzhanzidingyixinxizhanshi" />
          <span>门户网站</span>
        </div>
      </div>
    </div>

    <div class="absolute left-[400px] right-0 top-0 pb-5 pr-5 pt-5">
      <div class="flex">
        <div class="mr-5 flex-[2]">
          <div class="h-[240px]">
            <introductory />
          </div>
          <div class="mt-5 h-[290px]">
            <network />
          </div>
        </div>
        <div class="h-[550px] flex-1">
          <message />
        </div>
      </div>

      <div class="mt-5">
        <project />
      </div>
    </div>
  </div>
</template>

<script setup>
  import message from '../components/message.vue';
  import introductory from '../components/introductory.vue';
  import network from '../components/network.vue';
  import project from './components/project.vue';
  import { useRouter } from 'vue-router';
  let router = useRouter();
  import { useUsers } from '@/store/user-info';
  import { ElMessage } from 'element-plus';

  const username = computed(() => {
    return useUsers().user.username;
  });
  let gotoPage = (name) => {
    if (!name) {
      ElMessage({ type: 'warning', message: '暂未开放' });
      return;
    }
    router.push({ name });
  };
</script>

<style scoped lang="scss">
  .allicon {
    .icon-item {
      height: 112px;
      width: 112px;
      margin-bottom: 16px;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      padding-top: 15px;
      border-bottom: 3px solid transparent;

      .svg-icon {
        width: 44px;
        height: 44px;
      }

      &:hover {
        border-bottom: 3px solid $color-primary;
      }

      span {
        margin-top: 16px;
        margin-bottom: 13px;
      }
    }
  }

  .example-showcase .el-dropdown-link {
    cursor: pointer;
    color: none;
    display: flex;
    align-items: center;
  }
  .el-dropdown span {
    color: white;
  }

  .el-dropdown {
    --el-dropdown-menu-box-shadow: none;
    --el-dropdown-menuItem-hover-fill: none;
    --el-dropdown-menuItem-hover-color: none;
    color: #007f99 !important;
    outline: none;
  }

  .el-dropdown:hover {
    outline: none;
  }
</style>
