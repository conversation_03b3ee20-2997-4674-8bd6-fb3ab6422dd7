import { defineStore } from 'pinia';
import { fetchUser } from '@/utils/common-api';
import { postLogin, deleteLogout, changePwd } from '@/api/user';
import { ElMessage, ElMessageBox } from 'element-plus';
import { computed, reactive } from 'vue';
import router from '@/router/index';
import { useRouteStore } from './route-store';
import { getRoutesByRoleCode } from '@/utils/business';
import { refreshTokenApi } from '@/api/refreshApi';
import type { RoleCode } from 'types/router';

interface User {
  id: number;
  token: string;
  refreshToken: string;
  username: string;
  roleCode: RoleCode[];
  userCenterPageUp: string; // 用于用户中心返回上一页
  lockFlag: string; //锁定标记，0已启用，1注册申请中，3注册审核拒绝，9已锁定
  registerId: number; //注册申请id
  /**
   * 用户类型
   */
  userType: '个人用户' | '机构用户';
}

interface RememberData {
  username: string;
  password: string;
}

export const useUsers = defineStore(
  'userInfo',
  () => {
    const routeStore = useRouteStore();

    const user = reactive<User>({
      id: 0,
      token: '',
      refreshToken: '',
      username: '',
      roleCode: [],
      userCenterPageUp: '',
      lockFlag: '1',
      registerId: 0,
      userType: '个人用户',
    });

    async function login(form) {
      try {
        const res: any = await postLogin(form);
        user.token = res.access_token;
        user.refreshToken = res.refresh_token;
        user.username = res.username;
        user.id = res.user_id;
        await fetchUser();
        return Promise.resolve(res);
      } catch (error) {
        return Promise.reject(error);
      }
    }

    function logout() {
      return new Promise<void>((resolve, reject) => {
        ElMessageBox({
          closeOnClickModal: false,
          closeOnPressEscape: false,
          title: '提示',
          message: '此操作将退出登录, 是否继续?',
          showCancelButton: true,
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          buttonSize: 'default',
          beforeClose: (action, instance, done) => {
            if (action === 'confirm') {
              instance.confirmButtonLoading = true;
              instance.confirmButtonText = '退出中';
              setTimeout(() => {
                done();
                setTimeout(() => {
                  instance.confirmButtonLoading = false;
                }, 300);
              }, 700);
            } else {
              done();
            }
          },
        })
          .then(async () => {
            try {
              quit();
              resolve();
            } catch (error) {
              console.log(error);
              reject(error);
            }
          })
          .catch((error) => {
            reject(error);
          });
      });
    }

    function quit() {
      // await deleteLogout();
      _resetUser();
      routeStore.init = false;
    }

    function refreshToken() {
      return new Promise((resolve, reject) => {
        refreshTokenApi(user.refreshToken)
          .then((res) => {
            // 存储token 信息
            user.token = res.data.access_token;
            user.refreshToken = res.data.refresh_token;
            resolve(res);
          })
          .catch((err) => {
            ElMessage.error({
              message: '您的登录已过期，请重新登录。',
              type: 'error',
              duration: 3000, // 可选：设置消息显示的持续时间
              showClose: true, // 可选：显示关闭按钮
            });
            reject(err);
          });
      });
    }

    function changePassword(data: PasswordDTO) {
      if (!data.id) {
        data.id = user.id;
      }
      return changePwd(data);
    }

    function _resetUser() {
      user.id = 0;
      user.token = '';
      user.refreshToken = '';
      user.username = '';
      user.roleCode = [];
      user.userCenterPageUp = '';
      user.lockFlag = '1';
      user.registerId = 0;
      user.userType = '个人用户';
    }

    const setLockFlag = (lockFlag: string) => {
      user.lockFlag = lockFlag;
    };
    const setUserCenterPageUp = (value: string) => {
      user.userCenterPageUp = value;
    };
    const setRegisterId = (id: number) => {
      user.registerId = id;
    };
    const setUserType = (userType: User['userType']) => {
      user.userType = userType;
    };
    const setRole = (roleCode: User['roleCode']) => {
      user.roleCode = roleCode;
    };

    const rememberData = reactive<RememberData>({
      username: '',
      password: '',
    });
    function setRememberData(data: RememberData) {
      rememberData.username = data.username;
      rememberData.password = data.password;
    }

    const loggedHome = computed(() => {
      const rolePageMap = {
        ADMIN: 'Background',
        CUSTOMER: 'Personal',
        RESOURCE_OPERATOR: 'IntelligentHome',
        USER_OPERATOR: 'BusinessCustomer',
        THEME_OPERATOR: 'ThematicDataTable',
      };

      let home = 'Index';
      const { roleCode } = user;

      if (roleCode.length === 0) {
        home = 'WorkbenchBaseInfo';
      } else if (roleCode.length === 1) {
        const code = roleCode[0];
        home = rolePageMap[code] || 'WorkbenchBaseInfo';
      } else {
        home = 'RoleLandingPage';
      }

      return home;
    });

    /**
     * 账号是否正常
     */
    const isEnable = computed(() => {
      return user.lockFlag === '0';
    });
    //是否登录
    const isLogin = computed(() => {
      return !!user.token;
    });

    // watch(user.roleCode, () => {
    //   //根据角色动态生成路由
    //   const accessRoutes = getRoutesByRoleCode(user.roleCode);
    //   accessRoutes.forEach((route) => {
    //     router.addRoute(route);
    //   });
    //   routeStore.init = true;
    // });

    return {
      user,
      loggedHome,
      isEnable,
      logout,
      login,
      refreshToken,
      rememberData,
      setRememberData,
      setLockFlag,
      setUserCenterPageUp,
      setRegisterId,
      changePassword,
      quit,
      isLogin,
      setUserType,
      setRole,
    };
  },
  {
    persist: true,
  }
);
