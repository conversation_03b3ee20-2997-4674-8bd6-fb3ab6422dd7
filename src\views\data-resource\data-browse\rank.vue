<template>
  <div v-loading="loading" class="page">
    <div class="section">
      <h3 class="section-title">
        {{ name }}
      </h3>
      <span class="section-tip">{{ title }}</span>
    </div>
    <p class="p">
      {{ description }}
    </p>
    <!-- <p style="margin-top: 10px">
      我国首个面向脑疾病临床数据的大规模平台，提供了数据存储、获取、展示、分析等一系列功能，方便研究人员进行研究，促进脑疾病研究发展。
    </p> -->

    <div class="section mt-10">
      <h3 class="section-title">数据统计</h3>
      <span class="section-tip">数据从哪些方面统计的简单介绍</span>
    </div>

    <ul class="chart-wrapper">
      <li v-for="(item, index) in optionList" :key="index" class="chart">
        <Chart :option="item" />
      </li>
    </ul>
  </div>
</template>

<script setup>
  /* 数据浏览一级 */
  import { dataBrowse } from '@/api/index';
  import Chart from '@/components/Chart.vue';
  import { line, bar, pie, barLine, lineArea, scatter } from './components/chart';

  const loading = ref(true);
  const optionList = ref([]);
  const description = ref('');
  const title = ref('');
  const name = ref('');

  fetchData();
  async function fetchData() {
    try {
      loading.value = true;
      const { data } = await dataBrowse();
      description.value = data.description;
      title.value = data.title;
      name.value = data.name;
      data.chartList.forEach((item) => {
        if (item.type === 'line') {
          line.title.text = item.title;
          line.xAxis.data = item.xaxis.data;
          line.series = item.seriesList.map((serie) => {
            serie.symbol = 'circle';
            serie.showSymbol = false;
            return serie;
          });
        }
        if (item.type === '柱状图') {
          bar.title.text = item.title;
          bar.xAxis.data = item.xaxis.data;
          bar.series = item.seriesList.map((serie) => {
            serie.barWidth = '50%';
            return serie;
          });
        }
        if (item.type === '饼图') {
          pie.title.text = item.title;
          pie.series[0].data = item.seriesList[0].data[0];
        }
        if (item.type === '双轴图') {
          barLine.title.text = item.title;
          barLine.xAxis.data = item.xaxis.data;
          barLine.series[0].data = item.seriesList[0].data;
          barLine.series[1].data = item.seriesList[1].data;
        }
        if (item.type === '面积图') {
          lineArea.title.text = item.title;
          lineArea.xAxis.data = item.xaxis.data;
          lineArea.series[0].data = item.seriesList[0].data;
          lineArea.series[1].data = item.seriesList[1].data;
        }
        if (item.type === '气泡图') {
          scatter.title.text = item.title;
          scatter.xAxis.data = item.xaxis.data;
          scatter.series[0].data = item.seriesList[0].data[0];
          scatter.series[1].data = item.seriesList[1].data[0];
          scatter.series[2].data = item.seriesList[2].data[0];
        }
      });
      optionList.value = [line, bar, pie, barLine, lineArea, scatter];
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }
</script>

<style lang="scss" scoped>
  .page {
    box-sizing: border-box;
    padding-left: 40px;
    padding-right: 40px;
    padding-top: 20px;
    padding-bottom: 20px;
  }

  .section {
    display: flex;
    align-items: flex-end;
    line-height: 1;

    .section-title {
      font-size: 28px;
    }

    .section-tip {
      margin-left: 24px;
      color: $color-tip-text;
    }
  }

  .p {
    margin-top: 18px;
  }

  .chart-wrapper {
    margin-top: 18px;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;

    .chart {
      height: 380px;
    }
  }
</style>
