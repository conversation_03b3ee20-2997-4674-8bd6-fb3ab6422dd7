import fs from 'fs';
import archiver from 'archiver';

// 创建输出文件流
const output = fs.createWriteStream('dist.zip');
const archive = archiver('zip', {
  zlib: { level: 9 }, // 设置压缩级别
});

// 监听完成事件
output.on('close', () => {
  console.log(`打包完成，文件大小：${archive.pointer()} bytes`);
});

// 监听错误事件
archive.on('error', (err) => {
  throw err;
});

// 将输出文件流与archive关联
archive.pipe(output);

// 添加dist目录到zip
archive.directory('dist/', false);

// 完成打包
archive.finalize();
