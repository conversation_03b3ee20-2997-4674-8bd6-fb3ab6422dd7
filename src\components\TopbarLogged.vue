<template>
  <div class="topbar-logged box-border flex h-[60px] w-full items-center justify-between bg-p pl-6 pr-10">
    <div class="flex h-full w-fit cursor-pointer items-center" @click="onBackHome">
      <img class="mr-3 h-8 w-8" src="@/assets/a142x.png" />
      <h2 class="text-lg font-bold text-w">
        {{ title }}
      </h2>
    </div>

    <div class="business-tab">
      <!-- <el-tabs v-if="showBusiness" v-model="businessName" @tabChange="onTabChange">
        <el-tab-pane label="数据资源管理" name="BusinessResource" />
        <el-tab-pane label="用户业务管理" name="BusinessCustomer" />
        <el-tab-pane label="专题库管理" name="BusinessThematic" />
      </el-tabs> -->
      <el-tabs v-if="showDataManage" v-model="dataManageName" @tab-change="onTabChange">
        <el-tab-pane v-for="(item, index) in dataManageTabs" :key="index" :label="item.label" :name="item.name" />
      </el-tabs>
    </div>

    <div class="flex items-center">
      <svgicon
        style="height: 20px; width: 20px"
        class="mr-9 cursor-pointer"
        icon-name="icon-a-naolingtixingtongzhi"
        color="#fff"
        @click="gomsg"
      />

      <svgicon
        v-if="showShoppingCard"
        style="height: 20px; width: 20px"
        class="mr-9 cursor-pointer"
        icon-name="icon-gouwuche1"
        color="#fff"
        @click="onShoppingCard"
      />

      <el-icon class="mr-9 cursor-pointer" size="20px">
        <House color="#fff" @click="goindex" />
      </el-icon>

      <el-dropdown v-if="inResource">
        <div class="mr-9 flex select-none items-center text-w">
          <el-icon :size="18"><Menu /></el-icon>
        </div>
        <template #dropdown>
          <TopbarDropdownResource />
        </template>
      </el-dropdown>

      <el-dropdown trigger="click">
        <div class="flex select-none items-center text-w">
          <svgicon
            style="height: 20px; width: 20px"
            class="mr-2 cursor-pointer"
            icon-name="icon-touxiangtongyong"
            color="#ffffff"
          />
          {{ username }}
          <el-icon :size="14" color="#fff">
            <CaretBottom />
          </el-icon>
        </div>
        <template #dropdown>
          <TopbarDropdown />
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup lang="ts">
  import TopbarDropdown from './TopbarDropdown.vue';
  import TopbarDropdownResource from './TopbarDropdownResource.vue';
  import { useRouter, useRoute } from 'vue-router';
  const router = useRouter();
  const route = useRoute();
  import { useUsers } from '@/store/user-info';
  import { ElMessage } from 'element-plus';
  const store = useUsers();

  const username = computed(() => {
    return store.user.username;
  });
  const showShoppingCard = computed(() => {
    return false;
  });
  const title = computed(() => {
    let text = '工作台';
    if (store.user.roleCode.includes('RESOURCE_OPERATOR')) {
      text = '数据管理平台';
    }
    return text;
  });

  // const showBusiness = computed(() => {
  //   return store.user.roleName === 4;
  // });
  // const businessName = ref('BusinessCustomer');

  const showDataManage = computed(() => {
    return dataManageTabs.some((item) => route.fullPath.includes(item.path));
  });
  const dataManageName = ref('CleansingStandard');
  const dataManageTabs = [
    { name: 'IntelligentRegister', label: '数据目录管理', path: '/intelligent/catalog' },
    // { name: 'CleansingStandard', label: '数据清洗管理', path: '/intelligent/cleansing' },
    // { name: 'DesensitizationArithmetic', label: '数据脱敏管理', path: '/intelligent/desensitization' },
  ];
  const onTabChange = (name) => {
    router.push({ name });
  };
  watch(
    () => route.fullPath,
    (fullPath) => {
      dataManageTabs.forEach((item) => {
        if (fullPath.includes(item.path)) {
          dataManageName.value = item.name;
        }
      });
    },
    {
      immediate: true,
    }
  );

  let onBackHome = () => {
    router.replace({ name: store.loggedHome });
  };
  let onShoppingCard = () => {
    router.push({ name: 'WorkbenchCart' });
  };
  let goindex = () => {
    router.push({ name: 'Index' });
  };
  let gomsg = () => {
    ElMessage({ type: 'warning', message: '功能建设中' });
    // router.push({ name: 'WorkbenchMsg' });
  };

  const inResource = computed(() => route.path.startsWith('/intelligent'));
</script>

<style scoped lang="scss">
  .el-tooltip__trigger:focus-visible {
    outline: none;
  }

  :deep(.business-tab) {
    .el-tabs__header {
      margin: 0;
    }
    .el-tabs__active-bar {
      height: 0;
    }
    .el-tabs__item {
      color: #ffffff;
      height: 60px;
    }
    .el-tabs__item:hover {
      color: #fff !important;
    }
    .el-tabs__item.is-active {
      background: linear-gradient(#007f99, #3298ad);
      border-bottom: 2px solid #ffffff;
    }
  }
  .business-tab {
    :deep(.el-tabs--top .el-tabs__item.is-top:nth-child(2)) {
      padding-left: 20px;
    }
    :deep(.el-tabs--top .el-tabs__item.is-top:last-child) {
      padding-right: 20px;
    }
  }
</style>
