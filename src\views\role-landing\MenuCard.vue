<template>
  <li
    class="text-w flex h-[160px] w-[300px] items-center rounded-md bg-gradient-to-r py-4 pr-6 pl-4 shadow-lg"
    :class="bgGradient"
    @click="router.push({ name: routeName })"
  >
    <div>
      <div class="text-lg font-bold">{{ title }}</div>
      <div class="mt-2 mb-4 text-sm opacity-90">{{ description }}</div>
      <el-button style="background-color: transparent; color: #fff">进入</el-button>
    </div>
    <img :src="imageSrc" class="ml-6 h-[90px] w-[90px]" />
  </li>
</template>

<script setup lang="ts">
  import { useRouter } from 'vue-router';
  defineProps({
    title: {
      type: String,
      required: true,
    },
    description: {
      type: String,
      required: true,
    },
    imageSrc: {
      type: String,
      required: true,
    },
    routeName: {
      type: String,
      required: true,
    },
    bgGradient: {
      type: String,
      required: true,
    },
  });

  const router = useRouter();
</script>
