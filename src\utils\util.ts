/**
 * 根据id更新catalogData中对应项目的num值
 * @param {string} id - 要更新的项目的唯一标识符
 * @param {number} newNum - 新的num值
 * @returns {void}
 */
export function updateNumById(id, newNum, data) {
  const flattenCatalog = flattenNestedCatalog(data.value);
  const targetItem = flattenCatalog.find((item) => item.id === id);

  if (targetItem) {
    targetItem.num = newNum;
  }
}

/**
 * 将嵌套的目录结构展平为一维数组
 * @param {Array} catalog - 要展平的目录数组
 * @returns {Array} - 展平后的一维数组
 */
function flattenNestedCatalog(data) {
  return data.reduce((flatArray, item) => {
    flatArray.push(item);
    if (item.children && item.children.length > 0) {
      flatArray.push(...flattenNestedCatalog(item.children));
    }
    return flatArray;
  }, []);
}

/**
 * 合并两个对象，只改变第一个对象上已存在的属性
 * @param {Object} obj1 - 第一个对象
 * @param {Object} obj2 - 第二个对象
 * @returns {Object} - 合并后的对象
 */
export function mergeObjects(obj1, obj2) {
  // 遍历第一个对象的属性
  Object.keys(obj1).forEach((key) => {
    // 如果第二个对象也有相同的属性
    if (Object.prototype.hasOwnProperty.call(obj2, key)) {
      // 更新第一个对象的属性值为第二个对象对应属性的值
      obj1[key] = obj2[key];
    }
  });
  // 返回合并后的第一个对象
  return obj1;
}

/**
 * 将二进制数据转换为图像 URL。
 * @param binaryData - 要转换的二进制数据。
 * @param type - 图像的 MIME 类型（例如 'image/png'）。
 * @returns 指向由二进制数据创建的 Blob 对象的 URL。
 */
export function convertToImage(binaryData: Uint8Array, type: string): string {
  // 将二进制数据转换为Blob对象
  const blob = new Blob([binaryData], { type });
  // 创建一个URL指向Blob对象
  return URL.createObjectURL(blob);
}

/**
 * 检查给定的字符串是否为有效的URL
 * @param url - 要检查的字符串
 * @returns 如果字符串是有效的URL则返回true，否则返回false
 */
export function isUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch (e) {
    return false;
  }
}
