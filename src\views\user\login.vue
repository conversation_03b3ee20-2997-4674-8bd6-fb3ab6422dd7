<template>
  <div class="page">
    <div class="box">
      <div class="box-header">
        <h2 class="title">登录</h2>
        <div class="flex items-center">
          <a class="link" @click="goperRegister">个人注册</a>
          <div class="line" />
          <a class="link" @click="goorRegister">机构注册</a>
        </div>
      </div>

      <el-form ref="loginForm" class="form" :model="form" :rules="loginRules">
        <el-form-item v-if="mode === 'development'" prop="username">
          <el-select
            v-model="form.username"
            placeholder="请选择或输入"
            style="width: 100%"
            filterable
            allow-create
            default-first-option
            @change="handleChange"
          >
            <el-option v-for="item in testArr" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item v-else prop="username">
          <el-input
            v-model="form.username"
            size="large"
            placeholder="用户名"
            :prefix-icon="User"
            maxlength="30"
            @keypress.enter="submitform"
          />
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="form.password"
            type="password"
            size="large"
            placeholder="密码"
            :prefix-icon="Lock"
            maxlength="20"
            @keypress.enter="submitform"
          />
        </el-form-item>

        <!-- <el-form-item prop="code">
          <el-col :span="15">
            <el-input v-model="form.code" text maxlength="4" placeholder="验证码" clearable autocomplete="off">
              <template #prefix>
                <el-icon class="el-input__icon">
                  <Position />
                </el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="1"></el-col>
          <el-col :span="8">
            <img :src="imgSrc" @click="getVerifyCode" />
          </el-col>
        </el-form-item> -->

        <div class="auto-login">
          <el-checkbox v-model="remember" label="记住密码" size="large" />

          <!-- <div class="mb-2 flex items-center">
            <a @click="forgetpassword">忘记密码</a>
            <div class="line" />
            <a @click="forgetname">忘记账号</a>
          </div> -->
        </div>
      </el-form>

      <el-button :loading="saveLoading" class="btn" color="#007f99" type="primary" size="large" @click="submitform">
        登录
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { useUsers } from '@/store/user-info';
  const store = useUsers();
  import { User, Lock, Position } from '@element-plus/icons-vue';
  import { useRouter } from 'vue-router';
  const router = useRouter();
  import { generateUUID, encryption, decryption } from '@/utils/crypto';
  import { FormInstance } from 'element-plus';

  let loginForm = ref<FormInstance>();
  let loginRules = {
    username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
    password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  };
  let form = reactive({
    username: '',
    password: '',
    code: '', // 验证码
    randomStr: '', // 验证码随机数
  });
  const remember = ref(false);
  const saveLoading = ref(false);
  const mode = computed(() => import.meta.env.MODE);

  //获取验证码图片
  function getVerifyCode() {
    form.randomStr = generateUUID();
    imgSrc.value = `${import.meta.env.VITE_API_URL}/code/image?randomStr=${form.randomStr}`;
  }

  let submitform = () => {
    loginForm.value?.validate(async (valid) => {
      if (valid) {
        try {
          saveLoading.value = true;
          await store.login(form);
          if (remember.value) {
            const encPassword = encryption(form.password, import.meta.env.VITE_PWD_ENC_KEY);
            store.setRememberData({ username: form.username, password: encPassword });
          } else {
            store.setRememberData({ username: '', password: '' });
          }
          router.push({ path: '/blank' });
        } catch (error) {
          console.log(error);
          // getVerifyCode();
        } finally {
          saveLoading.value = false;
        }
      }
    });
  };
  //个人注册
  let goperRegister = () => {
    router.push({ name: 'PersonalRegister' });
  };
  //机构注册
  let goorRegister = () => {
    router.push({ name: 'OrgRegister' });
  };
  //忘记密码
  let forgetpassword = () => {
    router.push({ name: 'ForgetPass' });
  };
  //忘记账号
  let forgetname = () => {
    router.push({ name: 'ForgetName' });
  };

  const imgSrc = ref('');

  //回显账密
  if (store.rememberData.password) {
    form.username = store.rememberData.username;
    form.password = decryption(store.rememberData.password, import.meta.env.VITE_PWD_ENC_KEY);
    remember.value = true;
  }

  // onMounted(() => {
  //   getVerifyCode();
  // });

  const testArr = ref([
    { value: 'b_admin', label: 'b_admin' },
    { value: 'b_normal', label: '普通用户' },
    { value: 'b_resource', label: '数据资源管理员' },
    { value: 'b_user', label: '用户业务管理员' },
    { value: 'b_theme', label: '专题库管理员' },
    { value: 'b_noAuth', label: '未认证用户' },
  ]);
  const handleChange = (value) => {
    // 如果用户输入的值不在选项中，可以将其添加到选项列表中
    if (!testArr.value.some((item) => item.value === value)) {
      testArr.value.push({ value: value, label: value });
    }
  };
</script>

<style scoped lang="scss">
  .page {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
  }

  .line {
    background-color: #c8cbcc;
    width: 1px;
    height: 14px;
    margin: 0 16px;
  }

  .box {
    border-radius: 8px;
    background: #ffffff;
    padding: 100px 140px 120px 140px;
    width: 480px;
    box-sizing: content-box;
  }

  .box-header {
    display: flex;
    justify-content: space-between;

    .title {
      font-size: 30px;
      font-weight: 700;
      line-height: 44px;
    }

    .link {
      color: $color-primary;
    }
  }

  .form {
    margin-top: 48px;

    .el-form-item {
      margin-bottom: 28px;
    }
    :deep(.el-input span) {
      font-size: 20px;
    }
  }

  .auto-login {
    display: flex;
    justify-content: space-between;
    color: $color-regular-text;
  }

  .btn {
    width: 100%;
  }
</style>
