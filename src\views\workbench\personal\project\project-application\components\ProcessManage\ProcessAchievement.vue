<template>
  <div class="h-full">
    <el-scrollbar height="100%">
      <div class="overflow-hidden px-10 pt-6">
        <div class="flex items-center">
          <h4 class="text-xl font-bold">项目成果</h4>
        </div>
        <div class="mt-6">
          <el-button type="primary" @click="onUpload"> 上传成果 </el-button>
        </div>
        <AchievementManuscripts v-if="data.applyId" :data="data" @edit="onEdit('1')" @del="onDel('1')" />
        <AchievementDataFile v-if="data.count !== ''" :data="data" @edit="onEdit('2')" @del="onDel('2')" />
        <AchievementSourceCode v-if="data.desc" :data="data" @edit="onEdit('3')" @del="onDel('3')" />
        <AchievementAttached v-if="data.fileDesc" :data="data" @edit="onEdit('4')" @del="onDel('4')" />
      </div>
    </el-scrollbar>
  </div>

  <AchievementDrawer v-model="drawer" :data="data" @success="onSuccess" />
</template>

<script setup>
  /* 项目成果 */
  import AchievementManuscripts from './AchievementManuscripts.vue';
  import AchievementDataFile from './AchievementDataFile.vue';
  import AchievementSourceCode from './AchievementSourceCode.vue';
  import AchievementAttached from './AchievementAttached.vue';
  import AchievementDrawer from './AchievementDrawer.vue';

  const drawer = ref(false);
  const data = reactive({
    type: '1',
    applyId: '4563223568',
    title: '',
    firstAuthor: '',
    magazine: '',
    publicationYear: '',
    doi: '',
    pubmedId: '',
    manuscriptsFile: [],

    count: 452,
    derivedDesc: '',
    isUpdate: true,
    isForOther: true,
    publication: '',
    detail: '',
    dataFile: [],

    desc: '这是一段很长的描述',
    codePublication: '',
    language: '',
    platform: '',
    codeDoi: '',
    codeFile: [],

    fileDesc: '这是一段很长的描述',
    additionPublication: '',
    additionFile: [],
  });
  //上传成果
  const onUpload = () => {
    drawer.value = true;
  };
  const onEdit = (type) => {
    data.type = type;
    drawer.value = true;
  };
  const onDel = (type) => {
    switch (type) {
      case '1':
        data.applyId = '';
        break;
      case '2':
        data.count = '';
        break;
      case '3':
        data.desc = '';
        break;
      case '4':
        data.fileDesc = '';
        break;
    }
  };
  const onSuccess = () => {};
</script>
