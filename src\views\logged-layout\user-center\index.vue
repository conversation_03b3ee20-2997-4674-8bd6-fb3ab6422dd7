<template>
  <div class="flex h-full flex-col">
    <div class="header mb-5 bg-w">
      <h2 class="flex cursor-pointer items-center text-xl font-bold" @click="onBackHome">
        <el-icon class="mr-2" color="#939899">
          <ArrowLeft />
        </el-icon>
        用户中心
      </h2>
    </div>

    <div class="flex h-0 flex-1 pb-5 pl-5 pr-5">
      <div class="menu-container h-full w-[240px]">
        <el-menu
          :default-active="activeId"
          active-text-color="#007f99"
          class="custom-menu h-full w-full"
          @select="clickMenu"
        >
          <el-menu-item v-for="(item, index) in menuList" :key="index" :index="item.pathName">
            <span>{{ item.name }}</span>
          </el-menu-item>
        </el-menu>
      </div>

      <div class="h-full w-0 flex-1 bg-w">
        <el-scrollbar height="100%">
          <router-view />
        </el-scrollbar>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { newOrUpdateUser, uploadAttachmentMaterial } from '@/api/index';
  import { useUsers } from '@/store/user-info';
  import { ElMessage } from 'element-plus';
  const store = useUsers();
  import { useRoute, useRouter } from 'vue-router';
  let route = useRoute();
  let router = useRouter();

  let activeId = ref('WorkbenchBaseInfo');
  let menuList = ref([
    {
      name: '基本信息',
      pathName: 'WorkbenchBaseInfo',
    },
    { name: '账号认证', pathName: 'WorkbenchMaterial' },
    { name: '账号安全', pathName: 'WorkbenchAccessSecurity' },
    // { name: '数据平台权限审核', pathName: 'WorkbenchDataPlatform' },
    // { name: '订阅', pathName: 'WorkbenchSubscription' },
    { name: '我的团队', pathName: 'WorkbenchMyTeam' },
    // { name: '数据管理', pathName: 'WorkbenchDataManage' },
  ]);
  // if (store.isEnable) {
  // menuList.value.push({ name: '我的团队', pathName: 'WorkbenchMyTeam' });
  // menuList.value.push({ name: '数据管理', pathName: 'WorkbenchDataManage' });
  // }

  watch(
    () => route.name,
    (routeName) => {
      const menuItem = menuList.value.find((item) => item.pathName == routeName);
      if (menuItem) {
        activeId.value = menuItem.pathName;
      }
    },
    { immediate: true }
  );

  let clickMenu = (name) => {
    if (['WorkbenchBaseInfo', 'WorkbenchMaterial', 'WorkbenchAccessSecurity', 'WorkbenchMyTeam'].includes(name)) {
      router.push({ name });
    } else {
      ElMessage({ type: 'warning', message: '功能建设中' });
    }
  };
  const onBackHome = () => {
    router.replace({ name: store.user.userCenterPageUp });
  };
</script>

<style lang="scss" scoped>
  .header {
    height: 60px;
    display: flex;
    align-items: center;
    padding-left: 20px;
  }

  .custom-menu {
    padding-top: 20px;
    --el-menu-item-height: 40px;

    .is-active {
      background-image: url('@/assets/img/workbench/menu-selected.png');
      border-right: 2px solid $color-primary;
    }

    .el-menu-item {
      margin-bottom: 8px;
    }
  }
</style>
