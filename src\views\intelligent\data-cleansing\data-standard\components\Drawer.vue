<template>
  <el-drawer v-model="drawer" class="relative" @close="onCancel">
    <template #header>
      <h4 class="text-m">{{ action }}标准</h4>
    </template>

    <el-form ref="formRef" label-position="top" :model="form" :rules="rules">
      <el-form-item label="标准名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入标准名称" maxlength="50" />
      </el-form-item>
      <el-form-item label="标准号" prop="standardNumber">
        <el-input v-model="form.standardNumber" placeholder="请输入标准号" maxlength="50" />
      </el-form-item>
      <el-form-item label="标准字典" prop="dictionary">
        <el-select v-model="form.dictionary" placeholder="请选择标准字典" style="width: 100%">
          <el-option v-for="item in dictionaryOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="标准级别" prop="level">
        <el-select v-model="form.level" placeholder="请选择标准级别" style="width: 100%">
          <el-option v-for="item in levelOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="标准内容" prop="content">
        <el-input
          v-model="form.content"
          placeholder="请输入标准内容"
          maxlength="300"
          type="textarea"
          :rows="4"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="onCancel"> 取消 </el-button>
      <el-button type="primary" @click="onSave"> 保存 </el-button>
    </template>
  </el-drawer>
</template>

<script setup>
  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
      required: true,
    },
    data: {
      type: Object,
      required: true,
    },
  });
  const dictionaryOptions = ref([
    {
      value: '1',
      label: '标准字典',
    },
  ]);
  const levelOptions = ref([
    {
      value: '1',
      label: '国家级',
    },
  ]);
  const form = reactive({
    name: '',
    standardNumber: '',
    dictionary: '',
    level: '',
    date: '',
  });
  const rules = reactive({
    name: [{ required: true, message: '请输入标准名称', trigger: 'blur' }],
    standardNumber: [{ required: true, message: '请输入标准号', trigger: 'blur' }],
    dictionary: [{ required: true, message: '请选择标准字典', trigger: 'blur' }],
    level: [{ required: true, message: '请选择标准级别', trigger: 'blur' }],
    date: [{ required: true, message: '请选择创建时间', trigger: 'blur' }],
  });

  const drawer = ref(false);
  watchEffect(() => {
    drawer.value = props.modelValue;
  });
  watch(
    () => props.data,
    (value) => {
      for (const key in form) {
        form[key] = value[key];
      }
    }
  );
  const action = computed(() => {
    return Object.values(props.data).length === 0 ? '添加' : '编辑';
  });

  const emit = defineEmits(['update:modelValue', 'success']);
  const formRef = ref();
  const onCancel = () => {
    emit('update:modelValue', false);
    formRef.value.resetFields();
  };
  const onSave = () => {
    formRef.value.validate((valid) => {
      if (valid) {
        onCancel();
        emit('success');
      }
    });
  };
</script>
