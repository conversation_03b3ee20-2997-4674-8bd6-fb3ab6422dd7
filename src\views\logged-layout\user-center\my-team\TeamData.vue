<template>
  <div class="flex h-full flex-col overflow-hidden">
    <div class="m-5 flex h-0 flex-1 flex-col bg-w px-10 pt-5">
      <div>
        <el-button type="primary" @click="onAudit"> 数据使用审批 </el-button>
      </div>

      <div class="mt-4 h-0 flex-1">
        <el-table :data="tableData" style="width: 100%" height="100%" class="c-table-header">
          <el-table-column prop="name" label="数据名称" min-width="100px" />
          <el-table-column prop="time" label="数据购买时间" />
          <el-table-column prop="duration" label="数据可用时间" />
          <el-table-column prop="dataSize" label="数据量" />
          <el-table-column prop="projectNum" label="使用项目数" />
          <el-table-column prop="people" label="使用人数" />
          <el-table-column label="操作" width="100px">
            <template #default="{ row }">
              <el-button link type="primary" @click="toDetail(row)"> 查看详情 </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { useRouter } from 'vue-router';
  const router = useRouter();

  const tableData = ref([
    {
      id: 1,
      name: '脑疾病数据AAAA',
      time: '2022-03-12 12:00:00',
      duration: '3年',
      dataSize: '1TB',
      projectNum: 32,
      people: 856,
    },
    {
      id: 2,
      name: '脑疾病数据AAAA',
      time: '2022-03-12 12:00:00',
      duration: '3年',
      dataSize: '1TB',
      projectNum: 32,
      people: 856,
    },
    {
      id: 3,
      name: '脑疾病数据AAAA',
      time: '2022-03-12 12:00:00',
      duration: '3年',
      dataSize: '1TB',
      projectNum: 32,
      people: 856,
    },
    {
      id: 4,
      name: '脑疾病数据AAAA',
      time: '2022-03-12 12:00:00',
      duration: '3年',
      dataSize: '1TB',
      projectNum: 32,
      people: 856,
    },
  ]);
  const toDetail = (row) => {
    router.push({ name: 'OrgDataDetail', query: { id: row.id } });
  };
  const onAudit = () => {};
</script>
