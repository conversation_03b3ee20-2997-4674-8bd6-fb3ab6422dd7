<template>
  <div class="page flex h-full flex-col bg-bac">
    <TopbarLogged />
    <div class="h-0 flex-1">
      <el-scrollbar ref="scrollbar" class="full-scrollbar" height="100%">
        <router-view />
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup>
  /* 需登录页面布局页 */
  import TopbarLogged from '@/components/TopbarLogged.vue';
  import { useRoute } from 'vue-router';
  const route = useRoute();

  const scrollbar = ref();
  watch(route, () => {
    scrollbar.value.scrollTo(0, 0);
  });
</script>

<style lang="scss" scoped>
  .page {
    min-width: $main-width;
    :deep(.full-scrollbar .el-scrollbar__view) {
      height: 100%;
    }
  }
</style>
