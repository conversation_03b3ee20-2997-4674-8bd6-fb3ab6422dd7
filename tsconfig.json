{"compilerOptions": {"target": "esnext", "module": "ESNext", "moduleResolution": "bundler", "strict": true, "noLib": false, "forceConsistentCasingInFileNames": true, "allowSyntheticDefaultImports": true, "strictFunctionTypes": false, "jsx": "preserve", "baseUrl": ".", "allowJs": true, "sourceMap": true, "esModuleInterop": true, "resolveJsonModule": true, "experimentalDecorators": true, "lib": ["dom", "esnext"], "noImplicitAny": false, "skipLibCheck": true, "types": ["vite/client"], "removeComments": true, "paths": {"@/*": ["src/*"]}, "outDir": "dist"}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "types/**/*.d.ts", "auto-imports.d.ts", "vite.config.ts", "eslint.config.js", "src/api/api.d.ts"], "exclude": ["node_modules", "dist", "**/*.js", "src/assets/*.js"]}