<template>
  <div v-for="(item, index) in data" :key="item.id">
    <el-sub-menu v-if="item.children && item.children.length" :index="item.id">
      <template #title>
        <svgicon v-if="item.svgname" style="width: 14px; height: 14px" :icon-name="item.svgname" />
        <span class="ml-1 text-regular">{{ item.title }}</span>
      </template>
      <CustomMenuItem :data="item.children" :current-id="currentId" />
    </el-sub-menu>

    <el-menu-item v-else :index="item.id">
      <div class="flex w-full items-center">
        <svgicon v-if="item.svgname" style="width: 14px; height: 14px" :icon-name="item.svgname" />
        <span class="ml-1 text-regular" :class="{ active: currentId === item.id }">{{ item.title }}</span>
        <span v-if="item.num > 0">({{ item.num }})</span>
      </div>
    </el-menu-item>
  </div>
</template>

<script setup>
  const props = defineProps({
    data: { type: Array, required: true },
    currentId: { type: String, required: true },
  });
</script>

<style lang="scss" scoped>
  .active {
    color: #4492aa;
  }
  .el-menu-item.is-active {
    background-color: #ebf5f7;
  }
</style>
