<template>
  <div class="flex h-full">
    <div class="aside bg-w">
      <asidemenu />
    </div>

    <div class="w-0 flex-1">
      <el-scrollbar height="100%">
        <router-view />
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup>
  /* 工作台固定左侧栏布局页 */
  import asidemenu from '@/components/aside.vue';
  import { useRoute } from 'vue-router';
  let route = useRoute();

  let activeid = ref('0');

  let menulist = ref([
    { id: '1', title: '主页', pathname: 'Personal', svgname: 'icon-zhuye' },
    {
      id: '2',
      title: '项目申请',
      pathname: 'PersonalProjectApplication',
      svgname: 'icon-shenqing',
    },
    {
      id: '3',
      title: '项目管理',
      pathname: 'PersonalProjectManage',
      svgname: 'icon-guanliyuan_guanliyuanrizhi',
    },
    // {
    //   id: '4',
    //   title: '数据展示',
    //   svgname: 'icon-tongjitu',
    //   pathname: '',
    // },
  ]);

  watch(
    () => route.name,
    (val, old) => {
      menulist.value.forEach((e) => {
        if (e.pathname == val) {
          activeid.value = e.id;
        }
      });
    },
    {
      immediate: true,
    }
  );
  provide('datalist', menulist);
  provide('openid', activeid);
</script>

<style scoped lang="scss">
  .aside {
    width: 256px;
    height: 100%;
    box-shadow: 2px 0 12px 0 rgb(0 0 0 / 10%);
    z-index: 10;
  }

  .main-header {
    padding-top: 15px;
    padding-left: 28px;
    background: #fff;
  }
</style>
