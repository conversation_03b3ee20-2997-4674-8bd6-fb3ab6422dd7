/*
 * @OriginalName: 数据资源系统中字典管理模块
 * @Description: 医学数据资源系统中字典信息的创建、更新和查询
 */
import { request } from '@/utils/request';

/**
 * 新建或更新数据表中的记录
 * @description 按tdto的信息，新建或更新数据表中的记录。
 */
export function newOrUpdateEntity_13(data: DictionaryDTO) {
  return request<RDictionaryVO>(`/Dictionary/newOrUpdateEntity`, {
    method: 'post',
    data,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条或多条数据记录。
 */
export function findEntityById_13(data: Array<number>) {
  return request<RListDictionaryVO>(`/Dictionary/findEntityByIdCollection`, {
    method: 'post',
    data,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一或多条记录。
 */
export function deleteEntityById_13(data: Array<number>) {
  return request<R>(`/Dictionary/deleteEntityByIdCollection`, {
    method: 'post',
    data,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条或多条数据记录。
 */
export function findEntityById_41(idArray: Array<object>, params?: { idArray: Array<object> }) {
  return request<RListDictionaryVO>(`/Dictionary/findEntityByIdArray/${idArray}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条数据记录。
 */
export function findEntityById_42(id: number, params?: { id: number }) {
  return request<RDictionaryVO>(`/Dictionary/findEntityById/${id}`, {
    method: 'get',
    params,
  });
}

/**
 * 查询满足状态条件的所有记录
 * @description 分页式返回满足状态条件的全部记录。state的值可以是：启用或禁用
 */
export function findByState_1(
  pageNum: number,
  pageSize: number,
  params?: { state?: string; pageNum: number; pageSize: number }
) {
  return request<RVOPageSystemDictionaryDictionaryVO>(`/Dictionary/findByState/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 显示数据表中的全部记录
 * @description 分页显示数据表中的全部记录
 */
export function findAll_13(pageNum: number, pageSize: number, params?: { pageNum: number; pageSize: number }) {
  return request<R>(`/Dictionary/findAll/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一或多条记录。
 */
export function deleteEntityById_41(idArray: Array<object>, params?: { idArray: Array<object> }) {
  return request<R>(`/Dictionary/deleteEntityByIdArray/${idArray}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一条记录。
 */
export function deleteEntityById_42(id: number, params?: { id: number }) {
  return request<R>(`/Dictionary/deleteEntityById/${id}`, {
    method: 'get',
    params,
  });
}
