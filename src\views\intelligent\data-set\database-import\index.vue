<template>
  <div class="flex h-full flex-col overflow-hidden">
    <h2 class="bg-w flex h-[60px] items-center pl-5 text-xl font-semibold">数据源管理</h2>

    <div v-loading="loading" class="bg-w m-5 flex h-0 flex-1 flex-col rounded-md pt-5">
      <div class="flex justify-between px-10">
        <el-button type="primary" @click="onAdd">新增</el-button>

        <el-input
          v-model="search"
          placeholder="请输入关键字搜索"
          style="width: 300px"
          clearable
          @clear="onSearch"
          @keyup.enter="onSearch"
        >
          <template #append>
            <el-button :icon="Search" @click="onSearch" />
          </template>
        </el-input>
      </div>

      <div class="mt-3 h-0 w-full flex-1 px-10">
        <el-table height="100%" :data="tableData" style="width: 100%" class="c-table-header">
          <el-table-column prop="databaseType" label="数据库类型" width="100px" />
          <el-table-column prop="driverClassName" label="数据库驱动" />
          <el-table-column prop="address" label="数据库地址" />
          <el-table-column prop="port" label="端口号" width="80px" />
          <el-table-column prop="databaseName" label="数据库名称" />
          <el-table-column prop="aliasName" label="数据库别名" />
          <el-table-column prop="startDate" label="生效开始时间" width="170px" />
          <el-table-column prop="endDate" label="生效结束时间" width="170px" />
          <el-table-column prop="note" label="备注" />
          <el-table-column fixed="right" label="操作" width="200">
            <template #default="{ row }">
              <el-space wrap>
                <el-link type="primary" :underline="false" @click="onViewDetail(row)">查看</el-link>
                <el-link type="primary" :underline="false" @click="onEdit(row)">修改</el-link>
                <el-popconfirm title="确定删除？" @confirm="onDel(row)">
                  <template #reference>
                    <el-link type="primary" :underline="false">删除</el-link>
                  </template>
                </el-popconfirm>
                <el-link type="primary" :underline="false" @click="onTest(row)">测试连接</el-link>
                <el-link type="primary" :underline="false" @click="onImport(row)">导入到平台数据库</el-link>
              </el-space>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="pagination-bottom">
        <el-pagination
          background
          layout="total, prev, pager, next, jumper"
          :page-size="pagination.pageSize"
          :total="total"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>

  <el-dialog v-model="showAdd" :title="formTitle" width="700px" @close="onAddClose">
    <el-form ref="formRef" :model="addForm" :rules="rules" class="mt-4" label-width="200px">
      <el-form-item label="数据库类型" prop="databaseType">
        <el-select v-model="addForm.databaseType" placeholder="请选择数据库类型">
          <el-option v-for="item in databaseTypes" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="数据库驱动" prop="driverClassName">
        <el-input v-model="addForm.driverClassName" placeholder="请输入数据库配置驱动" maxlength="50" />
      </el-form-item>
      <el-form-item label="数据库地址" prop="address">
        <el-input v-model="addForm.address" placeholder="请输入数据库地址" maxlength="200" />
      </el-form-item>
      <el-form-item label="端口号" prop="port">
        <el-input v-model="addForm.port" placeholder="请输入端口号" maxlength="10" />
      </el-form-item>
      <el-form-item label="数据库名称" prop="databaseName">
        <el-input v-model="addForm.databaseName" placeholder="请输入数据库名称" maxlength="30" />
      </el-form-item>
      <el-form-item label="数据库别名" prop="aliasName">
        <el-input v-model="addForm.aliasName" placeholder="请输入数据库别名" maxlength="30" />
      </el-form-item>
      <el-form-item label="用户名" prop="userName">
        <el-input v-model="addForm.userName" placeholder="请输入用户名" maxlength="50" />
      </el-form-item>
      <el-form-item label="密码" prop="password">
        <el-input v-model="addForm.password" placeholder="请输入密码" maxlength="50" />
      </el-form-item>
      <el-form-item label="数据库编码规则" prop="characterEncoding">
        <el-input v-model="addForm.characterEncoding" placeholder="请输入数据库编码规则" maxlength="200" />
      </el-form-item>
      <el-form-item label="测试连接字符串" prop="zeroDateTimeBehavior">
        <el-input v-model="addForm.zeroDateTimeBehavior" placeholder="请输入测试数据库连接属性" maxlength="500" />
      </el-form-item>
      <el-form-item label="useSSL" prop="useSSL">
        <el-radio-group v-model="addForm.useSSL">
          <el-radio value="是">是</el-radio>
          <el-radio value="否">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="是否使用时区转换兼容规则" prop="useJDBCCompliantTimezoneShift">
        <el-radio-group v-model="addForm.useJDBCCompliantTimezoneShift">
          <el-radio value="是">是</el-radio>
          <el-radio value="否">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="是否使用本地时区" prop="useLegacyDatetimeCode">
        <el-radio-group v-model="addForm.useLegacyDatetimeCode">
          <el-radio value="是">是</el-radio>
          <el-radio value="否">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="mysql时区" prop="serverTimezone">
        <el-input v-model="addForm.serverTimezone" placeholder="请输入mysql使用的时区" maxlength="200" />
      </el-form-item>
      <el-form-item label="生效开始时间" prop="startDate">
        <el-date-picker v-model="addForm.startDate" type="datetime" placeholder="请选择开始时间" />
      </el-form-item>
      <el-form-item label="生效结束时间" prop="endDate">
        <el-date-picker v-model="addForm.endDate" type="datetime" placeholder="请选择结束时间" />
      </el-form-item>
      <el-form-item label="备注" prop="note">
        <el-input v-model="addForm.note" placeholder="请输入备注" maxlength="200" />
      </el-form-item>
    </el-form>

    <template #footer>
      <span>
        <el-button @click="onAddClose">取消</el-button>
        <el-button type="primary" :loading="addLoading" @click="onAddConfirm"> 确定 </el-button>
      </span>
    </template>
  </el-dialog>

  <el-dialog v-model="showDatabase" title="选择要导入的平台数据库" width="400px" @close="onDatabaseClose">
    <el-radio-group v-model="databaseValue" class="vertical-radio-group">
      <el-radio v-for="item in databaseList" :key="item.id" :value="item.id">
        {{ item.databaseName }}({{ item.address }})
      </el-radio>
    </el-radio-group>

    <template #footer>
      <span>
        <el-button @click="onDatabaseClose">取消</el-button>
        <el-button type="primary" :loading="addLoading" @click="onDatabaseConfirm"> 确定 </el-button>
      </span>
    </template>
  </el-dialog>

  <ImportPlatform :id="selectId" v-model="showSelect" @success="onSelectSuccess" />
</template>

<script setup lang="ts">
  import { findAllDb, newOrUpdateDatabaseVO, checkConnectionDBVOByDbId } from '@/api';
  import { Search } from '@element-plus/icons-vue';
  import { ElMessage, FormInstance } from 'element-plus';
  import { useRouter } from 'vue-router';
  import ImportPlatform from './components/ImportPlatform.vue';

  export type MyDb = CBDDefDatabaseVO & {
    startDate?: string;
    endDate?: string;
  };

  const router = useRouter();
  const loading = ref(false);
  const currentRow = ref<CBDDefDatabaseVO | null>(null);
  const search = ref('');
  const onSearch = () => {
    fetchData();
  };

  //---------新增数据源-----------
  const formRef = ref<FormInstance>();
  const addForm = reactive<MyDb>({
    id: 0, //数据库信息记录Id。新建数据库信息时，值是0
    databaseType: '', //数据库的类型，可与是mysql,sqlserver,postgresql。
    driverClassName: '', //数据库配置驱动
    address: '', //数据库的URL地址，可以连接字+计算机名或IP地址
    port: '', //数据库的端口号
    databaseName: '', //数据库名称
    aliasName: '', //数据库别名
    userName: '', //访问数据库的用户名
    password: '', //访问数据库的口令
    characterEncoding: '', //数据库编码规则
    zeroDateTimeBehavior: '', //测试数据库连接属性文件，若连接异常，则将Java对象转为null
    useSSL: '', //设置安全连接属性
    useJDBCCompliantTimezoneShift: '', //是否使用JDBC中严格的时区转换规则兼容规则
    useLegacyDatetimeCode: '', //是否使用本地时区进行日期时间解析和格式化
    serverTimezone: '', //指定mysql服务的时区
    startDate: '', //生效开始时间
    endDate: '', //效力结束时间
    note: '', //数据库的备注说明信息
  });
  const rules = ref({
    databaseType: [{ required: true, message: '不能为空' }],
    driverClassName: [{ required: true, message: '不能为空' }],
    address: [{ required: true, message: '不能为空' }],
    port: [{ required: true, message: '不能为空' }],
    databaseName: [{ required: true, message: '不能为空' }],
    userName: [{ required: true, message: '不能为空' }],
    password: [{ required: true, message: '不能为空' }],
    characterEncoding: [{ required: true, message: '不能为空' }],
    zeroDateTimeBehavior: [{ required: true, message: '不能为空' }],
    useSSL: [{ required: true, message: '不能为空' }],
    useJDBCCompliantTimezoneShift: [{ required: true, message: '不能为空' }],
    useLegacyDatetimeCode: [{ required: true, message: '不能为空' }],
    startDate: [{ required: true, message: '不能为空' }],
    endDate: [{ required: true, message: '不能为空' }],
  });
  const databaseTypes = ref([
    { label: 'mysql', value: 'mysql' },
    { label: 'sqlserver', value: 'sqlserver' },
    { label: 'postgresql', value: 'postgresql' },
  ]);
  const showAdd = ref(false);
  const addLoading = ref(false);
  const formTitle = computed(() => (addForm.id ? '编辑数据源' : '新增数据源'));

  const onAdd = () => {
    showAdd.value = true;
  };
  const onEdit = (row: CBDDefDatabaseVO) => {
    showAdd.value = true;
    nextTick(() => {
      Object.assign(addForm, row);
    });
  };
  const onAddClose = () => {
    showAdd.value = false;
    formRef.value?.resetFields();
  };
  const onAddConfirm = () => {
    formRef.value?.validate(async (valid) => {
      try {
        if (valid) {
          addLoading.value = true;
          await newOrUpdateDatabaseVO(addForm);
          ElMessage({ type: 'success', message: '操作成功' });
          onAddClose();
          fetchData();
        }
      } catch (error) {
        console.log(error);
      } finally {
        addLoading.value = false;
      }
    });
  };

  //----------数据源列表----------
  const tableData = ref<CBDDefDatabaseVO[]>([]);
  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  const total = ref(0);
  async function fetchData(pageNum = 1) {
    try {
      loading.value = true;
      // let params: AnnotationIDListDTO = {
      //   annotationIDList: [],
      //   pageNum,
      //   pageSize: pagination.pageSize,
      // };
      const { data } = await findAllDb();
      total.value = data?.length || 0;
      tableData.value = data || [];
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }
  const handleCurrentChange = (e) => {
    pagination.page = e;
    fetchData(e);
  };
  //查看详情
  const onViewDetail = (row) => {
    router.push({ name: 'DatabaseField', params: { id: row.id } });
  };
  //删除
  async function onDel(row: CBDDefDatabaseVO) {
    // try {
    //   loading.value = true;
    //   await deleteEntityById_14([row.id!]);
    //   ElMessage({ type: 'success', message: '删除成功' });
    //   fetchData();
    // } catch (error) {
    //   console.log(error);
    // } finally {
    //   loading.value = false;
    // }
  }
  const onTest = async (row: CBDDefDatabaseVO) => {
    try {
      loading.value = true;
      await checkConnectionDBVOByDbId(row.id!);
      ElMessage({ type: 'success', message: '连接数据库成功' });
    } catch (error) {
      console.log('🚀 ~ onTest ~ error:', error);
    } finally {
      loading.value = false;
    }
  };

  //----------导入到数据库--------------
  const showSelect = ref(false);
  const selectId = ref(0);
  const showDatabase = ref(false);
  const databaseValue = ref(0);
  const databaseList = ref<CBDDefDatabaseVO[]>([]);
  const onImport = async (row: CBDDefDatabaseVO) => {
    selectId.value = +row.id!;
    showSelect.value = true;
    // fetchDatabase();
    // databaseValue.value = 0;
    // currentRow.value = row;
    // showDatabase.value = true;
  };
  const onSelectSuccess = () => {};
  const onDatabaseClose = () => {
    showDatabase.value = false;
  };
  async function fetchDatabase() {
    try {
      const { data } = await findAllDb();
      databaseList.value = data!;
    } catch (error) {
      console.log(error);
    }
  }
  const onDatabaseConfirm = async () => {
    // if (!databaseValue.value) {
    //   return ElMessage({ type: 'warning', message: '请选择数据库' });
    // }
    // try {
    //   addLoading.value = true;
    //   //1.导入数据结构schema，sheetIndex必须是0
    //   await importMedicalDataSchemaAndCreateCBDDefDatabase(currentRow.value!.id!, 0, databaseValue.value);
    //   //2.把数据从医学数据集种导入到数据库里
    //   await exportMedicalDataToDatabase(currentRow.value!.id!, databaseValue.value);
    //   ElMessage({ type: 'success', message: '导入成功' });
    //   onDatabaseClose();
    // } catch (error) {
    //   console.log('🚀 ~ onDatabaseConfirm ~ error:', error);
    // } finally {
    //   addLoading.value = false;
    // }
  };

  onBeforeMount(() => {
    fetchData();
  });
</script>

<style lang="scss" scoped>
  .vertical-radio-group {
    display: block;
    .el-radio {
      display: block;
    }
  }

  // .c-table-header {
  //   --el-color-primary: #2979ff;
  // }
</style>
