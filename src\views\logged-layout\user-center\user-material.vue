<template>
  <div v-loading="loading" class="flex h-full flex-col pl-10 pr-4 pt-4">
    <h4 class="text-xl font-bold">账号认证</h4>

    <div>
      <div class="my-5 flex items-center">
        <div class="h-[13px] w-[3px] bg-p" />
        <span class="ml-2">认证状态</span>
      </div>
      <div class="rounded-md bg-bac p-2 text-sm">
        <div>
          <span class="inline-block w-[70px] text-right text-tip">状态：</span>
          {{ auditResult }}
        </div>
        <div v-if="auditResult === '注册审核拒绝'" class="mt-2">
          <span class="inline-block w-[70px] text-tip">处理意见：</span>{{ opinion || '无' }}
        </div>
      </div>
    </div>

    <div class="my-5 flex items-center">
      <div class="h-[13px] w-[3px] bg-p" />
      <span class="ml-2">证明材料</span>
      <span class="ml-2 text-sm text-tip">需上传身份证正反面和工作证</span>
    </div>
    <div v-if="auditResult !== '注册审核通过'" class="mb-4">
      <el-button type="primary" @click="onAddMaterial"> 新增材料 </el-button>
    </div>
    <el-table v-loading="tableLoading" :data="materialData" style="width: 100%" class="c-table-header h-0 flex-1">
      <!-- <el-table-column label="材料">
        <template #default="{ row }">
          <a>示例文件.pdf</a>
        </template>
      </el-table-column> -->
      <el-table-column prop="title" label="材料标题" />
      <el-table-column prop="documentType" label="材料类型" />
      <el-table-column prop="description" label="材料说明" />
      <el-table-column label="上传状态">
        <template #default="{ row }">
          {{ row.contentEmpty ? '未上传' : '已上传' }}
        </template>
      </el-table-column>
      <el-table-column v-if="auditResult !== '注册审核通过'" label="操作">
        <template #default="{ row }">
          <div class="flex items-center">
            <!-- <el-button v-if="row.contentEmpty" link type="primary" @click="onShowUpload(row)"> 上传文件 </el-button> -->
            <UploadButton
              v-if="row.contentEmpty"
              :action="'/attachmentMaterial/uploadAttachmentMaterialId/' + row.id"
              name="attachmentFile"
              :accept="accepts"
              :limit="1"
              @success="onUploadSuccess"
            />
            <el-button link type="primary" @click="onEdit(row)"> 编辑 </el-button>
            <el-popconfirm title="确定删除此项？" @confirm="onDelMaterial(row)">
              <template #reference>
                <el-button link type="primary"> 删除 </el-button>
              </template>
            </el-popconfirm>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <div v-if="auditResult !== '注册审核通过'" class="flex justify-center py-4">
      <el-button color="#007f99" type="primary" :disabled="btnDisable" :loading="saveLoading" @click="onSubmit">
        {{ btnText }}
      </el-button>
    </div>
  </div>

  <el-dialog v-model="showAdd" :title="formTitle" width="500px" @close="onAddClose">
    <el-form ref="formRef" :model="addForm" :rules="rules" label-width="100px">
      <!-- <el-form-item v-if="!addForm.id" label="材料" prop="attachmentFile">
        <UploadButton v-model="addForm.attachmentFile" accept="*" :link="false" :limit="1" :auto-upload="false" />
      </el-form-item> -->
      <el-form-item label="材料标题" prop="title">
        <el-input v-model="addForm.title" placeholder="请输入标题" />
      </el-form-item>
      <el-form-item label="材料类型" prop="documentType">
        <el-select v-model="addForm.documentType" placeholder="请选择材料类型">
          <el-option v-for="item in materialOptions" :key="item" :label="item" :value="item"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="材料说明" prop="description">
        <el-input v-model="addForm.description" placeholder="请输入说明" />
      </el-form-item>
    </el-form>

    <template #footer>
      <span>
        <el-button @click="onAddClose">取消</el-button>
        <el-button type="primary" :loading="addLoading" @click="onAddConfirm"> 确定 </el-button>
      </span>
    </template>
  </el-dialog>

  <!-- <el-dialog v-model="showUpload" title="上传文件" width="500px" @close="onUploadClose">
    <el-form ref="uploadRef" :model="uploadForm" :rules="rules2" label-width="100px">
      <el-form-item label="材料" prop="attachmentFile">
        <UploadButton
          v-model="uploadForm.attachmentFile"
          :accept="accepts"
          :link="false"
          :limit="1"
          :auto-upload="false"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span>
        <el-button @click="onUploadClose">取消</el-button>
        <el-button type="primary" :loading="uploadLoading" @click="onUploadConfirm"> 确定 </el-button>
      </span>
    </template>
  </el-dialog> -->
</template>

<script setup lang="ts">
  import UploadButton from '@/components/UploadButton.vue';
  import {
    findAttachmentMaterialByEnrollmentId,
    setAttachmentMaterialForEnrollment,
    summitEnrollAgain,
    newOrUpdateEntity_7,
    deleteEntityById_7,
    findVerifyResult,
  } from '@/api/index';
  import { upload } from '@/utils/request';
  import { ElMessage, FormInstance, UploadFiles } from 'element-plus';
  import { useUsers } from '@/store/user-info';
  import { mergeObjects } from '@/utils/util';
  const store = useUsers();

  const loading = ref(true);
  //认证结果
  const auditResult = ref('');
  const opinion = ref('');
  async function fetchState() {
    const { data } = await findVerifyResult(store.user.id);
    if (data) {
      auditResult.value = data?.resolution || '';
      opinion.value = data?.description ?? '';
    } else {
      store.user.lockFlag === '1' ? (auditResult.value = '审核中') : '';
    }
  }

  //证明材料
  const materialOptions = ['jpg', 'jpeg', 'png'];
  const accepts = materialOptions.map((item) => '.' + item).join(',');
  const tableLoading = ref(false);
  const btnText = computed(() => {
    let text = '提交材料';
    switch (store.user.lockFlag) {
      case '0':
        text = '已通过';
        break;
      // case '1':
      //   text = '审核中';
      //   break;
      case '3':
        text = '重新提交';
        break;
      case '9':
        text = '账号已锁定';
        break;
    }
    return text;
  });
  const btnDisable = computed(() => {
    let result = false;
    switch (store.user.lockFlag) {
      case '0':
      // case '1':
      case '9':
        result = true;
        break;
    }
    return result;
  });

  const materialData = ref<any[]>([]);
  async function fetchMaterial() {
    const { data } = await findAttachmentMaterialByEnrollmentId(store.user.registerId, 1, 100, {} as any);
    materialData.value = data?.content || [];
  }
  const formRef = ref<FormInstance>();
  const addForm = reactive({
    id: 0,
    title: '',
    documentType: '',
    description: '',
  });

  const rules = ref({
    title: [{ required: true, message: '不能为空' }],
    documentType: [{ required: true, message: '不能为空' }],
    attachmentFile: [{ required: true, message: '不能为空' }],
  });

  const showAdd = ref(false);
  const onAddMaterial = () => {
    addForm.id = 0;
    showAdd.value = true;
  };
  const addLoading = ref(false);
  const onAddClose = () => {
    showAdd.value = false;
    formRef.value?.resetFields();
  };
  const onAddConfirm = () => {
    formRef.value?.validate(async (valid) => {
      try {
        if (valid) {
          addLoading.value = true;
          const { data } = await newOrUpdateEntity_7(addForm);
          await setAttachmentMaterialForEnrollment(store.user.registerId, [data!.id!], {} as any);
          ElMessage({ type: 'success', message: '操作成功' });
          onAddClose();
          fetchMaterial();
        }
      } catch (error) {
        console.log(error);
      } finally {
        addLoading.value = false;
      }
    });
  };
  const onDelMaterial = async (row: AttatchmentMaterialVO) => {
    try {
      tableLoading.value = true;
      await deleteEntityById_7([row.id!]);
      ElMessage({ type: 'success', message: '删除成功' });
      fetchMaterial();
    } catch (error) {
      console.log(error);
    } finally {
      tableLoading.value = false;
    }
  };
  const onEdit = (row) => {
    mergeObjects(addForm, row);
    showAdd.value = true;
  };
  const formTitle = computed(() => (addForm.id ? '编辑材料' : '新增材料'));

  //上传文件
  const rules2 = ref({
    attachmentFile: [{ required: true, message: '不能为空' }],
  });
  const showUpload = ref(false);
  let attachmentMaterialId = 0;
  const uploadRef = ref<FormInstance>();
  const uploadLoading = ref(false);
  const uploadForm = reactive({
    attachmentFile: [] as any[],
  });
  const onShowUpload = (row) => {
    attachmentMaterialId = row.id;
    showUpload.value = true;
  };
  const onUploadClose = () => {
    showUpload.value = false;
    uploadRef.value?.resetFields();
  };
  const onUploadConfirm = () => {
    uploadRef.value?.validate(async (valid) => {
      try {
        if (valid) {
          uploadLoading.value = true;
          // 创建FormData实例
          const formData = new FormData();
          // const jsonBlob1 = new Blob([JSON.stringify(uploadForm.attachmentFile[0])], { type: 'application/json' });
          // formData.append('attachmentFile', jsonBlob1);
          console.log(uploadForm.attachmentFile[0]);
          formData.append('attachmentFile', uploadForm.attachmentFile[0].raw);
          await upload(`/attachmentMaterial/uploadAttachmentMaterialId/${attachmentMaterialId}`, {
            method: 'post',
            data: formData,
          });
          ElMessage({ type: 'success', message: '上传成功' });
          onUploadClose();
          fetchMaterial();
        }
      } catch (error) {
        console.log(error);
      } finally {
        uploadLoading.value = false;
      }
    });
  };
  const onUploadSuccess = () => {
    fetchMaterial();
  };

  //提交申请
  const saveLoading = ref(false);
  const onSubmit = async () => {
    if (materialData.value?.length === 0) {
      ElMessage({ type: 'warning', message: '请先添加证明材料' });
      return;
    }

    try {
      saveLoading.value = true;
      await summitEnrollAgain(store.user.id, {} as any);
      ElMessage({ type: 'success', message: '提交申请成功' });
    } catch (error) {
      console.log(error);
    } finally {
      saveLoading.value = false;
    }
  };

  onBeforeMount(async () => {
    try {
      loading.value = true;
      await Promise.all([fetchState(), fetchMaterial()]);
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  });
</script>

<style lang="scss" scoped>
  .text-required:before {
    content: '*';
    color: var(--el-color-danger);
    margin-right: 4px;
  }
</style>
