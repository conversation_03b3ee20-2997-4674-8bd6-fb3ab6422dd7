import { RouteRecordRaw } from 'vue-router';
import dataResource from './data-resource';
import workbench from '@/router/workbench/index';

// 静态路由
export const staticRoutes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Index',
    component: () => import('@/views/index.vue'),
  },
  //登录注册
  {
    path: '/user',
    redirect: '/user/login',
    component: () => import('@/views/user/user.vue'),
    children: [
      {
        path: 'login',
        name: 'Login',
        component: () => import('@/views/user/login.vue'),
      },
      {
        path: 'forget-name',
        name: 'ForgetName',
        component: () => import('@/views/user/forget-name.vue'),
      },
      {
        path: 'forget-pass',
        name: 'ForgetPass',
        component: () => import('@/views/user/forget-pass.vue'),
      },
      {
        path: 'org-register',
        name: 'OrgRegister',
        component: () => import('@/views/user/org-register.vue'),
      },
      {
        path: 'personal-register',
        name: 'PersonalRegister',
        component: () => import('@/views/user/personal-register.vue'),
      },
      {
        path: 'role-landing',
        name: 'RoleLandingPage',
        component: () => import('@/views/role-landing/index.vue'),
      },
    ],
  },
  {
    path: '/protocol',
    name: 'Protocol',
    component: () => import('@/views/user/protocol.vue'),
  },
  {
    path: '/:pathMatch(.*)*', // 此写法解决动态路由页面刷新的 warning 警告
    component: () => import('@/views/404.vue'),
  },
  //数据资源库
  dataResource,
  //用户中心
  workbench,
  {
    path: '/blank',
    name: 'BlankPage',
    component: () => import('@/views/BlankPage.vue'),
  },
];
