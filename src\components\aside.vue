<template>
  <el-menu unique-opened :default-active="openid" @select="clickaside">
    <div v-for="(item, index) in datalist" :key="item.id" style="width: 100%; height: 100%">
      <el-sub-menu v-if="item.children && item.children.length > 0" :index="item.id">
        <template #title>
          <svgicon style="width: 16px; height: 16px" :icon-name="item.svgname" />
          <span class="ml-4">{{ item.title }}</span>
        </template>
        <div v-for="(citem, cindex) in item.children" :key="citem.id">
          <el-menu-item :index="citem.id">
            <span> {{ citem.title }}</span>
          </el-menu-item>
        </div>
      </el-sub-menu>
      <el-menu-item v-else :index="item.id">
        <svgicon style="width: 16px; height: 16px" :icon-name="item.svgname" />
        <span class="ml-4">{{ item.title }}</span>
      </el-menu-item>
    </div>
  </el-menu>
</template>

<script setup lang="ts">
  import { inject } from 'vue';
  import { useRouter } from 'vue-router';
  let router = useRouter();
  let datalist = inject<any>('datalist');
  let openid = inject<any>('openid');

  let clickaside = (e) => {
    if (e !== openid?.value) {
      datalist.value.forEach((ele) => {
        if (e == ele.id) {
          router.push({ name: ele.pathname });
        }
        if (ele.children && ele.children.length > 0) {
          ele.children.forEach((element) => {
            if (e == element.id) {
              router.push({ name: element.pathname });
            }
          });
        }
      });
    }
  };
</script>

<style scoped lang="scss">
  .el-menu {
    border-right: none;
  }

  :deep(.el-menu-item.is-active) > span {
    font-weight: 900;
    color: #007f99;
  }
  :deep(.el-menu-item.is-active) {
    background-color: #d9ecf0;
  }
  .el-menu :deep(span) {
    font-size: small;
    color: #565b5c;
  }
  :deep(.el-menu-item) {
    color: #565b5c;
    font-size: small;
  }
  .el-sub-menu.is-opened :deep(.el-sub-menu__title) {
    font-weight: bold;
  }
</style>
