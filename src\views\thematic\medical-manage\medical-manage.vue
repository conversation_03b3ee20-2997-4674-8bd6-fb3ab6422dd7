<template>
  <div class="flex h-full w-full flex-col">
    <h2 class="flex h-[60px] items-center bg-w pl-5 text-xl font-semibold">元数据</h2>

    <el-container class="flex h-0 flex-1 p-5">
      <el-main class="rounded bg-w">
        <div class="flex h-full flex-col">
          <div class="flex justify-between px-10 pt-5">
            <div>
              <el-button type="primary" @click="onAdd"> 新增 </el-button>
              <el-button :disabled="checkList.length <= 0" :loading="batchLoading" @click="onBatchDel">
                批量删除
              </el-button>
            </div>

            <div class="flex">
              <el-select
                v-model="valueType"
                placeholder="类型"
                clearable
                class="mr-4"
                style="width: 100px"
                @change="fetchData()"
              >
                <el-option v-for="item in valueTypes" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
              <el-select
                v-model="gendered"
                placeholder="是否按性别区分"
                clearable
                class="mr-4"
                style="width: 150px"
                @change="fetchData()"
              >
                <el-option v-for="item in boolTypes" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
              <el-input v-model="fieldName" placeholder="请输入字段名称" style="width: 300px">
                <template #append>
                  <el-button :icon="Search" @click="fetchData()" />
                </template>
              </el-input>
              <el-button class="ml-3" :icon="Refresh" @click="fetchData()" />
            </div>
          </div>

          <div class="mt-4 h-0 flex-1 px-10">
            <el-table
              v-loading="loading"
              :data="tableData"
              style="width: 100%"
              height="100%"
              class="c-table-header"
              @selection-change="handleSelectionChange"
            >
              <el-table-column type="selection" width="55" />
              <el-table-column prop="name" label="名称" min-width="100px" />
              <el-table-column prop="valueType" label="类型" />
              <el-table-column label="是否按性别区分">
                <template #default="{ row }">
                  <span>{{ row.gendered ? '是' : '否' }}</span>
                </template>
              </el-table-column>
              <!-- <el-table-column label="是否有实例">
                <template #default="{ row }">
                  <span>{{ row.instanced ? '是' : '否' }}</span>
                </template>
              </el-table-column> -->
              <el-table-column prop="description" label="说明" />
              <el-table-column prop="rltTime.updateTime" label="更新时间" />
              <el-table-column label="操作" width="200">
                <template #default="{ row }">
                  <el-button link type="primary" @click="onEdit(row)"> 编辑 </el-button>
                  <el-popconfirm title="确定删除此项？" @confirm="onDel(row)">
                    <template #reference>
                      <el-button link type="primary"> 删除 </el-button>
                    </template>
                  </el-popconfirm>
                  <el-button link type="primary" @click="onConnect(row)"> 关联源数据 </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <div class="pagination-bottom">
            <el-pagination
              background
              layout="total, sizes, prev, pager, next, jumper"
              :current-page="pagination.page"
              :page-size="pagination.pageSize"
              :total="total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </el-main>
    </el-container>
  </div>

  <Drawer :id="id" v-model="showDrawer" :readonly="readonly" @success="onSuccess" />
  <!-- <ConnectDialog v-model="showDialog" @success="onDialogSuccess" /> -->
</template>

<script setup>
  import { findMedicalFieldVOByCriteria, deleteMedicalFieldById } from '@/api/index';
  import Drawer from './components/Drawer.vue';
  // import ConnectDialog from './components/ConnectDialog.vue';
  import { Search, Operation, Refresh } from '@element-plus/icons-vue';
  import { ElMessageBox, ElMessage } from 'element-plus';
  import { valueTypes } from '@/utils/constants';
  import { useRouter } from 'vue-router';
  const router = useRouter();

  const loading = ref(false);
  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  const total = ref(0);
  const handleCurrentChange = (e) => {
    pagination.page = e;
    fetchData(e);
  };
  const handleSizeChange = (e) => {
    pagination.pageSize = e;
    fetchData();
  };

  const fieldName = ref('');
  const valueType = ref('');
  const gendered = ref('');
  const boolTypes = ref([
    {
      label: '是',
      value: true,
    },
    {
      label: '否',
      value: false,
    },
  ]);
  fetchData();

  const tableData = ref([]);
  const checkList = ref([]);
  const handleSelectionChange = (val) => {
    checkList.value = val;
  };

  const onDel = async (row) => {
    try {
      await deleteMedicalFieldById(row.id);
      ElMessage({ type: 'success', message: '删除成功' });
      fetchData();
    } catch (error) {
      console.log(error);
    }
  };
  const batchLoading = ref(false);
  const onBatchDel = () => {
    ElMessageBox.confirm('确定删除所有选中项？', '操作提示', { type: 'warning' })
      .then(async () => {
        try {
          batchLoading.value = true;
          await Promise.all(checkList.value.map((row) => deleteMedicalFieldById(row.id, true)));
          ElMessage({ type: 'success', message: '删除成功' });
          fetchData();
        } catch (error) {
          console.log(error);
        } finally {
          batchLoading.value = false;
        }
      })
      .catch(() => {});
  };

  //编辑
  let id = ref('');
  const readonly = ref(false);
  const showDrawer = ref(false);
  const onAdd = () => {
    id.value = '';
    readonly.value = false;
    showDrawer.value = true;
  };
  const onEdit = (item) => {
    id.value = item.id;
    readonly.value = false;
    showDrawer.value = true;
  };
  const onSuccess = () => {
    fetchData(pagination.page);
  };

  //关联数据源
  // const showDialog = ref(false);
  const onConnect = (row) => {
    // showDialog.value = true;
    router.push({ name: 'ThematicMedicalConnect', params: { id: row.id } });
  };
  const onDialogSuccess = () => {};

  async function fetchData(pageNum = 1) {
    try {
      loading.value = true;
      let params = {
        name: fieldName.value,
        valueType: valueType.value,
        gendered: gendered.value,
        // description: null,
        pageNum,
        pageSize: pagination.pageSize,
      };
      const { data } = await findMedicalFieldVOByCriteria(params);
      total.value = data.totalElement;
      pagination.page = pageNum;
      tableData.value = data.content;
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }
</script>

<style lang="scss" scoped>
  .el-main {
    --el-main-padding: 0;
  }
</style>
