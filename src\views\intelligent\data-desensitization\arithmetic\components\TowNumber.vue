<template>
  <div class="flex">
    <div>
      <span class="mr-2">n=</span>
      <el-input-number
        v-model="num1"
        :min="min"
        :max="max"
        controls-position="right"
        :disabled="disabled"
        @change="onChange"
      />
    </div>
    <div class="ml-5">
      <span class="mr-2">m=</span>
      <el-input-number
        v-model="num2"
        :min="min"
        :max="max"
        controls-position="right"
        :disabled="disabled"
        @change="onChange"
      />
    </div>
  </div>
</template>

<script setup>
  const props = defineProps({
    modelValue: {
      type: String,
    },
    min: {
      type: Number,
      default: 0,
    },
    max: {
      type: Number,
      default: 99999,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  });

  const num1 = ref(0);
  const num2 = ref(0);

  watch(
    () => props.modelValue,
    (value) => {
      if (value) {
        let result = value.split(',');
        num1.value = Number(result[0]) || 0;
        num2.value = Number(result[1]) || 0;
      } else {
        num1.value = 0;
        num2.value = 0;
      }
    },
    {
      immediate: true,
    }
  );

  const emit = defineEmits(['update:modelValue']);
  const onChange = () => {
    emit('update:modelValue', `${num1.value},${num2.value}`);
  };
</script>
