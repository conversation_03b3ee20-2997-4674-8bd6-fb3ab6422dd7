import { isRef, isReactive } from 'vue';
import { ElMessage } from 'element-plus';

//自定义消息提示函数
export function elmes(type, text) {
  if (type == 's') {
    ElMessage({
      showClose: true,
      message: text,
      type: 'success',
    });
  } else {
    if (type == 'f') {
      ElMessage({
        showClose: true,
        message: text,
        type: 'error',
      });
    } else {
      ElMessage({
        showClose: true,
        message: text,
        type: 'warning',
      });
    }
  }
}

export function isEmpty(value) {
  if (value === null || value === undefined) {
    return true; // 空值情况
  }

  if (isRef(value)) {
    return isEmpty(value.value); // 处理 ref
  }

  if (isReactive(value)) {
    return Object.keys(value).length === 0; // 处理 reactive
  }

  if (Array.isArray(value) || typeof value === 'string') {
    return value.length === 0; // 处理数组和字符串
  }

  if (typeof value === 'object') {
    return Object.keys(value).length === 0; // 处理普通对象
  }

  return false; // 其他情况，视为非空值
}

//在多维数组中查找相关属性的符合条件值的对象，并且返回该对象

export function findObjectWithPropertyValue(obj, propertyName, targetValue) {
  //obj多维数组
  if (Object.prototype.hasOwnProperty.call(obj, propertyName) && obj[propertyName] === targetValue) {
    return obj;
  }

  for (const key in obj) {
    if (typeof obj[key] === 'object' && obj[key] !== null) {
      const nestedResult = findObjectWithPropertyValue(obj[key], propertyName, targetValue);
      if (nestedResult) {
        return nestedResult;
      }
    }
  }

  return null;
}

//在对象数组中搜索符合条件的对象，以数组形式返回
export function filterObjectsByPropertyValue(array, propertyName, targetValue) {
  return array.filter((item) => item[propertyName] === targetValue);
}

//深拷贝
export function deepCopy(source) {
  if (source === null || typeof source !== 'object') return source;

  const target = Array.isArray(source) ? [] : {};

  for (const key in source) {
    if (Object.prototype.hasOwnProperty.call(source, key)) {
      target[key] = deepCopy(source[key]);
    }
  }

  return target;
}

//浅拷贝
export function shallowCopy(source) {
  if (source === null || typeof source !== 'object') return source;

  const target = Array.isArray(source) ? [] : {};

  for (const key in source) {
    if (Object.prototype.hasOwnProperty.call(source, key)) {
      target[key] = source[key];
    }
  }

  return target;
}

//在多维对象数组中寻找到相应的对象并且修改其指定的属性为指定的值

export function findAndModify(data, searchProperty, searchValue, modifyProperty, modifyValue) {
  if (typeof data === 'object' && data !== null) {
    if (Array.isArray(data)) {
      // 处理数组
      for (let i = 0; i < data.length; i++) {
        data[i] = findAndModify(data[i], searchProperty, searchValue, modifyProperty, modifyValue);
      }
    } else {
      // 处理对象
      if (Object.prototype.hasOwnProperty.call(data, searchProperty) && data[searchProperty] === searchValue) {
        data[modifyProperty] = modifyValue; // 找到匹配的对象，修改其指定属性为指定的值
      }

      for (const key in data) {
        data[key] = findAndModify(data[key], searchProperty, searchValue, modifyProperty, modifyValue); // 递归搜索子项
      }
    }
  }
  return data;
}
