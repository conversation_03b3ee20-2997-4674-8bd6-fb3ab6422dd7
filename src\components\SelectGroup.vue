<template>
  <div class="c-select-group">
    <el-select v-model="select1" :placeholder="placeholder1" style="width: 165px" @change="onChange1">
      <el-option v-for="(item, index) in option1" :key="index" :label="item.label" :value="item.value" />
    </el-select>
    <div class="h-[30px] w-[1px] bg-border" />
    <el-select v-model="select2" :placeholder="placeholder2" class="flex-1" @change="onChange">
      <el-option v-for="(item, index) in option2" :key="index" :label="item.label" :value="item.value" />
    </el-select>
  </div>
</template>

<script setup>
  const props = defineProps({
    modelValue: { type: String },
    placeholder1: { type: String, default: '请选择' },
    placeholder2: { type: String, default: '请选择' },
    // option1: {
    //   type: Array,
    //   default() {
    //     return [];
    //   },
    // },
    // option2: {
    //   type: Array,
    //   default() {
    //     return [];
    //   },
    // },
  });
  const option1 = ref([
    {
      label: 'oracle',
      value: 'oracle',
    },
    {
      label: 'mysql',
      value: 'mysql',
    },
  ]);
  const option2 = ref([
    {
      label: 'zw_test_esaeynous',
      value: 'zw_test_esaeynous',
    },
  ]);

  const select1 = ref('');
  const select2 = ref('');
  watchEffect(() => {
    const temp = props.modelValue.split(',');
    if (temp?.length) {
      select1.value = temp[0];
      select2.value = temp[1] || '';
    }
  });

  const emit = defineEmits(['update:modelValue']);
  const onChange = () => {
    emit('update:modelValue', select1.value + ',' + select2.value);
  };
  const onChange1 = () => {
    //重新加载option2数据
    onChange();
  };
</script>

<style lang="scss" scoped>
  .c-select-group {
    width: 480px;
    display: flex;
    align-items: center;
    border: 1px solid var(--el-border-color);
    border-radius: var(--el-input-border-radius, var(--el-border-radius-base));
  }
  .c-is-foucus {
    border-color: $color-primary;
  }
  .el-form-item.is-error {
    .c-select-group {
      border-color: var(--el-color-danger);
    }
  }

  :deep(.el-select) {
    --el-select-border-color-hover: #fff;
    --el-color-danger: #fff;
    .el-input__wrapper {
      box-shadow: none !important;
      border-radius: none;
    }
    .el-input__wrapper.is-focus {
      box-shadow: 0 0 0 0px !important;
    }
    .el-input.is-focus .el-input__wrapper {
      box-shadow: 0 0 0 0px !important;
    }
    .el-input__inner {
      --el-input-inner-height: 28px;
    }
    .el-input__inner:focus {
      outline: none;
    }
  }

  :deep(.el-form-item.is-error .el-input__wrapper.is-focus) {
    box-shadow: none !important;
  }
  :deep(.el-form-item.is-error .el-select .el-input.is-focus .el-input__wrapper) {
    box-shadow: none !important;
  }
</style>
