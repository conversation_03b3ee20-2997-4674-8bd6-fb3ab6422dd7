/*
 * @OriginalName: 数据资源系统中数据源管理模块
 * @Description: 数据源管理，管理医学数据的原始数据库
 */
import { request } from '@/utils/request';

/**
 * 新建或更新数据源的数据库的管理信息
 * @description 根据CBDDefDatabaseVO中的信息，在数据库的CBDDefDatabase表中若已存在ID相同的记录，则更新表中的相应记录；不存在则添加一条新记录。
 */
export function newOrUpdateDatabaseVO(data: CBDDefDatabaseDTO) {
  return request<RCBDDefDatabaseVO>(`/dataSource/newOrUpdateDatabase`, {
    method: 'post',
    data,
  });
}

/**
 * 向数据库内添加或更新一个数据表的管理信息
 * @description 根据tableDTO中的信息,新增或更新CBDDefTable表中的记录。在数据库中已经存在同名数据表，则更新；不存在,则添加一条新记录。
 */
export function addTable(data: CBDDefTableDTO) {
  return request<RCBDDefTableVO>(`/dataSource/addTable`, {
    method: 'post',
    data,
  });
}

/**
 * 向数据库内添加多个数据表的管理信息
 * @description 根据tableDTO中的信息,新增或更新CBDDefTable表中的记录。在数据库中已经存在同名数据表，则更新；不存在,则添加一条新记录。
 */
export function addTable_1(data: Array<CBDDefTableDTO>) {
  return request<Array<R>>(`/dataSource/addTableArray`, {
    method: 'post',
    data,
  });
}

/**
 * 向数据表内添加一个数据字段的管理信息
 * @description 根据fieldDTO中的信息,新增或更新CBDDefField表中的记录。在数据表中若已存在同名的字段，则更新；不存在,则添加一条新记录。
 */
export function addField(data: CBDDefFieldDTO) {
  return request<RCBDDefFieldVO>(`/dataSource/addField`, {
    method: 'post',
    data,
  });
}

/**
 * 向数据表内添加多个数据字段的管理信息
 * @description 根据fieldDTO中的信息,新增或更新CBDDefField表中的记录。在数据表中若已存在同名的字段，则更新；不存在,则添加一条新记录。
 */
export function addField_1(data: Array<CBDDefFieldDTO>) {
  return request<Array<R>>(`/dataSource/addFieldArray`, {
    method: 'post',
    data,
  });
}

/**
 * 同步数据库数据容量信息
 * @description 把数据源中的数据容量数据同步到数据库管理信息中。返回负数，表示同步失败。
 */
export function synchronizeVolume(dbId: number, params?: { dbId: number }) {
  return request<RDouble>(`/dataSource/synchronizeVolume/${dbId}`, {
    method: 'get',
    params,
  });
}

/**
 * 查看数据源的数据库的管理信息
 * @description 按数据库Id，精确查找数据库信息
 */
export function findDBVOByDbId(dbId: number, params?: { dbId: number }) {
  return request<RCBDDefDatabaseVO>(`/dataSource/findDBVOByDbId/${dbId}`, {
    method: 'get',
    params,
  });
}

/**
 * 访问数据库中的数据表的管理信息
 * @description 按数据库id，查询该数据库内的所有数据表
 */
export function findAllTableVOByDbId(dbId: number, params?: { dbId: number }) {
  return request<RList>(`/dataSource/findAllTableVOByDbId/${dbId}`, {
    method: 'get',
    params,
  });
}

/**
 * 访问数据库中一个数据表内的所有字段的管理信息
 * @description 按数据库id和数据表id，查询该数据表内的所有数据字段
 */
export function findAllFieldVOByDbIdTblId(dbId: number, tblId: number, params?: { dbId: number; tblId: number }) {
  return request<RList>(`/dataSource/findAllFieldVOByDbIdTblId/${dbId}/${tblId}`, {
    method: 'get',
    params,
  });
}

/**
 * 查看全部数据源的数据库的管理信息
 * @description 获取全部数据源的数据库信息
 */
export function findAllDb() {
  return request<RListCBDDefDatabaseVO>(`/dataSource/findAllDb`, {
    method: 'get',
  });
}

/**
 * 删除数据库中的一个数据表的管理信息
 * @description 根据CBDDefTable主键ID,删除一条记录，isObligated为true,则强制删除。
 */
export function deleteTableById(id: number, isObligated: boolean, params?: { id: number; isObligated: boolean }) {
  return request<R>(`/dataSource/deleteTableById/${id}/${isObligated}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除数据表中的一个字段的管理信息
 * @description 根据CBDDefField主键ID,删除一条记录
 */
export function deleteFieldById(id: number, params?: { id: number }) {
  return request<R>(`/dataSource/deleteFieldById/${id}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除一个数据源的数据库的管理信息
 * @description 根据CBDDefDatabase主键ID,删除一条记录，isObligated为true,则强制删除。
 */
export function deleteDatabaseById(id: number, isObligated: boolean, params?: { id: number; isObligated: boolean }) {
  return request<R>(`/dataSource/deleteDatabaseById/${id}/${isObligated}`, {
    method: 'get',
    params,
  });
}

/**
 * 测试数据源的数据库连接
 * @description 按数据库Id，测试数据库的连接是否正常
 */
export function checkConnectionDBVOByDbId(dbId: number, params?: { dbId: number }) {
  return request<R>(`/dataSource/checkConnectionDBVOByDbId/${dbId}`, {
    method: 'get',
    params,
  });
}
