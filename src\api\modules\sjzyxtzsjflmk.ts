/*
 * @OriginalName: 数据资源系统中数据分类模块
 * @Description: 管理数据分类，实现对数据类别的管理
 */
import { request } from '@/utils/request';

/**
 * 从一个分类中卸载一个或多个医学字段
 * @description 根据分类主键ctlgId,将主键为mfId的医学数据字段从ctlgId代表的分类中卸载,返回被卸载的数据。
 */
export function unmountMedicalFieldFromCatalogue(data: Array<CatalogueMedicalFieldDTO>) {
  return request<RListMedicalFieldVO>(`/catalogue/unmountMedicalFieldFromCatalogue`, {
    method: 'post',
    data,
  });
}

/**
 * 新建或更新一个分类
 * @description 根据分类主键，若数据库中已存在该分类，则更新分类信息，若不存在，新建一个分类。
 */
export function newOrUpdateCatalogue(data: CatalogueDTO) {
  return request<RCatalogueVO>(`/catalogue/newOrUpdateCatalogueVo`, {
    method: 'post',
    data,
  });
}

/**
 * 挂接医学字段到分类中
 * @description 根据分类主键ctlgId,将主键为mfId的医学数据字段挂接到ctlgId代表的分类。endDate时间为null，表示永远
 */
export function mountMedicalFieldToCatalogue(data: Array<CatalogueMedicalFieldDTO>) {
  return request<RListMedicalFieldVO>(`/catalogue/mountMedicalFieldToCatalogue`, {
    method: 'post',
    data,
  });
}

/**
 * 访问数据分类
 * @description 以分类id号获取数据分类展示对象
 */
export function getCatalogue(id: number, params?: { id: number }) {
  return request<RCatalogueVO>(`/catalogue/${id}`, {
    method: 'get',
    params,
  });
}

/**
 * 访问基础数据分类的顶层目录
 * @description 访问基础数据分类的顶层分类，以列表形式返回结果
 */
export function getTopBasicCatalogues() {
  return request<RListCatalogueVO>(`/catalogue/topBasicCatalogues`, {
    method: 'get',
  });
}

/**
 * 访问数据字段
 * @description 以分类id号获取该数据分类内的医学字段展示对象
 */
export function getMedicalFieldInCatalogue(id: number, params?: { id: number }) {
  return request<RListMedicalFieldVO>(`/catalogue/medicalFieldInCatalogue/${id}`, {
    method: 'get',
    params,
  });
}

/**
 * 访问父数据分类
 * @description 以分类id号获取该数据分类的父分类展示对象
 */
export function getParentCatalogue(id: number, params?: { id: number }) {
  return request<RCatalogueVO>(`/catalogue/getParentCatalogue/${id}`, {
    method: 'get',
    params,
  });
}

/**
 * 访问子数据分类
 * @description 以分类id号获取该数据分类的子分类展示对象
 */
export function getChildCatalogue(id: number, params?: { id: number }) {
  return request<RListCatalogueVO>(`/catalogue/getChildCatalogue/${id}`, {
    method: 'get',
    params,
  });
}

/**
 * 访问数据分类
 * @description 以分类类型获取数据分类展示对象
 */
export function getCatalogueByType(params?: { type: string }) {
  return request<RListCatalogueVO>(`/catalogue/getCatalogueByType`, {
    method: 'get',
    params,
  });
}

/**
 * 访问数据分类
 * @description 以分类名称获取数据分类展示对象
 */
export function getCatalogueByTitle(title: string, params?: { title: string }) {
  return request<RListCatalogueVO>(`/catalogue/getCatalogueByTitle/${title}`, {
    method: 'get',
    params,
  });
}

/**
 * 访问兄弟数据分类
 * @description 以分类id号获取该数据分类的兄弟分类展示对象
 */
export function getBrotherCatalogue(id: number, params?: { id: number }) {
  return request<RListCatalogueVO>(`/catalogue/getBrotherCatalogue/${id}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除一个分类
 * @description 根据分类主键ID,删除一条记录，isObligated为true,则强制删除。
 */
export function deleteCatalogue(id: number, isObligated: boolean, params?: { id: number; isObligated: boolean }) {
  return request<R>(`/catalogue/deleteCatalogue/${id}/${isObligated}`, {
    method: 'get',
    params,
  });
}

/**
 * 获取chart图
 * @description 数据浏览模块内的统计图数据
 */
export function dataBrowse() {
  return request<RDataBrowseChartVO>(`/catalogue/dataBrowse/chart`, {
    method: 'get',
  });
}
