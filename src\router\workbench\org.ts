/*
 * @Description:工作台-机构用户
 */

export default {
  path: '/org',
  redirect: 'org/index',
  component: () => import('@/views/logged-layout/index.vue'),
  children: [
    //首页
    {
      path: 'index',
      name: 'Org',
      component: () => import('@/views/workbench/org/index.vue'),
    },
    //审批中心
    {
      path: 'approve',
      name: 'OrgApprove',
      component: () => import('@/views/workbench/org/approve/index.vue'),
      props: (route) => ({ id: route.query.id }),
    },
    {
      path: 'menu',
      component: () => import('@/views/workbench/org/org-menu.vue'),
      children: [
        //机构信息
        {
          path: 'org-info',
          name: 'OrgInfo',
          component: () => import('@/views/workbench/org/org-info/index.vue'),
        },
        //人员管理
        {
          path: 'person-manage',
          name: 'OrgPersonManage',
          component: () => import('@/views/workbench/org/person-manage/index.vue'),
        },
        // //数据管理
        // {
        //   path: 'data-manage',
        //   name: 'OrgDataManage',
        //   component: () => import('@/views/workbench/org/data-manage/index.vue'),
        // },
        // //数据详情
        // {
        //   path: 'data-detail',
        //   name: 'OrgDataDetail',
        //   component: () => import('@/views/workbench/org/data-manage/detail.vue'),
        //   props: (route) => ({ id: route.query.id }),
        // },
      ],
    },
  ],
};
