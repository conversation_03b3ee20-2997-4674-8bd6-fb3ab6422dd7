<template>
  <div>
    <!-- 机构用户抽屉 -->
    <el-drawer v-model="showUsers" :title="'机构用户 - ' + currentOrg?.name" size="60%" destroy-on-close>
      <div class="m-5 flex flex-col" v-loading="loading">
        <div class="mb-4 flex gap-4">
          <el-button type="primary" @click="onAddUser">添加用户</el-button>
          <el-button type="danger" :disabled="selectedRows.length === 0" @click="onBatchRemoveUser">批量删除</el-button>
        </div>
        <el-table
          :data="userTableData"
          style="width: 100%"
          class="c-table-header"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column type="index" width="60" label="序号" />
          <el-table-column prop="userName" label="用户名" />
          <el-table-column prop="name" label="姓名" />
          <el-table-column prop="orgName" label="机构名称" />
          <el-table-column prop="updateTime" label="更新时间">
            <template #default="{ row }">
              <div>{{ row.rltTime?.updateTime || '' }}</div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100px">
            <template #default="{ row }">
              <el-popconfirm title="确定移除该用户？" @confirm="onRemoveUser(row)">
                <template #reference>
                  <div>
                    <el-tooltip content="移除" effect="dark">
                      <el-button link icon="delete" type="primary" />
                    </el-tooltip>
                  </div>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-drawer>

    <!-- 添加机构用户对话框 -->
    <el-dialog v-model="showAddUser" title="添加用户到机构" width="800px" destroy-on-close @close="onAddUserClose">
      <div class="mb-4 flex gap-4">
        <el-input
          v-model="userNameFilter"
          placeholder="用户名"
          style="width: 200px"
          clearable
          @clear="onUserSearch"
          @keyup.enter="onUserSearch"
        />
        <el-input
          v-model="nameFilter"
          placeholder="姓名"
          style="width: 200px"
          clearable
          @clear="onUserSearch"
          @keyup.enter="onUserSearch"
        />
        <div>
          <el-button type="default" @click="onUserSearch">查询</el-button>
        </div>
      </div>

      <el-table
        v-loading="userLoading"
        :data="userList"
        style="width: 100%"
        row-key="id"
        @selection-change="handleUserSelectionChange"
      >
        <el-table-column type="selection" width="55" reserve-selection />
        <el-table-column prop="userName" label="用户名" />
        <el-table-column prop="name" label="姓名" />
        <el-table-column prop="phone" label="电话" />
        <el-table-column prop="email" label="邮箱" />
      </el-table>

      <div class="pagination-bottom">
        <el-pagination
          background
          layout="total, prev, pager, next, jumper"
          :page-size="userPagination.pageSize"
          :total="userTotal"
          @current-change="handleUserPageChange"
        />
      </div>

      <template #footer>
        <span>
          <el-button @click="onAddUserClose">取消</el-button>
          <el-button type="primary" :loading="addUserLoading" @click="onAddUserConfirm"> 确定 </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { findUserVOByCriteria, findUserByOrgId, newOrUpdateOrgUser, deleteOrgUserById } from '@/api';
  import { ElMessage } from 'element-plus';

  const props = defineProps<{
    currentOrg: OrgVO | null;
  }>();

  // 显示用户抽屉
  const showUsers = ref(false);
  const userTableData = ref<OrgUserVO[]>([]);
  const loading = ref(false);
  const selectedRows = ref<OrgUserVO[]>([]);

  // 用户添加相关
  const showAddUser = ref(false);
  const addUserLoading = ref(false);
  const userLoading = ref(false);
  const userNameFilter = ref('');
  const nameFilter = ref('');
  const userList = ref<MDMUserVO[]>([]);
  const userTotal = ref(0);
  const selectedUserIds = ref<number[]>([]);

  // 用户分页
  const userPagination = reactive({
    page: 1,
    pageSize: 10,
  });

  /**
   * 打开用户管理抽屉
   */
  const open = async (org: OrgVO) => {
    showUsers.value = true;
    await fetchOrgUsers(org.id);
  };

  /**
   * 获取机构用户列表
   */
  async function fetchOrgUsers(orgId?: number) {
    try {
      loading.value = true;
      if (props.currentOrg?.id || orgId) {
        const { data } = await findUserByOrgId(props.currentOrg?.id || orgId!);
        userTableData.value = data || [];
      }
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  /**
   * 表格多选变化
   */
  const handleSelectionChange = (selection: OrgUserVO[]) => {
    selectedRows.value = selection;
  };

  /**
   * 批量移除机构用户
   */
  async function onBatchRemoveUser() {
    try {
      if (selectedRows.value.length === 0) {
        ElMessage({ type: 'warning', message: '请至少选择一条记录' });
        return;
      }

      const orgUserIds = selectedRows.value.filter((row) => row.orgUserId).map((row) => row.orgUserId!);

      if (orgUserIds.length === 0) {
        ElMessage({ type: 'warning', message: '未找到有效的用户关联ID' });
        return;
      }

      loading.value = true;
      await deleteOrgUserById(orgUserIds);
      ElMessage({ type: 'success', message: '批量删除成功' });
      await fetchOrgUsers();
      selectedRows.value = [];
    } catch (error) {
      console.log(error);
      ElMessage({ type: 'error', message: '批量删除失败' });
    } finally {
      loading.value = false;
    }
  }

  /**
   * 移除机构用户
   */
  async function onRemoveUser(row: OrgUserVO) {
    try {
      loading.value = true;
      if (row.orgUserId) {
        await deleteOrgUserById([row.orgUserId]);
        ElMessage({ type: 'success', message: '移除成功' });
        await fetchOrgUsers();
      }
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  /**
   * 打开添加用户对话框
   */
  const onAddUser = () => {
    userPagination.page = 1;
    showAddUser.value = true;
    fetchUserList();
  };

  /**
   * 关闭添加用户对话框
   */
  const onAddUserClose = () => {
    showAddUser.value = false;
    userNameFilter.value = '';
    nameFilter.value = '';
    selectedUserIds.value = [];
  };

  /**
   * 获取用户列表
   */
  async function fetchUserList() {
    try {
      userLoading.value = true;
      const { data } = await findUserVOByCriteria({
        userName: userNameFilter.value,
        name: nameFilter.value,
        pageNum: userPagination.page,
        pageSize: userPagination.pageSize,
      });
      userTotal.value = data?.totalElement || 0;
      userList.value = data?.content || [];
    } catch (error) {
      console.log(error);
    } finally {
      userLoading.value = false;
    }
  }

  /**
   * 用户查询
   */
  const onUserSearch = () => {
    userPagination.page = 1;
    fetchUserList();
  };

  /**
   * 用户分页变化
   */
  const handleUserPageChange = (page: number) => {
    userPagination.page = page;
    fetchUserList();
  };

  /**
   * 用户表格选择变化
   */
  const handleUserSelectionChange = (val: MDMUserVO[]) => {
    selectedUserIds.value = val.map((item) => item.id!); //只保留id
  };

  /**
   * 提交添加用户表单
   */
  const onAddUserConfirm = async () => {
    try {
      if (selectedUserIds.value.length === 0) {
        ElMessage({ type: 'warning', message: '请至少选择一个用户' });
        return;
      }

      if (!props.currentOrg?.id) {
        ElMessage({ type: 'warning', message: '未找到当前机构' });
        return;
      }

      addUserLoading.value = true;

      // 创建批量添加用户的数据数组
      const orgUserDTOs = selectedUserIds.value.map((userId) => ({
        orgUserId: {
          orgId: props.currentOrg!.id,
          userId: userId,
        },
      }));

      // 批量添加用户
      await newOrUpdateOrgUser(orgUserDTOs);

      ElMessage({ type: 'success', message: '添加用户成功' });
      onAddUserClose();
      await fetchOrgUsers();
    } catch (error) {
      console.log(error);
      ElMessage({ type: 'error', message: '添加用户失败' });
    } finally {
      addUserLoading.value = false;
    }
  };

  // 对外暴露方法
  defineExpose({
    open,
    showUsers,
  });
</script>
