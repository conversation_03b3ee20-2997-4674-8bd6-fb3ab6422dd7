import * as echarts from 'echarts';

let textStyle = {
  color: '#303333',
  fontSize: 16,
};

let xAxis = {
  axisTick: {
    show: false,
  },
  axisLabel: {
    color: '#939899',
  },
  axisLine: {
    lineStyle: {
      color: '#C8C9CC',
    },
  },
};

export const line = {
  title: {
    text: '',
    textStyle,
  },
  tooltip: {
    trigger: 'axis',
    confine: true,
    axisPointer: {
      lineStyle: {
        type: 'solid',
        color: '#E1E3E6',
      },
    },
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    boundaryGap: true,
    data: [],
    axisTick: {
      show: false,
    },
    axisLabel: {
      color: '#939899',
    },
    axisLine: {
      lineStyle: {
        color: '#C8C9CC',
      },
    },
  },
  yAxis: {
    type: 'value',
    min: 0,
    axisLabel: {
      color: '#939899',
    },
  },
  color: ['rgb(19, 161, 191)', 'rgb(243, 164, 84)', 'rgb(118, 132, 161)', 'rgb(92, 137, 230)'],
  series: [
    // {
    //   name: '图例1',
    //   type: 'line',
    //   data: [120, 119, 119, 119, 118, 124, 129, 142, 149, 170, 176, 182],
    //   symbol: 'circle',
    //   showSymbol: false,
    // }
  ],
};

export const bar = {
  title: {
    text: '',
    textStyle,
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true,
  },
  xAxis: [
    {
      type: 'category',
      data: [],
      boundaryGap: true,
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#939899',
      },
      axisLine: {
        lineStyle: {
          color: '#C8C9CC',
        },
      },
    },
  ],
  yAxis: [
    {
      type: 'value',
      axisLabel: {
        color: '#939899',
      },
    },
  ],
  color: ['rgb(30, 165, 194)'],
  series: [
    // {
    //   name: 'Direct',
    //   type: 'bar',
    //   barWidth: '50%',
    //   data: [180, 322, 120, 444, 180, 227],
    // },
  ],
};

export const pie = {
  title: {
    text: '',
    textStyle,
  },
  tooltip: {
    trigger: 'item',
  },
  series: [
    {
      type: 'pie',
      radius: '50%',
      data: [
        // { value: 7, name: '图例一' },
        // { value: 35, name: '图例二' },
        // { value: 40, name: '图例三' },
        // { value: 20, name: '图例四' },
        // { value: 15, name: '图例五' },
      ],
      label: {
        color: '#939899',
      },
      itemStyle: {
        color: function (colors) {
          let colorList = ['#2eb8e6', '#5c89e6', '#13a1bf', '#29ccb0', '#907fe6'];
          return colorList[colors.dataIndex];
        },
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)',
        },
      },
    },
  ],
};

export const barLine = {
  title: {
    text: '',
    textStyle,
  },
  tooltip: {
    trigger: 'axis',
  },
  grid: {
    right: '20%',
  },
  xAxis: [
    {
      type: 'category',
      data: [],
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#939899',
      },
      axisLine: {
        lineStyle: {
          color: '#C8C9CC',
        },
      },
    },
  ],
  yAxis: [
    {
      type: 'value',
      position: 'right',
      alignTicks: true,
      axisLine: {
        lineStyle: {
          color: '#2b2b2c',
        },
      },
      axisLabel: {
        formatter: '{value}%',
        color: '#939899',
      },
    },
    {},
    {
      type: 'value',
      position: 'left',
      alignTicks: true,
      axisLine: {
        lineStyle: {
          color: '#2b2b2c',
        },
      },
      axisLabel: {
        formatter: '{value}k',
        color: '#939899',
      },
    },
  ],
  series: [
    {
      name: 'Evaporation',
      type: 'bar',
      barWidth: '60%',
      data: [],
      color: '#1ea5c2',
    },
    {
      name: 'Temperature',
      type: 'line',
      yAxisIndex: 2,
      symbol: 'none',
      smooth: true,
      data: [],
      color: '#5c89e6',
    },
  ],
};

export const lineArea = {
  title: {
    text: '',
    textStyle,
  },
  xAxis: {
    type: 'category',
    boundaryGap: true,
    data: [],
    ...xAxis,
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: '{value}k',
      color: '#939899',
    },
  },
  tooltip: {
    trigger: 'axis',
  },
  series: [
    {
      data: [],
      symbol: 'none',
      type: 'line',
      color: '#13a1bf',
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: '#6ccee3',
          },
          {
            offset: 1,
            color: 'white',
          },
        ]), // 背景渐变色
      },
    },
    {
      data: [],
      symbol: 'none',
      type: 'line',
      color: '#5c89e6',
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: '#9ebdfe',
          },
          {
            offset: 1,
            color: 'white',
          },
        ]), // 背景渐变色
      },
    },
  ],
};

// const dataBJ = [
//   [32, 20, 19],
//   [34, 7, 4],
//   [30, 12, 166],
//   [36, 10, 19],
//   [37, 13, 21],
//   [38, 15, 66],
//   [40, 12, 40],
//   [42, 10, 22],
//   [44, 21, 16],
// ];
// const dataGZ = [
//   [31, 7, 27],
//   [34.8, 14, 8],
//   [37, 17, 42],
//   [40, 7, 68],
//   [44, 15, 32],
//   [45.4, 19.4, 11],
//   [48, 17, 54],
// ];
// const dataSH = [
//   [27, 20, 35],
//   [34, 17, 15],
//   [37, 7, 20],
//   [42, 20.8, 12],
//   [42.6, 17.5, 28],
//   [43.5, 17.8, 34],
//   [46, 13, 59],
//   [47, 7, 13],
// ];
const itemStyle = {
  opacity: 0.8,
};
export const scatter = {
  title: {
    text: '',
    textStyle,
  },
  color: ['#9ed7e2', '#c1d0f1', '#f4d3b2'],
  grid: {
    left: '10%',
    right: '10%',
    top: '18%',
    bottom: '10%',
  },
  tooltip: {
    backgroundColor: 'rgba(255,255,255,0.7)',
  },
  xAxis: {
    type: 'value',
    min: 25,
    ...xAxis,
  },
  yAxis: {
    type: 'value',
    max: 25,
    axisTick: {
      show: false,
    },
    axisLabel: {
      color: '#939899',
    },
    axisLine: {
      lineStyle: {
        color: '#C8C9CC',
      },
    },
  },
  visualMap: [
    {
      show: false,
      dimension: 2,
      min: 0,
      itemWidth: 0,
      itemHeight: 0,
      max: 250,
      inRange: {
        symbolSize: [5, 100],
      },
    },
  ],
  series: [
    {
      type: 'scatter',
      itemStyle: itemStyle,
      data: [],
    },
    {
      type: 'scatter',
      itemStyle: itemStyle,
      data: [],
    },
    {
      type: 'scatter',
      itemStyle: itemStyle,
      data: [],
    },
  ],
};
