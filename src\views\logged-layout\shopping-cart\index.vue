<template>
  <div class="flex h-full flex-col bg-w">
    <h2 class="flex cursor-pointer items-center px-[28px] pt-4 text-xl font-bold" @click="onBackHome">
      <el-icon class="mr-2" color="#939899">
        <ArrowLeft />
      </el-icon>
      购物车
    </h2>

    <el-tabs v-model="activeName" class="tabs mt-3 flex h-0 flex-1 flex-col">
      <el-tab-pane label="待支付" name="first">
        <div class="h-full px-[28px] py-4">
          <div class="flex h-full flex-col bg-w p-4">
            <el-table
              :data="tableData"
              class="h-0 w-full flex-1"
              style="width: 100%"
              height="100%"
              @selection-change="handleSelectionChange"
            >
              <el-table-column type="selection" width="55" />
              <el-table-column label="ID" prop="longID" />
              <el-table-column label="题目" prop="name" />
              <el-table-column label="提交时间" prop="time" />
              <el-table-column label="价格" prop="price">
                <template #default="{ row }"> ￥{{ row.price }} </template>
              </el-table-column>
              <el-table-column label="操作" width="200">
                <template #default="{ row }">
                  <el-popconfirm title="确定删除此项？" @confirm="onDel(row)">
                    <template #reference>
                      <el-button link type="primary"> 删除 </el-button>
                    </template>
                  </el-popconfirm>
                </template>
              </el-table-column>
            </el-table>

            <div class="flex items-center justify-end pt-4">
              <span>已选择</span>
              <span class="px-1 text-p">{{ multipleSelection.length }}</span>
              <span>项</span>
              <span class="ml-10">总价: </span>
              <span class="text-2xl font-bold text-p">￥{{ totalPrice }}</span>
              <el-button class="ml-14" type="primary" :disabled="totalPrice <= 0" @click="onPay"> 去支付 </el-button>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <el-tab-pane label="购买记录" name="second"> Config </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
  /* 购物车 */
  import { useRouter } from 'vue-router';
  const router = useRouter();
  import { ElMessage, ElMessageBox } from 'element-plus';

  const activeName = ref('first');
  const tableData = ref([
    {
      id: 1,
      longID: 4563223568,
      name: '脑疾病研究',
      price: 23040.0,
      time: '2022-03-12 12:00:00',
    },
    {
      id: 2,
      longID: 4563223568,
      name: '脑疾病组学数据研究',
      price: 162000.0,
      time: '2022-03-12 12:00:00',
    },
    {
      id: 3,
      longID: 4563223568,
      name: '脑疾病组学数据研究',
      price: 30000.0,
      time: '2022-03-12 12:00:00',
    },
    {
      id: 4,
      longID: 4563223568,
      name: '脑疾病研究',
      price: 23040.0,
      time: '2022-03-12 12:00:00',
    },
  ]);
  //总价
  const totalPrice = computed(() => {
    return multipleSelection.value.reduce((total, item) => total + item.price, 0);
  });

  //删除
  const onDel = (row) => {};

  //多选
  const multipleSelection = ref([]);
  const handleSelectionChange = (val) => {
    multipleSelection.value = val;
  };

  //支付
  const onPay = () => {
    ElMessageBox.confirm(`确定支付${totalPrice.value}元？`, '支付提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(() => {
        ElMessage({
          type: 'success',
          message: '支付成功',
        });
      })
      .catch((err) => {
        // console.log(err);
      });
  };

  const onBackHome = () => {
    router.back();
  };
</script>

<style scoped lang="scss">
  .tabs {
    --el-border-color-light: transparent;

    :deep(.el-tabs__content) {
      height: 0;
      flex: 1;
      background: #f0f2f5;
    }

    :deep(.el-tabs__header) {
      margin: 0;
      padding-left: 28px;
      border-bottom: none;
    }

    .el-tab-pane {
      height: 100%;
    }
  }
</style>
