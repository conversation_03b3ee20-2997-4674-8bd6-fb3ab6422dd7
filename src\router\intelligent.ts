//智能分析平台
export default {
  path: '/intelligent',
  redirect: '/intelligent/home',
  component: () => import('@/views/logged-layout/index.vue'),
  children: [
    {
      path: 'home',
      name: 'IntelligentHome',
      component: () => import('@/views/intelligent/intelligent-home.vue'),
    },
    {
      path: 'topic',
      name: 'ResourceTopic',
      component: () => import('@/views/intelligent/topic.vue'),
      props: (route) => ({ tag: route.query.tag }),
    },
    //专题库详情
    {
      path: 'info',
      name: 'IntelligentInfo',
      component: () => import('@/views/intelligent/info/info.vue'),
    },
    //数据目录管理
    /* {
      path: 'catalog',
      component: () => import('@/views/intelligent/data-manage/catalog-menu.vue'),
      children: [
        {
          path: 'register',
          name: 'IntelligentRegister',
          component: () => import('@/views/intelligent/data-manage/category-register/category-register.vue'),
        },
        {
          path: 'connect',
          name: 'IntelligentConnect',
          component: () => import('@/views/intelligent/data-manage/connect-table/connect-table.vue'),
        },
        {
          path: 'audit',
          name: 'IntelligentAudit',
          component: () => import('@/views/intelligent/data-manage/catalog-audit/catalog-audit.vue'),
        },

        {
          path: 'issue',
          name: 'IntelligentIssue',
          component: () => import('@/views/intelligent/data-manage/catalog-issue/catalog-issue.vue'),
        },
      ],
    }, */
    // {
    //   path: 'catalog/connect-add/:catalogId',
    //   name: 'IntelligentConnectAdd',
    //   component: () => import('@/views/intelligent/data-manage/connect-table/connect-add.vue'),
    //   props: true,
    // },
    {
      path: 'catalog/approve/:id',
      name: 'IntelligentApprove',
      component: () => import('@/views/intelligent/data-manage/catalog-audit/catalog-approve.vue'),
      props: true,
    },
    {
      path: 'catalog/publish/:id',
      name: 'IntelligentPublish',
      component: () => import('@/views/intelligent/data-manage/catalog-issue/catalog-publish.vue'),
      props: true,
    },
    //数据清洗管理
    {
      path: 'cleansing',
      component: () => import('@/views/intelligent/data-cleansing/cleansing-layout.vue'),
      children: [
        {
          path: 'data-standard',
          name: 'CleansingStandard',
          component: () => import('@/views/intelligent/data-cleansing/data-standard/data-standard.vue'),
        },
        {
          path: 'task',
          name: 'CleansingTask',
          component: () => import('@/views/intelligent/data-cleansing/cleaning-task/cleaning-task.vue'),
        },
      ],
    },
    {
      path: 'cleansing/task-config',
      name: 'CleansingConfig',
      component: () => import('@/views/intelligent/data-cleansing/cleaning-task/task-config.vue'),
    },
    //数据脱敏管理
    {
      path: 'desensitization',
      component: () => import('@/views/intelligent/data-desensitization/desensitization-layout.vue'),
      children: [
        {
          path: 'arithmetic',
          name: 'DesensitizationArithmetic',
          component: () => import('@/views/intelligent/data-desensitization/arithmetic/arithmetic.vue'),
        },
        {
          path: 'arithmetic-template',
          name: 'ArithmeticTemplate',
          component: () =>
            import('@/views/intelligent/data-desensitization/arithmetic-template/arithmetic-template.vue'),
        },
        {
          path: 'desensitization-task',
          name: 'DesensitizationTask',
          component: () =>
            import('@/views/intelligent/data-desensitization/desensitization-task/desensitization-task.vue'),
        },
        {
          path: 'manage',
          name: 'DesensitizationManage',
          component: () => import('@/views/intelligent/data-desensitization/manage/index.vue'),
        },
        {
          path: 'field/:id',
          name: 'DesensitizationField',
          component: () => import('@/views/intelligent/data-desensitization/manage/field.vue'),
          props: true,
        },
      ],
    },
    {
      path: 'desensitization/task-config',
      name: 'DesensitizationConfig',
      component: () => import('@/views/intelligent/data-desensitization/desensitization-task/task-config.vue'),
    },
    //数据集管理
    {
      path: 'data-set',
      component: () => import('@/views/intelligent/data-set/dataset-layout.vue'),
      children: [
        {
          path: 'manage',
          name: 'DatasetManage',
          component: () => import('@/views/intelligent/data-set/manage/index.vue'),
        },
        {
          path: 'field/:id',
          name: 'DatasetField',
          component: () => import('@/views/intelligent/data-set/manage/field.vue'),
          props: true,
        },
        {
          path: 'database',
          name: 'DatabaseImport',
          component: () => import('@/views/intelligent/data-set/database-import/index.vue'),
        },
        {
          path: 'db-field/:id',
          name: 'DatabaseField',
          component: () => import('@/views/intelligent/data-set/database-import/db-list.vue'),
          props: true,
        },
        {
          path: 'type-mapping',
          name: 'DatasetTypeMappingManage',
          component: () => import('@/views/intelligent/data-set/type-mapping/index.vue'),
        },
      ],
    },
    //专题库管理
    {
      path: 'subject',
      component: () => import('@/views/intelligent/subject-manage/subject-menu.vue'),
      children: [
        {
          path: 'index',
          name: 'SubjectIndex',
          component: () => import('@/views/intelligent/subject-manage/subject-manage.vue'),
        },
        {
          path: 'data-updating',
          name: 'SubjectDataUpdating',
          component: () => import('@/views/intelligent/subject-manage/data-updating/data-updating.vue'),
        },
      ],
    },
  ],
};
