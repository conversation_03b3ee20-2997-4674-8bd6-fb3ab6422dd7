<template>
  <el-scrollbar class="mr-4 h-full w-0 flex-1 rounded bg-w">
    <div class="py-7">
      <div class="mb-4 flex w-full items-center pl-7">
        <div class="h-[13px] w-[3px] bg-p" />
        <span class="ml-2">字段信息</span>
      </div>
      <el-descriptions :column="2" border class="px-10">
        <el-descriptions-item
          v-for="(item, index) in fieldmes"
          :key="index"
          :label="item.label"
          label-align="right"
          width="50"
        >
          <div class="flex justify-between">
            <span class="w-0 flex-1">{{ item.text }}</span>
            <div v-if="item.edit" class="flex cursor-pointer items-center pl-4" @click="onEdit(item)">
              <el-icon color="#007f99" class="mr-2">
                <EditPen />
              </el-icon>
              <span class="text-p">编辑</span>
            </div>
          </div>
        </el-descriptions-item>
      </el-descriptions>

      <div class="mb-4 mt-10 flex w-full items-center pl-7">
        <div class="h-[13px] w-[3px] bg-p" />
        <span class="ml-2">数据信息</span>
      </div>
      <el-descriptions :column="2" border class="px-10">
        <el-descriptions-item
          v-for="(item, index) in datames"
          :key="index"
          :label="item.label"
          label-align="right"
          width="50"
        >
          <div class="flex justify-between">
            <span class="w-0 flex-1">{{ item.text }}</span>
            <div v-if="item.edit" class="flex cursor-pointer items-center pl-4" @click="onEdit(item)">
              <el-icon color="#007f99" class="mr-2">
                <EditPen />
              </el-icon>
              <span class="text-p">编辑</span>
            </div>
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </div>
  </el-scrollbar>

  <DetailsLeftDrawer v-model="visible" :data="drawerData" @success="onSuccess" />
</template>

<script setup>
  import DetailsLeftDrawer from './DetailsLeftDrawer.vue';

  const visible = ref(false);
  let drawerData = reactive({});
  const onEdit = (item) => {
    drawerData = item;
    visible.value = true;
  };
  const onSuccess = () => {};

  let fieldmes = ref([
    {
      label: '数据源',
      text: '317,673',
      edit: false,
    },
    {
      label: '数据库表',
      text: '317,673',
      edit: false,
    },
    {
      label: '字段名称',
      text: '317,673',
      edit: false,
    },
    {
      label: '数据默认值',
      text: '317,673',
      edit: true,
    },
    {
      label: '是否为主键',
      text: '317,673',
      edit: false,
    },
    {
      label: '是否允许为空',
      text: '317,673',
      edit: false,
    },
    {
      label: '数据类型',
      text: '317,673',
      edit: false,
    },
    {
      label: '数据长度',
      text: '317,673',
      edit: false,
    },
    {
      label: '数据精度',
      text: '317,673',
      edit: false,
    },
    {
      label: '数据小数位',
      text: '317,673',
      edit: false,
    },
    {
      label: '字段描述',
      text: '发货的介绍客户水水水水水水水水水水水水和法国打开链接撒卡卡卡卡卡卡卡卡卡卡防护等级孔子思想工作重中之重这种工作',
      edit: true,
    },
  ]);

  let datames = ref([
    {
      label: '参与者',
      text: '317,673',
      edit: false,
    },
    {
      label: '值类型',
      text: '单个值',
      edit: true,
    },
    {
      label: '记录数',
      text: '317,673',
      edit: false,
    },
    {
      label: '数据类型',
      text: '数据',
      edit: true,
    },
    {
      label: '稳定性',
      text: '进行中',
      edit: true,
    },
    {
      label: '层级',
      text: '主要',
      edit: true,
    },
    {
      label: '性别',
      text: '两性',
      edit: true,
    },
    {
      label: '发布日期',
      text: '2012.02',
      edit: false,
    },
    {
      label: '实例',
      text: '单数',
      edit: true,
    },
    {
      label: '版本日期',
      text: '317,673',
      edit: false,
    },
    {
      label: '数据项',
      text: '317,673',
      edit: true,
    },
    {
      label: '成本层',
      text: '317,673',
      edit: true,
    },
    {
      label: '备注',
      text: '肌肤抵抗力商量商量商量商量商量商量商量发生的集控楼附近的撒离开就看了个士大夫黄金分割开来官方特高科和肌肉的放松开了个金佛店铺数据发过来肯定就是克里夫',
      edit: true,
    },
  ]);
</script>

<style lang="scss" scoped>
  :deep(.el-descriptions) {
    .el-descriptions__label {
      color: $color-tip-text;
    }
  }
</style>
