<template>
  <div class="h-full w-full overflow-hidden">
    <div v-loading="loading" class="sidebar-container">
      <div class="mb-2 flex cursor-pointer items-center" @click="goHome">
        <div class="h-[32px] w-[32px]">
          <img class="h-full w-full" src="@/assets/a52x.png" alt="logo" />
        </div>
        <div class="ml-1 w-0 flex-1 text-lg font-semibold">临床大数据平台</div>
      </div>

      <el-scrollbar class="h-0 flex-1">
        <el-menu unique-opened mode="vertical" :default-active="defaultActive" @select="onMenuSelect">
          <el-menu-item v-for="item in menuList" :key="item.id" :index="item.path">
            <i class="iconfont mr-2" :class="item.icon" style="font-size: 16px" />
            <span>{{ item.name }}</span>
          </el-menu-item>
        </el-menu>
      </el-scrollbar>
    </div>

    <div class="ml-[220px] flex h-full flex-col">
      <AccountManagementHeader :title="currentName" />
      <div class="h-0 flex-1">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { useRouter } from 'vue-router';
  import { background } from '@/router/background';
  import { useRoute } from 'vue-router';
  import AccountManagementHeader from '@/components/AccountManagementHeader.vue';
  import { useUsers } from '@/store/index';
  const store = useUsers();

  const route = useRoute();
  export type menuType = {
    id?: number;
    name?: string;
    path?: string;
    children?: menuType[];
    icon?: string;
    parentId?: number;
    redirect?: string;
  };

  const router = useRouter();
  const loading = ref(false);
  const defaultActive = ref('');
  const menuList = ref<menuType[]>([]);
  const currentName = ref('');

  function getMenuData() {
    let arr: menuType[] = [];
    background.children?.forEach((item, index) => {
      arr.push({
        id: index + 1,
        name: item.meta?.title as string,
        path: background.path + '/' + item.path,
        icon: item.meta?.icon as string,
      });
    });
    menuList.value = arr;
    // defaultActive.value = menuList.value[0].path || '';
  }

  const goHome = () => {
    router.replace({ name: store.loggedHome });
  };

  const onMenuSelect = (key: string, keyPath: string[]) => {
    getNamebyPath(key);
    router.push(key);
  };

  const getNamebyPath = (path: string) => {
    if (path) {
      currentName.value = menuList.value.find((item) => item.path === path)?.name || '';
    } else {
      currentName.value = '';
    }
  };

  onMounted(() => {
    getMenuData();
    defaultActive.value = route.path;
    getNamebyPath(defaultActive.value);
  });
</script>

<style lang="scss" scoped>
  .sidebar-container {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1001;
    width: 220px;
    height: 100%;
    overflow: visible;
    font-size: 0;
    border-right: 1px solid rgb(5 5 5 / 6%);
    padding: 8px 8px 0;
    display: flex;
    flex-direction: column;

    --el-menu-border-color: transparent;
    --el-menu-item-height: 42px;
    --el-menu-hover-bg-color: hsl(240 5% 96%);

    .el-menu-item {
      margin-bottom: 2px;
      border-radius: 6px;
    }

    .el-menu-item.is-active {
      background-color: #ebf5f7;
    }
  }
</style>
