<template>
  <div class="bg-baf flex h-full flex-col overflow-hidden">
    <div v-loading="loading" class="bg-w m-5 flex h-0 flex-1 flex-col rounded-md pt-5">
      <div class="flex gap-4 px-10">
        <!-- <el-input
          v-model="code"
          placeholder="团队代码"
          style="width: 200px"
          clearable
          @clear="onSearch"
          @keyup.enter="onSearch"
        />
        <el-input
          v-model="name"
          placeholder="团队名称"
          style="width: 200px"
          clearable
          @clear="onSearch"
          @keyup.enter="onSearch"
        /> -->
        <div>
          <!-- <el-button type="default" @click="onSearch">查询</el-button> -->
          <el-button type="primary" @click="onAdd">新增</el-button>
        </div>
      </div>

      <div class="mt-3 h-0 w-full flex-1 px-10">
        <el-table height="100%" :data="tableData" style="width: 100%" class="c-table-header">
          <el-table-column type="index" width="60" label="序号" />
          <!-- <el-table-column prop="code" label="团队代码" /> -->
          <el-table-column prop="name" label="团队名称" />
          <el-table-column prop="phone" label="电话号码" />
          <el-table-column prop="email" label="邮箱" />
          <el-table-column prop="address" label="通信地址" width="180" show-overflow-tooltip />
          <el-table-column prop="description" label="团队说明" width="180" show-overflow-tooltip />
          <el-table-column prop="updateTime" label="更新时间">
            <template #default="{ row }">
              <div>{{ row.rltTime?.updateTime || '' }}</div>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="180px">
            <template #default="{ row }">
              <el-tooltip content="编辑" effect="dark">
                <el-button link type="primary" icon="edit" @click="onEdit(row)" />
              </el-tooltip>
              <el-popconfirm title="确定删除？" @confirm="onDel(row)">
                <template #reference>
                  <span class="mx-3">
                    <el-tooltip content="删除" effect="dark">
                      <el-button link icon="delete" type="primary" />
                    </el-tooltip>
                  </span>
                </template>
              </el-popconfirm>
              <el-tooltip content="查看成员" effect="dark">
                <el-button link type="primary" icon="User" @click="onViewMembers(row)" />
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="pagination-bottom">
        <el-pagination
          background
          layout="total, prev, pager, next, jumper"
          :page-size="pagination.pageSize"
          :total="total"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <el-dialog v-model="showAdd" :title="formTitle" width="600px" @close="onAddClose">
      <el-form ref="formRef" :model="addForm" :rules="rules" label-width="140px" autocomplete="off">
        <!-- <el-form-item label="团队代码：" prop="code">
          <el-input v-model="addForm.code" placeholder="请输入" maxlength="30" />
        </el-form-item> -->
        <el-form-item label="团队名称：" prop="name">
          <el-input v-model="addForm.name" placeholder="请输入" maxlength="50" />
        </el-form-item>
        <el-form-item label="电话号码：" prop="phone">
          <el-input v-model="addForm.phone" placeholder="请输入" maxlength="20" />
        </el-form-item>
        <el-form-item label="邮箱：" prop="email">
          <el-input v-model="addForm.email" placeholder="请输入" maxlength="50" />
        </el-form-item>
        <el-form-item label="通信地址：" prop="address">
          <el-input v-model="addForm.address" placeholder="请输入" maxlength="200" />
        </el-form-item>
        <el-form-item label="团队说明：" prop="description">
          <el-input v-model="addForm.description" type="textarea" :rows="3" placeholder="请输入" maxlength="500" />
        </el-form-item>
        <el-form-item label="备注说明：" prop="note">
          <el-input v-model="addForm.note" type="textarea" :rows="2" placeholder="请输入" maxlength="200" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span>
          <el-button @click="onAddClose">取消</el-button>
          <el-button type="primary" :loading="addLoading" @click="onAddConfirm"> 确定 </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 引入成员管理组件 -->
    <MemberManagement ref="memberManagementRef" :current-team="currentTeam" />
  </div>
</template>

<script setup lang="ts">
  // 导入API和工具函数
  import { findAllTeam_02, newOrUpdateTeam, deleteTeamById } from '@/api/index';
  import { ElMessage, FormInstance } from 'element-plus';
  import { validateEmail } from '@/utils/validator';
  import MemberManagement from './components/MemberManagement.vue';

  // ==================== 公共状态 ====================
  const loading = ref(false);
  const currentTeam = ref<TeamVO | null>(null);
  const memberManagementRef = ref();

  // ==================== 查询相关 ====================
  const code = ref('');
  const name = ref('');

  const onSearch = () => {
    fetchData();
  };

  // ==================== 团队列表相关 ====================
  const tableData = ref<TeamVO[]>([]);
  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  const total = ref(0);

  /**
   * 获取团队列表数据
   */
  async function fetchData() {
    try {
      loading.value = true;
      const { data } = await findAllTeam_02({
        pageNum: pagination.page,
        pageSize: pagination.pageSize,
      });
      total.value = data?.totalElement || 0;
      tableData.value = data?.content || [];
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  /**
   * 分页变化处理
   */
  const handleCurrentChange = (e) => {
    pagination.page = e;
    fetchData();
  };

  // ==================== 新增/编辑团队相关 ====================
  const formRef = ref<FormInstance>();
  const showAdd = ref(false);
  const addLoading = ref(false);

  // 表单数据
  const addForm = reactive<TeamDTO>({
    id: 0,
    code: '',
    name: '',
    phone: '',
    email: '',
    address: '',
    description: '',
    note: '',
    rltSybole: {
      lockFlag: '0',
    } as any,
  });

  // 表单校验规则
  const rules = ref({
    // code: [{ required: true, message: '不能为空' }],
    name: [{ required: true, message: '不能为空' }],
    phone: [{ required: true, message: '不能为空' }],
    email: [{ required: true, message: '不能为空', validator: validateEmail }],
    address: [{ required: true, message: '不能为空' }],
  });

  // 动态表单标题
  const formTitle = computed(() => (addForm.id ? '编辑团队' : '新增团队'));

  /**
   * 打开新增团队表单
   */
  const onAdd = () => {
    addForm.id = 0;
    addForm.code = '';
    addForm.name = '';
    addForm.phone = '';
    addForm.email = '';
    addForm.address = '';
    addForm.description = '';
    addForm.note = '';
    addForm.rltSybole = { lockFlag: '0' } as any;
    showAdd.value = true;
  };

  /**
   * 打开编辑团队表单
   */
  const onEdit = (row: TeamVO) => {
    showAdd.value = true;
    nextTick(() => {
      addForm.id = row.id;
      addForm.code = row.code || '';
      addForm.name = row.name || '';
      addForm.phone = row.phone || '';
      addForm.email = row.email || '';
      addForm.address = row.address || '';
      addForm.description = row.description || '';
      addForm.note = row.note || '';
      addForm.rltSybole = row.rltSybole || ({ lockFlag: '0' } as any);
    });
  };

  /**
   * 关闭新增/编辑表单
   */
  const onAddClose = () => {
    showAdd.value = false;
    formRef.value?.resetFields();
  };

  /**
   * 提交新增/编辑表单
   */
  const onAddConfirm = () => {
    formRef.value?.validate(async (valid) => {
      try {
        if (valid) {
          addLoading.value = true;
          await newOrUpdateTeam(addForm);
          ElMessage({ type: 'success', message: '操作成功' });
          onAddClose();
          fetchData();
        }
      } catch (error) {
        console.log(error);
      } finally {
        addLoading.value = false;
      }
    });
  };

  // ==================== 删除团队相关 ====================
  /**
   * 删除团队
   */
  async function onDel(row: TeamVO) {
    try {
      loading.value = true;
      await deleteTeamById([row.id!]);
      ElMessage({ type: 'success', message: '删除成功' });
      fetchData();
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  // ==================== 团队成员相关 ====================
  /**
   * 查看团队成员
   */
  const onViewMembers = async (row: TeamVO) => {
    currentTeam.value = row;
    memberManagementRef.value.open(row);
  };

  // ==================== 生命周期钩子 ====================
  onBeforeMount(() => {
    pagination.page = 1;
    fetchData();
  });
</script>
