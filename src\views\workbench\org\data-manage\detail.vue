<template>
  <h2 class="flex h-[60px] cursor-pointer items-center bg-w pl-5 text-xl font-bold" @click="onBack">
    <el-icon class="mr-2" color="#939899">
      <ArrowLeft />
    </el-icon>
    数据集详情
  </h2>

  <div class="overflow-hidden px-5">
    <div class="mt-5 rounded bg-w p-4">
      <h3 class="mb-4">数据集描述</h3>
      <el-descriptions class="px-5" direction="horizontal" :column="2">
        <el-descriptions-item label="数据名称："> 脑疾病数据aaaa </el-descriptions-item>
        <el-descriptions-item label="管理员ID："> admin11 </el-descriptions-item>
        <el-descriptions-item label="数据购买时间："> 2024-1-11 10:19:49 </el-descriptions-item>
        <el-descriptions-item label="数据可用时间："> 3年 </el-descriptions-item>
      </el-descriptions>
    </div>

    <div class="mt-5 rounded bg-w p-4">
      <h3 class="mb-4">数据集字段详情</h3>
      <p class="text-sm">
        {{ subtitle1 }}
      </p>
      <p class="mt-1 text-sm text-tip">
        {{ subtitle2 }}
      </p>
      <el-collapse v-model="activeNames" accordion>
        <el-collapse-item v-for="(item, index) in dsTable" :key="index" :name="index">
          <template #title>
            <div class="flex w-full items-center justify-between pr-2">
              <div class="flex items-center">
                <el-icon v-if="activeNames !== index" color="#B4B6B8">
                  <CaretRight />
                </el-icon>
                <el-icon v-else color="#007f99">
                  <CaretBottom />
                </el-icon>
                <span class="ml-2 font-bold">{{ item }}</span>
              </div>
            </div>
          </template>

          <div class="desc bg-baf p-3 pb-0">
            <el-descriptions :column="4">
              <el-descriptions-item v-for="(detailItem, i) in dsDetails" :key="i" :label="detailItem.name">
                {{ detailItem.text }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>

    <div class="mt-5 rounded bg-w p-4">
      <h3 class="mb-4">数据集相关项目信息</h3>
      <el-table :data="tableProject" style="width: 100%" class="c-table-header mt-4">
        <el-table-column prop="name" label="项目名称" min-width="100px" />
        <el-table-column prop="director" label="项目负责人" />
        <el-table-column prop="timeStart" label="项目起始时间" />
        <el-table-column prop="timeEnd" label="项目终止时间" />
        <el-table-column label="项目状态">
          <template #default="{ row }">
            <span class="status" :class="statusClass[row.state]">{{ statusText[row.state] }}</span>
          </template>
        </el-table-column>
        <el-table-column label="项目成果信息">
          <template #default="{ row }">
            <el-button link type="primary">
              {{ row.sentence }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="项目成员列表">
          <template #default="{ row }">
            <el-button link type="primary">
              {{ row.list }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="mt-5 rounded bg-w p-4">
      <h3 class="mb-4">数据集相关人员信息</h3>
      <el-table :data="tablePeople" style="width: 100%" class="c-table-header mt-4">
        <el-table-column prop="name" label="姓名" min-width="100px" />
        <el-table-column prop="director" label="部门" />
        <el-table-column prop="ID" label="ID" />
        <el-table-column prop="contact" label="联系方式" />
        <el-table-column label="数据使用状态">
          <template #default="{ row }">
            <span class="status" :class="statusClass[row.state]">{{ statusText[row.state] }}</span>
          </template>
        </el-table-column>
        <el-table-column label="人员状态">
          <template #default="{ row }">
            <span class="status" :class="statusClass[row.people]">{{ peopleText[row.people] }}</span>
          </template>
        </el-table-column>
        <el-table-column label="相关项目">
          <template #default="{ row }">
            <el-button link type="primary">
              {{ row.sentence }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup>
  import { useRouter } from 'vue-router';
  const router = useRouter();

  const props = defineProps({ id: { type: String } });
  const onBack = () => {
    router.back();
  };

  const subtitle1 = ref('Basket ID: 2009975');
  const subtitle2 = ref(
    'Contains: 767 standard fileds ( 767 tabular, 0 genetic SNP, and 0 dataset) and 45 bulk fields (45 bulk and 0 HES record)'
  );
  const activeNames = ref('');
  const dsTable = ref(['Addictions', 'Alcohol', 'Alcohol use']);
  const dsDetails = ref([
    { id: 1, name: 'Participants', text: '235,544' },
    { id: 2, name: 'ltem count', text: '318,546' },
    { id: 3, name: 'Stability', text: 'Ongoing' },
    { id: 4, name: 'VaIue Type', text: 'Categorical (single)' },
    { id: 5, name: 'ltem Type', text: 'Data' },
    { id: 6, name: 'Strata', text: 'Derived' },
    { id: 7, name: 'sexed', text: 'Both sexes' },
    { id: 8, name: 'lnstances', text: 'Singular' },
    { id: 9, name: 'Array', text: 'No' },
    { id: 10, name: 'Debut', text: 'Feb 2012' },
    { id: 11, name: 'Version', text: 'Feb 2012' },
    { id: 12, name: 'Cost Tier', text: 'dl 01 sl' },
  ]);

  const tableProject = ref([
    {
      id: 1,
      name: '脑疾病项目AAAA',
      director: '张三',
      timeStart: '2022-03-12 12:00:00',
      timeEnd: '2022-03-12 12:00:00',
      state: 1,
      sentence: '成果信息.pdf',
      list: '成员列表.pdf',
    },
    {
      id: 2,
      name: '脑疾病项目AAAA',
      director: '张三',
      timeStart: '2022-03-12 12:00:00',
      timeEnd: '2022-03-12 12:00:00',
      state: 1,
      sentence: '成果信息.pdf',
      list: '成员列表.pdf',
    },
    {
      id: 3,
      name: '脑疾病项目AAAA',
      director: '张三',
      timeStart: '2022-03-12 12:00:00',
      timeEnd: '2022-03-12 12:00:00',
      state: 1,
      sentence: '成果信息.pdf',
      list: '成员列表.pdf',
    },
    {
      id: 4,
      name: '脑疾病项目AAAA',
      director: '张三',
      timeStart: '2022-03-12 12:00:00',
      timeEnd: '2022-03-12 12:00:00',
      state: 1,
      sentence: '成果信息.pdf',
      list: '成员列表.pdf',
    },
  ]);
  const statusText = {
    1: '进行中',
    2: '已结束',
  };
  const statusClass = {
    1: 'status-green',
    2: 'status-gray',
  };

  const tablePeople = ref([
    {
      id: 1,
      name: '脑疾病项目AAAA',
      director: '计算机学院',
      ID: '1155456589965',
      contact: '13245641231',
      state: 1,
      people: 1,
      sentence: '相关项目.pdf',
    },
    {
      id: 2,
      name: '脑疾病项目AAAA',
      director: '计算机学院',
      ID: '1155456589965',
      contact: '13245641231',
      state: 1,
      people: 1,
      sentence: '相关项目.pdf',
    },
    {
      id: 3,
      name: '脑疾病项目AAAA',
      director: '计算机学院',
      ID: '1155456589965',
      contact: '13245641231',
      state: 1,
      people: 2,
      sentence: '相关项目.pdf',
    },
    {
      id: 4,
      name: '脑疾病项目AAAA',
      director: '计算机学院',
      ID: '1155456589965',
      contact: '13245641231',
      state: 1,
      people: 1,
      sentence: '相关项目.pdf',
    },
    {
      id: 5,
      name: '脑疾病项目AAAA',
      director: '计算机学院',
      ID: '1155456589965',
      contact: '13245641231',
      state: 1,
      people: 1,
      sentence: '相关项目.pdf',
    },
    {
      id: 6,
      name: '脑疾病项目AAAA',
      director: '计算机学院',
      ID: '1155456589965',
      contact: '13245641231',
      state: 1,
      people: 1,
      sentence: '相关项目.pdf',
    },
    {
      id: 7,
      name: '脑疾病项目AAAA',
      director: '计算机学院',
      ID: '1155456589965',
      contact: '13245641231',
      state: 1,
      people: 2,
      sentence: '相关项目.pdf',
    },
    {
      id: 8,
      name: '脑疾病项目AAAA',
      director: '计算机学院',
      ID: '1155456589965',
      contact: '13245641231',
      state: 1,
      people: 1,
      sentence: '相关项目.pdf',
    },
    {
      id: 9,
      name: '脑疾病项目AAAA',
      director: '计算机学院',
      ID: '1155456589965',
      contact: '13245641231',
      state: 1,
      people: 2,
      sentence: '相关项目.pdf',
    },
  ]);
  const peopleText = {
    1: '在职',
    2: '离职',
  };
</script>

<style lang="scss" scoped>
  :deep(.el-descriptions) {
    .el-descriptions__body {
      background-color: transparent;
    }
    .el-descriptions__content {
      color: $color-regular-text;
    }
  }

  .el-collapse {
    --el-border-color-lighter: transparent;
    :deep(.el-collapse-item__arrow) {
      display: none;
    }
  }

  :deep(.desc) {
    .el-descriptions__label {
      color: $color-tip-text;
    }
    .el-descriptions__content {
      color: $color-main-text;
    }
  }

  .status::before {
    content: '';
    display: inline-block;
    border-radius: 999px;
    width: 6px;
    height: 6px;
    margin-right: 6px;
    position: relative;
    top: -2px;
    background: #000;
  }

  .status-green::before {
    background: #29b586;
  }

  .status-gray::before {
    background: $color-tip-text;
  }
</style>
