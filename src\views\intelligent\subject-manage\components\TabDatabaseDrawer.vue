<template>
  <el-drawer v-model="drawer" class="relative" @close="onCancel">
    <template #header>
      <h4 class="text-m">新增数据库</h4>
    </template>

    <el-form ref="formRef" label-position="top" :model="form" :rules="rules">
      <el-form-item label="数据库名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入数据库名称" maxlength="50" />
      </el-form-item>
      <el-form-item label="数据库说明" prop="desc">
        <el-input
          v-model="form.desc"
          placeholder="请输入数据库说明"
          maxlength="300"
          type="textarea"
          :rows="4"
          show-word-limit
        />
      </el-form-item>
      <el-form-item label="更新频率" prop="frequency">
        <el-select v-model="form.frequency" placeholder="请选择更新频率" style="width: 100%">
          <el-option v-for="item in frequencyOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="建库语句" prop="statement">
        <UploadButton v-model="form.statement" />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="onCancel"> 取消 </el-button>
      <el-button type="primary" @click="onSave"> 保存 </el-button>
    </template>
  </el-drawer>
</template>

<script setup>
  import UploadButton from '@/components/UploadButton.vue';
  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
      required: true,
    },
  });
  const drawer = ref(false);
  const form = reactive({
    frequency: '',
    name: '',
    desc: '',
    statement: [],
  });
  const rules = reactive({
    name: [{ required: true, message: '请输入数据库名称', trigger: 'blur' }],
    frequency: [{ required: true, message: '请选择更新频率', trigger: 'blur' }],
    desc: [{ required: true, message: '请输入数据库说明', trigger: 'blur' }],
    statement: [{ required: true, message: '请上传建库语句文件', trigger: 'blur' }],
  });
  const frequencyOptions = ref([
    { label: '每天', value: '1' },
    { label: '每周', value: '2' },
    { label: '每月', value: '3' },
    { label: '每年', value: '4' },
  ]);

  watchEffect(() => {
    drawer.value = props.modelValue;
  });
  const emit = defineEmits(['update:modelValue', 'success']);
  const formRef = ref();
  const onCancel = () => {
    emit('update:modelValue', false);
    formRef.value.resetFields();
  };
  const onSave = () => {
    formRef.value.validate((valid) => {
      if (valid) {
        onCancel();
        emit('success');
      }
    });
  };
</script>
