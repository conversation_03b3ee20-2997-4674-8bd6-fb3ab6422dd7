<template>
  <div class="flex h-full">
    <div class="aside bg-w">
      <asidemenu />
    </div>

    <div class="w-0 flex-1">
      <router-view />
    </div>
  </div>
</template>

<script setup>
  import asidemenu from '@/components/aside.vue';
  import { useRoute } from 'vue-router';
  let route = useRoute();

  let activeid = ref('0');

  let menulist = ref([
    {
      id: '0',
      title: '机构管理',
      pathname: 'BusinessOrgManage',
      svgname: 'icon-zhuye',
    },
    {
      id: '1',
      title: '个人用户管理',
      pathname: 'BusinessPersonalManage',
      svgname: 'icon-guanliyuan_guanliyuanrizhi',
    },
    {
      id: '2',
      title: '项目信息管理',
      pathname: 'BusinessProjectManage',
      svgname: 'icon-tongjitu',
    },
  ]);

  watch(
    () => route.name,
    (val, old) => {
      menulist.value.forEach((e) => {
        if (e.pathname == val) {
          activeid.value = e.id;
        }
      });
    },
    {
      immediate: true,
    }
  );
  provide('datalist', menulist);
  provide('openid', activeid);
</script>

<style scoped lang="scss">
  .aside {
    width: 256px;
    height: 100%;
    box-shadow: 2px 0 12px 0 rgb(0 0 0 / 10%);
    z-index: 10;
  }

  .main-header {
    padding-top: 15px;
    padding-left: 28px;
    background: #fff;
  }
</style>
