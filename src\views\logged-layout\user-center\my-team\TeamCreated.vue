<template>
  <el-scrollbar height="100%">
    <div class="bg-w flex h-full flex-col rounded-md pt-5">
      <div class="flex gap-2 px-10">
        <el-input
          v-model="searchInput"
          placeholder="搜索"
          style="width: 200px"
          clearable
          @keyup.enter="fetchData()"
          @clear="fetchData()"
        />
        <el-button @click="fetchData()"> 搜索 </el-button>
        <el-button type="primary" @click="onAdd"> 新增 </el-button>
        <!-- <el-button :disabled="checkList.length <= 0" :loading="batchLoading" @click="onBatchDel">
            批量删除
          </el-button> -->
      </div>

      <div class="mt-3 h-0 w-full flex-1 px-10">
        <el-table v-loading="loading" height="100%" :data="tableData" style="width: 100%" class="c-table-header">
          <el-table-column label="团队名称">
            <template #default="{ row }">
              <a @click="onView(row)">{{ row.name }}</a>
            </template>
          </el-table-column>
          <el-table-column label="团队成员" width="100">
            <template #default="{ row }">
              <a @click="onMember(row)">成员管理</a>
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip prop="address" label="通讯地址" />
          <el-table-column show-overflow-tooltip prop="description" label="团队描述" />
          <el-table-column label="操作" width="180">
            <template #default="{ row }">
              <el-button link type="primary" @click="onEdit(row)"> 编辑 </el-button>
              <el-popconfirm title="确定删除此项？" @confirm="onDel(row)">
                <template #reference>
                  <el-button link type="primary"> 删除 </el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="pagination-bottom">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :page-size="pagination.pageSize"
          :total="tableData.length"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </el-scrollbar>

  <Drawer :id="id" v-model="showDrawer" :readonly="readonly" @success="onSuccess" />
  <TeamCreatedMember :id="teamId" v-model="showMember" />
</template>

<script setup lang="ts">
  import { findAllTeam_02, deleteTeamById } from '@/api/index';
  import Drawer from './Drawer.vue';
  import { ElMessageBox, ElMessage } from 'element-plus';
  import TeamCreatedMember from './TeamCreatedMember.vue';

  const loading = ref(false);
  //表格
  const searchInput = ref('');
  const tableData = ref<object[]>([]);
  async function fetchData(pageNum = 1) {
    try {
      loading.value = true;
      const { data } = await findAllTeam_02({ pageNum, pageSize: pagination.pageSize, searchInput: searchInput.value });
      total.value = data?.totalElement ?? 0;
      pagination.page = pageNum;
      tableData.value = data?.content ?? [];
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }
  const checkList = ref<object[]>([]);
  const handleSelectionChange = (val) => {
    checkList.value = val;
  };
  const batchLoading = ref(false);
  const onBatchDel = () => {
    ElMessageBox.confirm('确定删除所有选中项？', '操作提示', { type: 'warning' })
      .then(async () => {
        try {
          batchLoading.value = true;
          await Promise.all(checkList.value.map((row: any) => deleteTeamById(row.id, { teamId: row.id })));
          ElMessage({ type: 'success', message: '删除成功' });
          fetchData();
        } catch (error) {
          console.log(error);
        } finally {
          batchLoading.value = false;
        }
      })
      .catch(() => {});
  };
  const onDel = async (row) => {
    try {
      loading.value = true;
      await deleteTeamById(row.id, { teamId: row.id });
      ElMessage({ type: 'success', message: '删除成功' });
      fetchData();
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  };

  //新增
  const showDrawer = ref(false);
  const readonly = ref(false);
  let id = ref('');
  const onAdd = () => {
    id.value = '';
    readonly.value = false;
    showDrawer.value = true;
  };
  const onView = (row) => {
    id.value = row.id;
    readonly.value = true;
    showDrawer.value = true;
  };
  const onEdit = (item) => {
    id.value = item.id;
    readonly.value = false;
    showDrawer.value = true;
  };
  const onSuccess = () => {
    fetchData(pagination.page);
  };

  //页码
  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  const total = ref(0);
  const handleCurrentChange = (e) => {
    pagination.page = e;
    fetchData(e);
  };
  const handleSizeChange = (e) => {
    pagination.pageSize = e;
    fetchData();
  };

  //团队成员
  const showMember = ref(false);
  const teamId = ref(0);
  const onMember = (row) => {
    showMember.value = true;
    teamId.value = row.id;
  };

  fetchData();
</script>
