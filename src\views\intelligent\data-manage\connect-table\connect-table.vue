<template>
  <div class="flex h-full w-full flex-col">
    <h2 class="flex h-[60px] items-center bg-w pl-5 text-xl font-semibold">目录挂接</h2>

    <el-container class="flex h-0 flex-1 p-5">
      <el-aside class="mr-[10px] rounded bg-w">
        <CatalogList @update:model-value="changeCatalogId" />
      </el-aside>

      <ConnectAdd v-if="isHook" :catalog-id="catalogId" @back="onBack" />

      <el-main v-else class="rounded bg-w">
        <div class="flex h-full flex-col">
          <div class="flex justify-between px-10 pt-5">
            <div>
              <el-button type="primary" @click="onHook"> 添加挂接 </el-button>
              <el-button :loading="batchLoading" :disabled="checkList.length <= 0" @click="onBatchDel">
                批量删除
              </el-button>
            </div>

            <!-- <div class="flex">
              <el-input v-model="search" placeholder="请输入字段名称 ">
                <template #append>
                  <el-button :icon="Search" @click="onSearch" />
                </template>
              </el-input>
              <el-button class="ml-3" :icon="Operation" @click="changeSort"></el-button>
            </div> -->
          </div>

          <div class="mt-4 h-0 flex-1 px-10">
            <el-table
              v-loading="loading"
              :data="tableData"
              style="width: 100%"
              height="100%"
              class="c-table-header"
              @selection-change="handleSelectionChange"
            >
              <el-table-column type="selection" width="55" />
              <el-table-column type="index" width="55" label="序号" />
              <!-- <el-table-column prop="dbName" label="数据库名称" />
              <el-table-column prop="tableName" label="表名称" /> -->
              <el-table-column prop="name" label="字段名称" />
              <el-table-column prop="valueType" label="字段类型" />
              <el-table-column prop="description" label="描述" min-width="100px" />
              <!-- <el-table-column prop="size" label="数据长度" />
              <el-table-column prop="accuracy" label="数据精度" />
              <el-table-column prop="decimalPlace" label="数据小数位" />
              <el-table-column prop="defaultValue" label="数据默认值">
                <template #default="{ row }">
                  <span>{{ ['', null, undefined].includes(row.defaultValue) ? '-' : row.defaultValue }}</span>
                </template>
              </el-table-column> -->
              <el-table-column label="操作" width="60">
                <template #default="{ row }">
                  <el-popconfirm title="确定删除此项？" @confirm="onDel(row)">
                    <template #reference>
                      <el-button link type="primary"> 删除 </el-button>
                    </template>
                  </el-popconfirm>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- <div class="pagination-bottom">
            <el-pagination
              background
              layout="total, sizes, prev, pager, next, jumper"
              :page-size="pagination.pageSize"
              :total="tableData.length"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div> -->
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
  import { getMedicalFieldInCatalogue, unmountMedicalFieldFromCatalogue } from '@/api/index';
  import ConnectAdd from './connect-add.vue';
  import CatalogList from '../components/CatalogList.vue';
  import { ElMessageBox, ElMessage } from 'element-plus';
  // import { Search, Operation } from '@element-plus/icons-vue';
  // import { useRouter } from 'vue-router';
  // const router = useRouter();

  const catalogId = ref('');

  const search = ref(''); //查询的输入框
  const onSearch = () => {};
  const changeSort = () => {};

  const loading = ref(false);
  const tableData = ref([]);
  const checkList = ref([]);
  const handleSelectionChange = (val) => {
    checkList.value = val;
  };

  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  const handleCurrentChange = (e) => {
    pagination.page = e;
  };
  const handleSizeChange = (e) => {
    pagination.pageSize = e;
  };

  const isHook = ref(false);
  const onHook = () => {
    isHook.value = true;
  };
  const onBack = () => {
    isHook.value = false;
    fetchData();
  };

  const changeCatalogId = (id) => {
    catalogId.value = id;
    fetchData();
  };
  async function fetchData() {
    try {
      loading.value = true;
      const { data } = await getMedicalFieldInCatalogue(catalogId.value);
      tableData.value = data;
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  const onDel = async (row) => {
    try {
      loading.value = true;
      const prams = [{ ctlgId: catalogId.value, mfId: row.id }];
      await unmountMedicalFieldFromCatalogue(prams);
      ElMessage({ type: 'success', message: '删除成功' });
      fetchData();
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  };
  const batchLoading = ref(false);
  const onBatchDel = () => {
    ElMessageBox.confirm('确定删除所有选中项？', '操作提示', { type: 'warning' })
      .then(async () => {
        try {
          batchLoading.value = true;
          const prams = checkList.value.map((item) => {
            return { ctlgId: catalogId.value, mfId: item.id };
          });
          await unmountMedicalFieldFromCatalogue(prams);
          ElMessage({ type: 'success', message: '删除成功' });
          fetchData();
        } catch (error) {
          console.log(error);
        } finally {
          batchLoading.value = false;
        }
      })
      .catch(() => {});
  };
</script>

<style lang="scss" scoped>
  .el-main {
    --el-main-padding: 0;
  }
</style>
