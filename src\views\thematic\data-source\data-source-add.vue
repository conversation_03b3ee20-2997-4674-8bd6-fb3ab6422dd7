<template>
  <div class="flex h-full w-full flex-col overflow-hidden">
    <h2 class="flex h-[60px] cursor-pointer items-center bg-w pl-5 text-xl font-semibold" @click="onBack">
      <el-icon class="mr-2" color="#939899">
        <ArrowLeft />
      </el-icon>
      {{ props.id ? '编辑' : '新增' }}数据源
    </h2>

    <el-scrollbar class="h-0 flex-1">
      <div class="overflow-hidden p-5">
        <!-- <div class="mb-5 rounded bg-w px-7 py-4">
          <h3>数据源信息</h3>
          <el-form ref="originRef" label-position="top" :model="originForm" :rules="originRules" class="mt-4">
            <el-form-item label="数据源类型" prop="type">
              <el-select v-model="originForm.type" placeholder="请选择数据源类型" style="width: 100%">
                <el-option
                  v-for="(item, index) in dataTypeOptions"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="数据源名称" prop="name">
              <el-input placeholder="请输入数据源名称" v-model="originForm.name" />
            </el-form-item>
            <el-row class="w-full">
              <el-col :span="12">
                <el-form-item label="状态" prop="state">
                  <el-radio-group v-model="originForm.state">
                    <el-radio :label="1">启用</el-radio>
                    <el-radio :label="2">禁用</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="数据库类型" prop="databaseType">
                  <el-radio-group v-model="originForm.databaseType">
                    <el-radio :label="1">前置库</el-radio>
                    <el-radio :label="2">业务库</el-radio>
                    <el-radio :label="3">全量库</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="备注" prop="remark">
              <el-input
                :rows="4"
                type="textarea"
                v-model="originForm.remark"
                placeholder="请输入备注"
                maxlength="300"
                show-word-limit
              />
            </el-form-item>
          </el-form>
        </div> -->

        <div class="rounded bg-w pt-4">
          <h3 class="pl-7">连接信息</h3>
          <el-form ref="linkRef" label-position="top" :model="linkform" :rules="linkRules" class="mt-4 px-7">
            <el-row class="w-full">
              <el-col :span="8" class="pr-10">
                <el-form-item label="主机" prop="host">
                  <el-input v-model="linkform.host" placeholder="请输入主机" maxlength="100" />
                </el-form-item>
              </el-col>
              <el-col :span="8" class="pr-10">
                <el-form-item label="端口" prop="port">
                  <el-input v-model="linkform.port" placeholder="请输入端口" maxlength="10" />
                </el-form-item>
              </el-col>
              <el-col :span="8" class="pr-10">
                <el-form-item label="数据库" prop="database">
                  <el-input v-model="linkform.database" placeholder="请输入数据库" maxlength="50" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row class="w-full">
              <el-col :span="8" class="pr-10">
                <el-form-item label="用户名" prop="username">
                  <el-input v-model="linkform.username" placeholder="请输入用户名" maxlength="50" />
                </el-form-item>
              </el-col>
              <el-col :span="8" class="pr-10">
                <el-form-item label="密码" prop="password">
                  <el-input v-model="linkform.password" placeholder="请输入密码" maxlength="50" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <!-- <div class="mt-5 flex items-center justify-center border-t border-border py-3">
            <el-button plain type="primary" @click="connectivityTest">连通性测试</el-button>
          </div> -->
        </div>
      </div>
    </el-scrollbar>

    <div class="action-footer">
      <el-button type="primary" @click="onSubmit"> 保存 </el-button>
      <el-button @click="onBack"> 取消 </el-button>
    </div>
  </div>
</template>

<script setup>
  import { newOrUpdateDatabaseVO, findDBVOByDbId } from '@/api/index';
  import { useRouter } from 'vue-router';
  import { ElMessage } from 'element-plus';

  const router = useRouter();
  const props = defineProps({ id: { type: String } });
  watchEffect(() => {
    if (props.id) {
      fetchData();
    }
  });

  const onBack = () => {
    router.back();
  };

  const originForm = reactive({
    type: '',
    name: '',
    state: 1,
    databaseType: 1,
    remark: '',
  });
  const originRules = reactive({
    type: [{ required: true, message: '请选择数据源类型', trigger: 'blur' }],
    name: [{ required: true, message: '请输入数据源名称', trigger: 'blur' }],
    state: [{ required: true, message: '请选择状态', trigger: 'blur' }],
    databaseType: [{ required: true, message: '请选择数据库类型', trigger: 'blur' }],
  });
  const dataTypeOptions = ref([
    {
      label: '数据源1',
      value: 1,
    },
  ]);

  const linkform = reactive({
    host: '',
    port: '',
    database: '',
    username: '',
    password: '',
  });
  const linkRules = reactive({
    host: [{ required: true, message: '请输入主机', trigger: 'blur' }],
    port: [{ required: true, message: '请输入端口', trigger: 'blur' }],
    database: [{ required: true, message: '请输入数据库', trigger: 'blur' }],
    username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
    password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  });
  //连通性测试
  const connectivityTest = () => {};

  const originRef = ref();
  const linkRef = ref();
  const onSubmit = async () => {
    try {
      // await Promise.all([originRef.value.validate(), linkRef.value.validate()]);
      await Promise.all([linkRef.value.validate()]);
      let params = {
        address: linkform.host,
        port: linkform.port,
        databaseName: linkform.database,
        userName: linkform.username,
        password: linkform.password,
      };
      if (props.id) {
        params.id = props.id;
      }
      await newOrUpdateDatabaseVO(params);
      ElMessage({ type: 'success', message: '保存成功' });
      router.back();
    } catch (error) {
      console.warn(error);
    }
  };

  async function fetchData() {
    try {
      const { data } = await findDBVOByDbId(props.id);
      linkform.host = data.address;
      linkform.port = data.port;
      linkform.database = data.databaseName;
      linkform.username = data.userName;
      linkform.password = data.password;
    } catch (error) {
      console.log(error);
    }
  }
</script>
