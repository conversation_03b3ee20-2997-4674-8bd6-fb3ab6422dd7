<template>
  <el-drawer v-model="drawer" class="relative" size="1000px" @close="onCancel">
    <template #header>
      <h4 class="text-m">{{ action }}字段</h4>
    </template>

    <el-table
      ref="multipleTableRef"
      v-loading="tableLoading"
      height="100%"
      :data="tableData"
      style="width: 100%"
      class="c-table-header"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" :selectable="checkSelectable" />
      <el-table-column type="index" width="55" label="序号" />
      <el-table-column prop="name" label="字段名称" min-width="100px" />
      <el-table-column prop="dataType" label="字段类型" />
      <el-table-column prop="unit" label="单位" />
    </el-table>

    <template #footer>
      <el-button @click="onCancel"> 取消 </el-button>
      <el-button type="primary" :loading="saveLoading" @click="onSave"> 保存 </el-button>
    </template>
  </el-drawer>
</template>

<script setup>
  import { findAllFieldVOByDbIdTblId, addField_1 } from '@/api/index';
  import { ElMessage } from 'element-plus';

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
      required: true,
    },
    data: {
      type: Object,
      required: true,
    },
  });

  const form = reactive({
    tableId: '',
    dataBaseId: '',
  });
  const rules = reactive({
    tableName: [{ required: true, message: '请输入表名', trigger: 'blur' }],
    description: [{ required: true, message: '请输入说明', trigger: 'blur' }],
  });

  const drawer = ref(false);
  watchEffect(() => {
    drawer.value = props.modelValue;
  });
  watch(
    () => props.data,
    (value) => {
      for (const key in form) {
        form[key] = value[key];
      }
      fetchTable();
    }
  );
  const action = computed(() => {
    return props.data?.id ? '编辑' : '新增';
  });

  const multipleTableRef = ref();
  const checkSelectable = (row) => {
    return !row.hasFieldRecordInSystem;
  };
  let checkList = [];
  const handleSelectionChange = (val) => {
    checkList = val;
  };
  const tableData = ref([]);
  const tableLoading = ref(false);
  async function fetchTable() {
    try {
      tableLoading.value = true;
      const { data } = await findAllFieldVOByDbIdTblId(form.dataBaseId, form.tableId);
      tableData.value = data[1];
    } catch (error) {
      console.log(error);
    } finally {
      tableLoading.value = false;
    }
  }

  const emit = defineEmits(['update:modelValue', 'success']);
  const onCancel = () => {
    emit('update:modelValue', false);
  };
  const saveLoading = ref();
  const onSave = async () => {
    try {
      saveLoading.value = true;
      if (!checkList.length) {
        ElMessage({ type: 'warning', message: '请选择数据' });
        return;
      }
      let params = checkList
        .filter((item) => !item.hasFieldRecordInSystem)
        .map((item) => {
          return {
            tableId: form.tableId,
            name: item.name,
            unit: item.unit,
            dataType: item.dataType,
            description: item.description,
          };
        });
      await addField_1(params);
      ElMessage({ type: 'success', message: '保存成功' });
      onCancel();
      emit('success');
    } catch (error) {
      console.log(error);
    } finally {
      saveLoading.value = false;
    }
  };
</script>
