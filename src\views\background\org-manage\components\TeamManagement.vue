<template>
  <div>
    <!-- 机构团队抽屉 -->
    <el-drawer v-model="showTeams" :title="'机构团队 - ' + currentOrg?.name" size="80%" destroy-on-close>
      <div class="m-5 flex flex-col" v-loading="loading">
        <div class="mb-4 flex gap-4">
          <el-button type="primary" @click="onAddTeamToOrg">添加团队到机构</el-button>
          <el-button type="danger" :disabled="selectedRows.length === 0" @click="onBatchRemoveTeamFromOrg"
            >批量退出机构</el-button
          >
        </div>
        <el-table
          :data="teamTableData"
          style="width: 100%"
          class="c-table-header"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column type="index" width="60" label="序号" />
          <!-- <el-table-column prop="code" label="团队编码" /> -->
          <el-table-column prop="name" label="团队名称" />
          <!-- <el-table-column prop="description" label="团队描述" show-overflow-tooltip /> -->
          <el-table-column prop="phone" label="电话号码" />
          <el-table-column prop="email" label="电子邮箱" />
          <el-table-column prop="address" label="通信地址" show-overflow-tooltip />
          <el-table-column prop="note" label="备注说明" show-overflow-tooltip />
          <!-- <el-table-column prop="orgName" label="所属机构" /> -->
          <el-table-column prop="updateTime" label="更新时间" width="170px">
            <template #default="{ row }">
              <div>{{ row.rltTime?.updateTime || '' }}</div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100px">
            <template #default="{ row }">
              <el-tooltip content="查看成员" effect="dark">
                <span class="mr-2 inline-block">
                  <el-button link icon="user" type="primary" @click="onViewMembers(row)" />
                </span>
              </el-tooltip>
              <el-popconfirm title="确定让该团队退出机构？" @confirm="onRemoveTeamFromOrg(row)">
                <template #reference>
                  <span>
                    <el-tooltip content="退出机构" effect="dark">
                      <el-button link icon="delete" type="primary" />
                    </el-tooltip>
                  </span>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-drawer>

    <!-- 添加团队到机构对话框 -->
    <el-dialog v-model="showAddTeam" title="添加团队到机构" width="80%" destroy-on-close @close="onAddTeamClose">
      <div class="mb-4 flex gap-4">
        <el-input
          v-model="teamNameFilter"
          placeholder="团队名称"
          style="width: 200px"
          clearable
          @clear="onTeamSearch"
          @keyup.enter="onTeamSearch"
        />
        <div>
          <el-button type="default" @click="onTeamSearch">查询</el-button>
        </div>
      </div>

      <el-table
        v-loading="teamLoading"
        :data="teamList"
        style="width: 100%"
        row-key="id"
        @selection-change="handleTeamSelectionChange"
      >
        <el-table-column type="selection" width="55" reserve-selection />
        <!-- <el-table-column prop="code" label="团队编码" /> -->
        <el-table-column prop="name" label="团队名称" />
        <!-- <el-table-column prop="description" label="团队描述" show-overflow-tooltip /> -->
        <el-table-column prop="phone" label="电话号码" />
        <el-table-column prop="email" label="电子邮箱" />
        <el-table-column prop="address" label="通信地址" show-overflow-tooltip />
        <el-table-column prop="note" label="备注说明" show-overflow-tooltip />
      </el-table>

      <div class="pagination-bottom">
        <el-pagination
          background
          layout="total, prev, pager, next, jumper"
          :page-size="teamPagination.pageSize"
          :total="teamTotal"
          @current-change="handleTeamPageChange"
        />
      </div>

      <template #footer>
        <span>
          <el-button @click="onAddTeamClose">取消</el-button>
          <el-button type="primary" :loading="addTeamLoading" @click="onAddTeamConfirm">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 查看团队成员对话框 -->
    <el-dialog v-model="showMembers" :title="'团队成员 - ' + currentTeam?.name" width="600px" destroy-on-close>
      <div class="m-5" v-loading="memberLoading">
        <el-table :data="memberTableData" style="width: 100%" class="c-table-header">
          <el-table-column type="index" width="60" label="序号" />
          <el-table-column prop="userName" label="用户名" />
          <el-table-column prop="name" label="姓名" />
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { findTeamByOrgId } from '@/api';
  import {
    addTeamToOrg,
    removeTeamFromOrg,
    findAllTeam_02,
    findTeamUserRoleVOByTeamId,
  } from '@/api/modules/sjzyxtztdglmk';
  import { ElMessage } from 'element-plus';

  const props = defineProps<{
    currentOrg: OrgVO | null;
  }>();

  // 显示团队抽屉
  const showTeams = ref(false);
  const teamTableData = ref<TeamVO[]>([]);
  const loading = ref(false);
  const selectedRows = ref<TeamVO[]>([]);

  // 团队成员相关
  const showMembers = ref(false);
  const currentTeam = ref<any>(null);
  const memberTableData = ref<TeamUserRoleVO[]>([]);
  const memberLoading = ref(false);

  // 添加团队相关
  const showAddTeam = ref(false);
  const addTeamLoading = ref(false);
  const teamLoading = ref(false);
  const teamCodeFilter = ref('');
  const teamNameFilter = ref('');
  const teamList = ref<TeamDTO[]>([]);
  const teamTotal = ref(0);
  const selectedTeamIds = ref<number[]>([]);

  // 团队分页
  const teamPagination = reactive({
    page: 1,
    pageSize: 10,
  });

  /**
   * 打开团队管理抽屉
   */
  const open = async (org: OrgVO) => {
    showTeams.value = true;
    await fetchOrgTeams(org.id);
  };

  /**
   * 获取机构团队列表
   */
  async function fetchOrgTeams(orgId?: number) {
    try {
      loading.value = true;
      if (props.currentOrg?.id || orgId) {
        const { data } = await findTeamByOrgId(props.currentOrg?.id || orgId!);
        teamTableData.value = data || [];
      }
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  /**
   * 表格多选变化
   */
  const handleSelectionChange = (selection: any[]) => {
    selectedRows.value = selection;
  };

  /**
   * 批量让团队退出机构
   */
  async function onBatchRemoveTeamFromOrg() {
    try {
      if (selectedRows.value.length === 0) {
        ElMessage({ type: 'warning', message: '请至少选择一个团队' });
        return;
      }

      const teamIds = selectedRows.value.map((row) => row.id);

      if (!props.currentOrg?.id) {
        ElMessage({ type: 'warning', message: '未找到当前机构' });
        return;
      }

      loading.value = true;
      await removeTeamFromOrg(props.currentOrg.id, teamIds as any);
      ElMessage({ type: 'success', message: '批量退出成功' });
      await fetchOrgTeams(props.currentOrg.id);
      selectedRows.value = [];
    } catch (error) {
      console.log(error);
      ElMessage({ type: 'error', message: '批量退出失败' });
    } finally {
      loading.value = false;
    }
  }

  /**
   * 让单个团队退出机构
   */
  async function onRemoveTeamFromOrg(row: any) {
    try {
      if (!props.currentOrg?.id) {
        ElMessage({ type: 'warning', message: '未找到当前机构' });
        return;
      }

      loading.value = true;
      await removeTeamFromOrg(props.currentOrg.id, [row.id]);
      ElMessage({ type: 'success', message: '退出成功' });
      await fetchOrgTeams(props.currentOrg.id);
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  /**
   * 查看团队成员
   */
  const onViewMembers = async (row: any) => {
    try {
      memberLoading.value = true;
      currentTeam.value = row;
      showMembers.value = true;
      const { data } = await findTeamUserRoleVOByTeamId(row.id);
      memberTableData.value = data || [];
    } catch (error) {
      console.log(error);
      ElMessage({ type: 'error', message: '获取团队成员失败' });
    } finally {
      memberLoading.value = false;
    }
  };

  /**
   * 打开添加团队对话框
   */
  const onAddTeamToOrg = () => {
    teamPagination.page = 1;
    showAddTeam.value = true;
    fetchTeamList();
  };

  /**
   * 关闭添加团队对话框
   */
  const onAddTeamClose = () => {
    showAddTeam.value = false;
    teamCodeFilter.value = '';
    teamNameFilter.value = '';
    selectedTeamIds.value = [];
  };

  /**
   * 获取团队列表
   */
  async function fetchTeamList() {
    try {
      teamLoading.value = true;
      // 使用团队搜索API
      const { data } = await findAllTeam_02({
        pageNum: teamPagination.page,
        pageSize: teamPagination.pageSize,
        searchInput: teamNameFilter.value,
      });
      teamTotal.value = data?.totalElement || 0;
      teamList.value = data?.content || [];
    } catch (error) {
      console.log(error);
    } finally {
      teamLoading.value = false;
    }
  }

  /**
   * 团队查询
   */
  const onTeamSearch = () => {
    teamPagination.page = 1;
    fetchTeamList();
  };

  /**
   * 团队分页变化
   */
  const handleTeamPageChange = (page: number) => {
    teamPagination.page = page;
    fetchTeamList();
  };

  /**
   * 团队表格选择变化
   */
  const handleTeamSelectionChange = (val: any[]) => {
    selectedTeamIds.value = val.map((item) => item.id);
  };

  /**
   * 提交添加团队到机构表单
   */
  const onAddTeamConfirm = async () => {
    try {
      if (selectedTeamIds.value.length === 0) {
        ElMessage({ type: 'warning', message: '请至少选择一个团队' });
        return;
      }

      if (!props.currentOrg?.id) {
        ElMessage({ type: 'warning', message: '未找到当前机构' });
        return;
      }

      addTeamLoading.value = true;
      await addTeamToOrg(props.currentOrg.id, selectedTeamIds.value);
      ElMessage({ type: 'success', message: '添加团队成功' });
      onAddTeamClose();
      await fetchOrgTeams(props.currentOrg.id);
    } catch (error) {
      console.log(error);
      ElMessage({ type: 'error', message: '添加团队失败' });
    } finally {
      addTeamLoading.value = false;
    }
  };

  // 对外暴露方法
  defineExpose({
    open,
    showTeams,
  });
</script>
