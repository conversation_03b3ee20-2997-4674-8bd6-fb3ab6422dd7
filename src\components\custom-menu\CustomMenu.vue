<template>
  <div class="h-full bg-w">
    <el-scrollbar height="100%">
      <div class="p-5">
        <el-menu class="menu" unique-opened :default-active="currentId" @select="onSelect">
          <CustomMenuItem :data="data" :current-id="currentId" />
        </el-menu>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup>
  import CustomMenuItem from './CustomMenuItem.vue';
  const props = defineProps({
    modelValue: {
      type: String,
      default: '',
    },
    data: {
      type: Array,
      required: true,
    },
  });
  const emit = defineEmits(['update:modelValue']);
  const currentId = ref('');
  const onSelect = (index) => {
    currentId.value = index;
    emit('update:modelValue', index);
  };
  watchEffect(() => {
    currentId.value = props.modelValue;
  });
</script>

<style lang="scss" scoped>
  .menu {
    --el-menu-border-color: transparent;
  }
</style>
