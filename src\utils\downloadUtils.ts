import { ElMessage } from 'element-plus';
import type { AxiosRequestConfig } from 'axios';

/**
 * 通用文件下载方法
 * @param url 请求地址
 * @param data POST请求体数据
 * @param filename 可选，自定义文件名
 * @param config 可选，Axios请求配置
 */
export const downloadFile = async (url: string, data: any, filename?: string) => {
  try {
    const response = await fetch(url, {
      method: 'POST',
      body: data,
      redirect: 'follow',
    });

    if (!response.ok) {
      throw new Error(`下载失败: ${response.status} ${response.statusText}`);
    }

    const actualFilename = getFilenameFromResponse(response) || filename || 'download';
    const blob = await response.blob();
    triggerDownload(blob, actualFilename);

    return true;
  } catch (error) {
    console.error('下载错误:', error);
    ElMessage({
      type: 'error',
      message: error instanceof Error ? error.message : '文件下载失败',
    });
    return false;
  }
};

/**
 * 从响应头提取文件名
 */
const getFilenameFromResponse = (response: Response): string | null => {
  const disposition = response.headers.get('Content-Disposition');
  if (!disposition) return null;

  // 处理 filename="xxx" 和 filename*=UTF-8''xxx 格式
  const filenameRegex = /filename\*?=["']?(?:UTF-\d['"]*)?([^;"'\n]*)/i;
  const matches = disposition.match(filenameRegex);
  return matches?.[1] ? decodeURIComponent(matches[1]) : null;
};

/**
 * 触发浏览器下载
 */
const triggerDownload = (blob: Blob, filename: string) => {
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  a.style.display = 'none';

  document.body.appendChild(a);
  a.click();

  setTimeout(() => {
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  }, 100);
};
