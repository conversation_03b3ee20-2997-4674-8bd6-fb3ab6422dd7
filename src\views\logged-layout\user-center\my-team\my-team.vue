<template>
  <div class="flex h-full flex-col">
    <div class="box-shadow bg-w pl-10 pt-4">
      <h2 class="text-xl font-bold">我的团队</h2>
      <el-tabs v-model="activeName" class="mt-4">
        <el-tab-pane label="我的团队" name="1" />
        <!-- <el-tab-pane label="我加入的团队" name="2" />
        <el-tab-pane label="团队数据管理" name="3" /> -->
      </el-tabs>
    </div>

    <div class="h-0 flex-1 bg-baf">
      <TeamCreated v-if="activeName === '1'" />
      <TeamJoined v-if="activeName === '2'" />
      <TeamData v-if="activeName === '3'" />
    </div>
  </div>
</template>

<script setup>
  import TeamCreated from './TeamCreated.vue';
  import TeamJoined from './TeamJoined.vue';
  import TeamData from './TeamData.vue';
  const activeName = ref('1');
</script>

<style lang="scss" scoped>
  :deep(.el-tabs__header) {
    margin-bottom: 0;
  }
</style>
