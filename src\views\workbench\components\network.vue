<template>
  <div class="flex h-full flex-col rounded-lg bg-w pb-2 pl-6 pr-10 pt-[18px]">
    <div class="flex justify-between">
      <h4 class="text-lg font-bold">相关新闻</h4>
      <!-- <div class="cursor-pointer" @click="onMore">
        <span class="mr-1 text-xs text-[#939899]">查看更多</span>
        <el-icon :size="10">
          <ArrowRightBold color="#939899" :size="20" />
        </el-icon>
      </div> -->
    </div>

    <el-scrollbar class="h-0 flex-1">
      <ul class="list-disc pl-4 pt-5 text-sm text-p">
        <li v-for="item in datali" :key="item.id" class="mb-5 cursor-pointer" @click="onViewNews(item)">
          <div class="flex justify-between">
            <p class="mr-3 text-m hover:text-p">
              {{ item.text }}
            </p>
            <p class="w-[80px] whitespace-nowrap text-[#939899]">
              {{ item.date }}
            </p>
          </div>
        </li>
      </ul>
    </el-scrollbar>
  </div>
</template>
<script setup lang="ts">
  import { useRouter } from 'vue-router';
  const router = useRouter();

  let datali = [
    {
      id: 3,
      text: '陆林院士：人工智能在脑疾病诊治领域有较大应用空间',
      date: '2023-04-21',
    },
    {
      id: 2,
      text: '陆林院士：中国脑计划——从基础研究到临床应用',
      date: '2023-02-23',
    },
    {
      id: 5,
      text: '北京大学首届神经精神学科融合高峰论坛举办',
      date: '2022-10-31',
    },
    {
      id: 1,
      text: '牵头脑计划重大项目实施，引领脑疾病研究领域前沿',
      date: '2022-05-26',
    },
    {
      id: 4,
      text: '孤独症脑科学论坛圆满落幕',
      date: '2022-02-25',
    },
  ];

  const onMore = () => {
    console.log('查看更多');
  };

  const onViewNews = (item) => {
    router.push({ name: 'NetworkDetail', query: { id: item.id } });
  };
</script>
