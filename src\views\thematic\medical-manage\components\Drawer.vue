<template>
  <el-drawer v-model="drawer" class="relative" @close="onCancel">
    <template #header>
      <h4 class="text-m">{{ action }}医学字段</h4>
    </template>

    <el-form ref="formRef" v-loading="formLoading" :model="form" label-width="130px" :rules="rules">
      <el-form-item label="字段名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入字段名称" maxlength="50" />
      </el-form-item>
      <el-form-item label="字段类型" prop="valueType">
        <el-select v-model="form.valueType" placeholder="请选择字段类型" style="width: 100%">
          <el-option v-for="item in valueTypes" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="是否按性别区分" prop="gendered">
        <el-radio-group v-model="form.gendered" placeholder="请选择是否按性别区分">
          <el-radio :label="true"> 是 </el-radio>
          <el-radio :label="false"> 否 </el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- <el-form-item label="是否有实例" prop="instanced">
        <el-radio-group v-model="form.instanced" placeholder="请选择是否有实例">
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item> -->
      <el-form-item label="说明" prop="description">
        <el-input v-model="form.description" placeholder="请输入说明" maxlength="300" />
      </el-form-item>
      <template v-if="props.id">
        <el-form-item label="备选项">
          <a @click="gotoAlternative">备选项管理</a>
        </el-form-item>
        <el-form-item label="实例">
          <a @click="gotoInstance">实例管理</a>
        </el-form-item>
        <el-form-item label="出版文献">
          <a @click="showPublications = true">出版文献管理</a>
        </el-form-item>
        <el-form-item label="原始文档">
          <a @click="showDocument = true">原始文档管理</a>
        </el-form-item>
      </template>
    </el-form>

    <template #footer>
      <el-button @click="onCancel"> 取消 </el-button>
      <el-button type="primary" :loading="saveLoading" @click="onSave"> 保存 </el-button>
    </template>
  </el-drawer>

  <Alternative :id="id" v-model="showAlternative" />
  <Instance :id="id" v-model="showInstance" :value-type="form.valueType" />
  <Publications :id="id" v-model="showPublications" />
  <Document :id="id" v-model="showDocument" />
</template>

<script setup>
  import { newOrUpdateMedicalFieldVO, getMedicalField } from '@/api/index';
  import { ElMessage } from 'element-plus';
  import Alternative from './Alternative.vue';
  import Instance from './Instance.vue';
  import Publications from './Publications.vue';
  import Document from './Document.vue';
  import { valueTypes } from '@/utils/constants';

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
      required: true,
    },
    id: {
      type: [String, Number],
    },
    readonly: {
      type: Boolean,
      default: false,
    },
  });
  const drawer = ref(false);
  let form = reactive({
    id: 0,
    name: '',
    valueType: '',
    gendered: '',
    // instanced: '',
    description: '',
  });
  watchEffect(() => {
    drawer.value = props.modelValue;
  });
  watch(
    () => props.modelValue,
    (value) => {
      if (value && props.id) {
        fetchData();
      }
    }
  );
  const action = computed(() => {
    return props.id ? '编辑' : '添加';
  });

  const rules = reactive({
    name: [{ required: true, message: '请输入字段名称', trigger: 'blur' }],
    valueType: [{ required: true, message: '请选择字段类型', trigger: 'blur' }],
    gendered: [{ required: true, message: '请选择是否按性别区分', trigger: 'blur' }],
    instanced: [{ required: true, message: '请选择是否有实例', trigger: 'blur' }],
    description: [{ required: true, message: '请输入说明', trigger: 'blur' }],
  });

  const emit = defineEmits(['update:modelValue', 'success']);
  const formRef = ref();
  const saveLoading = ref(false);
  const onCancel = () => {
    formRef.value.resetFields();
    emit('update:modelValue', false);
  };
  const onSave = () => {
    formRef.value.validate(async (valid) => {
      if (valid) {
        try {
          saveLoading.value = true;
          form.id = props.id;
          await newOrUpdateMedicalFieldVO(form);
          ElMessage({ type: 'success', message: '保存成功' });
          onCancel();
          emit('success');
        } catch (error) {
          console.log(error);
        } finally {
          saveLoading.value = false;
        }
      }
    });
  };

  const showAlternative = ref(false);
  const gotoAlternative = () => {
    showAlternative.value = true;
  };
  const showInstance = ref(false);
  const gotoInstance = () => {
    showInstance.value = true;
  };
  const showPublications = ref(false);
  const showDocument = ref(false);

  const formLoading = ref(false);
  async function fetchData() {
    try {
      formLoading.value = true;
      await Promise.all([fetchField()]);
    } catch (error) {
      console.log(error);
    } finally {
      formLoading.value = false;
    }
  }

  async function fetchField() {
    try {
      const { data } = await getMedicalField(props.id);
      for (const key in form) {
        form[key] = data[key];
      }
    } catch (error) {
      return Promise.reject(error);
    }
  }
</script>
