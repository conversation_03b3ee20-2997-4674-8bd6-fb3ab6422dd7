/*
 * @OriginalName: 数据资源系统中工序管理模块
 * @Description: 工序的创建、更新和查询
 */
import { request } from '@/utils/request';

/**
 * 新建或更新工序
 * @description 按processDTO的信息，新建或更新工序的信息。
 */
export function newOrUpdateProcess(data: ProcessDTO) {
  return request<RProcessVO>(`/process/newOrUpdateProcess`, {
    method: 'post',
    data,
  });
}

/**
 * 查找工序
 * @description 按动态条件，获取满足相应条件的工序的基本信息。各条件按与操作进行模糊查询。所有条件均为空时，返回全部记录。
 */
export function findProcessByCriteria(data: ProcessCriteria) {
  return request<REntityVOPage>(`/process/findProcessByCriteria`, {
    method: 'post',
    data,
  });
}

/**
 * 查找工序
 * @description 按Process的Id，精确查找数据记录。
 */
export function findAllProcessById(processId: Array<number>, params?: { processId: Array<number> }) {
  return request<RListProcessVO>(`/process/findAllProcessById/${processId}`, {
    method: 'get',
    params,
  });
}

/**
 * 分页显示全部工序
 * @description 分页显示所有的工序
 */
export function findAllProcess(pageNum: number, pageSize: number, params?: { pageNum: number; pageSize: number }) {
  return request<REntityVOPage>(`/process/findAllProcess/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除工序
 * @description 按 Process的Id，删除一个或多个工序的记录。
 */
export function deleteProcessById(processId: Array<number>, params?: { processId: Array<number> }) {
  return request<R>(`/process/deleteProcessById/${processId}`, {
    method: 'get',
    params,
  });
}
