<template>
  <div class="flex h-full flex-col px-10 pt-6">
    <div class="flex items-center">
      <h4 class="text-xl font-bold">会议报告</h4>
    </div>

    <div class="mt-6">
      <el-button type="primary" @click="onUpload"> 上传文件 </el-button>
    </div>

    <div class="mt-4 h-0 flex-1">
      <el-table :data="tableData" style="width: 100%" class="c-table-header mt-4">
        <el-table-column prop="name" label="文件名称" min-width="100px" />
        <el-table-column prop="type" label="文件类型" />
        <el-table-column prop="date" label="上传日期" />
        <el-table-column prop="size" label="文件大小" />
        <el-table-column label="操作">
          <template #default="{ row }">
            <el-button link type="primary" @click="onPreview(row)"> 预览 </el-button>
            <el-popconfirm title="确定删除此项？" @confirm="onDel(row)">
              <template #reference>
                <el-button link type="primary"> 删除 </el-button>
              </template>
            </el-popconfirm>
            <el-button link type="primary" @click="onDownload(row)"> 下载 </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup>
  import dayjs from 'dayjs';
  /* 会议报告 */
  const onUpload = () => {};
  const onDownload = () => {};

  const tableData = ref([
    {
      name: '2020年1月项目报告',
      type: 'pdf',
      date: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      size: 50,
    },
  ]);
  const onPreview = (row) => {};
  const onDel = (row) => {};
</script>
