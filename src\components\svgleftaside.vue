<template>
  <div class="menu h-full w-[220px] bg-w">
    <el-menu class="el-menu-vertical-demo" unique-opened="true" :default-active="openid">
      <div v-for="(item, index) in datalist" :key="item.id" style="width: 100%; height: 100%" @click="handleclik(item)">
        <el-sub-menu v-if="item.children && item.children.length > 0" :index="item.id">
          <template #title>
            <svgicon style="cursor: pointer; width: 25%; height: 30%" :icon-name="item.svgname" />
            <span>{{ item.title }}{{ item.selectnum !== 0 && item.selectnum ? '(' + item.selectnum + ')' : '' }}</span>
          </template>
          <div v-for="(citem, cindex) in item.children" :key="citem.id" @click.stop="handleclik(citem)">
            <el-menu-item :index="citem.id" :class="citem.selectnum !== 0 && citem.selectnum ? 'havenum' : ''">
              <svgicon style="cursor: pointer; width: 30%; height: 30%" :icon-name="citem.svgname" />
              <span>
                {{ citem.title }}{{ citem.selectnum !== 0 && citem.selectnum ? '(' + citem.selectnum + ')' : '' }}</span
              >
            </el-menu-item>
          </div>
        </el-sub-menu>
        <el-menu-item v-else :class="openid == item.id ? 'activemenu' : ''" :index="item.id">
          <svgicon style="cursor: pointer; width: 22%; height: 30%" :icon-name="item.svgname" />
          <span>{{ item.title }}{{ item.selectnum !== 0 && item.selectnum ? '(' + item.selectnum + ')' : '' }}</span>
        </el-menu-item>
      </div>
    </el-menu>
  </div>
</template>

<script setup>
  import { inject } from 'vue';
  let datalist = inject('svgleftasidedata');
</script>

<style scoped>
  .menu ::v-deep span {
    font-size: small;
  }
  .el-sub-menu ::v-deep .el-sub-menu__icon-arrow {
    left: -150px !important;
  }
  .el-menu-item {
    padding: 0px 25px !important;
  }
  /* .el-sub-menu ::v-deep .el-icon-arrow-down:before {
  content: "\e791";
} */
  .el-sub-menu ::v-deep .el-sub-menu__title {
    padding-left: 25px !important;
  }
  .el-menu-item.is-active {
    color: #007f99;
    background-color: #ebf5f7;
    font-weight: 900;
  }
  .havenum {
    color: #007f99;
  }
</style>
