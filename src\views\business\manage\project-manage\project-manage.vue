<template>
  <div class="flex h-full flex-col">
    <h2 class="flex h-[60px] items-center bg-w pl-[28px] text-xl font-bold">项目信息管理</h2>

    <div class="m-5 flex h-0 flex-1 flex-col rounded-md bg-w pt-5">
      <div class="px-10">
        <el-button type="primary" @click="onApply"> 项目审批 </el-button>
        <el-button type="primary" @click="onAdd"> 添加 </el-button>
      </div>

      <div class="mt-3 h-0 w-full flex-1 px-10">
        <el-table height="100%" :data="tableData" style="width: 100%" class="c-table-header">
          <el-table-column prop="name" label="项目名称" min-width="100px" />
          <el-table-column prop="principal" label="项目负责人" />
          <el-table-column prop="entity" label="项目主体单位" />
          <el-table-column prop="state" label="项目状态" />
          <el-table-column prop="detail" label="项目详情" />
          <el-table-column label="操作" width="80">
            <template #default="{ row }">
              <el-button link type="primary" @click="onEdit(row)"> 编辑 </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="pagination-bottom">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :page-size="pagination.pageSize"
          :total="tableData.length"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
  import { useRouter } from 'vue-router';
  const router = useRouter();

  //新机构注册审批
  const onApply = () => {
    // router.push({ name: 'PersonalProjectApplication' });
  };
  const onAdd = () => {};

  //表格
  const tableData = ref([
    {
      id: 1,
      name: '脑疾病项目研究',
      principal: '张三',
      entity: 'XXXXXXX大学',
      state: '进行中',
      detail: '项目详情项目详情项目详情',
    },
  ]);
  const onEdit = (row) => {};

  //页码
  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  const handleCurrentChange = (e) => {
    pagination.page = e;
  };
  const handleSizeChange = (e) => {
    pagination.pageSize = e;
  };
</script>
