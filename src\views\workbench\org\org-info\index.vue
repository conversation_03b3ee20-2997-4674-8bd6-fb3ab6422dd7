<template>
  <h2 class="flex h-[60px] items-center bg-w pl-5 text-xl font-bold">机构信息</h2>

  <div class="overflow-hidden bg-bac px-5">
    <div class="mt-5 rounded bg-w p-4">
      <div class="flex justify-between">
        <h3>基本信息</h3>
        <div v-if="isEditBase">
          <el-button @click="changeBaseEdit"> 取消 </el-button>
          <el-button type="primary" size="default" @click="onSaveBase"> 保存 </el-button>
        </div>
        <el-button v-else type="primary" size="default" @click="changeBaseEdit"> 编辑 </el-button>
      </div>

      <el-form
        v-if="isEditBase"
        ref="formRef"
        :model="form"
        :rules="rules"
        label-position="right"
        label-width="110px"
        hide-required-asterisk
        class="mt-4 px-10"
      >
        <div class="flex">
          <el-form-item label="机构名称:" prop="name" class="flex-1">
            <el-input v-model="form.name" placeholder="请输入机构名称" maxlength="200" />
          </el-form-item>
          <el-form-item label="管理员ID:" prop="adminId" class="ml-20 flex-1">
            <el-input v-model="form.adminId" placeholder="请输入管理员ID" maxlength="50" />
          </el-form-item>
        </div>
        <div class="flex">
          <el-form-item label="社会统一编码:" prop="suc" class="flex-1">
            <el-input v-model="form.suc" placeholder="请输入社会统一编码" maxlength="100" />
          </el-form-item>
          <el-form-item label="注册时间:" prop="date" class="ml-20 flex-1">
            <el-date-picker v-model="form.date" type="datetime" placeholder="请选择注册时间" style="width: 100%" />
          </el-form-item>
        </div>
      </el-form>

      <el-descriptions v-else direction="horizontal" :column="2">
        <el-descriptions-item label="机构名称">
          {{ form.name }}
        </el-descriptions-item>
        <el-descriptions-item label="管理员ID">
          {{ form.adminId }}
        </el-descriptions-item>
        <el-descriptions-item label="社会统一编码">
          {{ form.suc }}
        </el-descriptions-item>
        <el-descriptions-item label="注册时间">
          {{ form.date }}
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <div class="my-5 rounded bg-w p-4">
      <div class="flex justify-between">
        <h3>组织结构</h3>
        <div v-if="isEditOrg">
          <el-button @click="changeOrgEdit"> 取消 </el-button>
          <el-button type="primary" size="default" @click="onSaveOrg"> 保存 </el-button>
        </div>
        <el-button v-else type="primary" size="default" @click="changeOrgEdit"> 编辑 </el-button>
      </div>

      <el-tree :data="dataSource" node-key="id" default-expand-all :expand-on-click-node="false" class="mt-4">
        <template #default="{ node, data }">
          <div class="h-5 w-full" :class="{ 'edit-tree': isEditOrg }">
            <span class="tree-label">{{ node.label }}</span>

            <span class="actions ml-5">
              <el-link :underline="false" type="primary" @click="append(data)">添加</el-link>

              <template v-if="node.level > 1">
                <el-link class="ml-2" :underline="false" type="primary" @click="edit(node, data)">编辑</el-link>

                <el-popconfirm title="确定删除此节点?" @confirm="remove(node, data)">
                  <template #reference>
                    <el-link class="ml-2" type="danger" :underline="false">删除</el-link>
                  </template>
                </el-popconfirm>
              </template>
            </span>
          </div>
        </template>
      </el-tree>
    </div>
  </div>

  <el-dialog v-model="showNode" :title="dialogTitle" width="500px">
    <el-form ref="nodeFormRef" :model="nodeForm" :rules="nodeRules">
      <el-form-item label="名称" prop="name">
        <el-input v-model="nodeForm.name" placeholder="请输入名称" maxlength="100" />
      </el-form-item>
      <el-form-item label="排序" prop="orderNum">
        <el-input v-model="nodeForm.orderNum" placeholder="请输入排序" maxlength="5" />
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="showNode = false">取消</el-button>
        <el-button type="primary" @click="onConfirm">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
  /* 机构信息 */
  const isEditBase = ref(false);
  const changeBaseEdit = () => {
    isEditBase.value = !isEditBase.value;
  };
  const form = reactive({
    name: '桂林医学院附属医院',
    adminId: 'admin11',
    suc: '1254557545445454',
    date: '2024-1-10 11:20:56',
  });
  const rules = reactive({
    name: [{ required: true, message: '请输入机构名称', trigger: 'blur' }],
    adminId: [{ required: true, message: '请输入管理员ID', trigger: 'blur' }],
    suc: [{ required: true, message: '请输入社会统一编码', trigger: 'blur' }],
    date: [{ required: true, message: '请选择注册时间', trigger: 'blur' }],
  });
  const formRef = ref();
  const onSaveBase = () => {
    formRef.value.validate((valid) => {
      if (valid) {
        changeBaseEdit();
      }
    });
  };

  const dataSource = ref([
    {
      id: 1,
      label: '桂林医学院附属医院',
      children: [
        {
          id: 4,
          orderNum: 1,
          label: '内科',
          children: [
            {
              id: 9,
              orderNum: 1,
              label: '呼吸与危重病医学科',
            },
            {
              id: 10,
              orderNum: 2,
              label: '心血管内科一病区',
            },
          ],
        },
        {
          id: 4,
          orderNum: 2,
          label: '外科',
          children: [
            {
              id: 9,
              orderNum: 1,
              label: '肝胆胰外科',
            },
            {
              id: 10,
              orderNum: 2,
              label: '胸外科',
            },
          ],
        },
      ],
    },
  ]);
  let id = 1000;
  const showNode = ref();
  const currentNode = ref();
  const parentNode = ref();
  const nodeForm = reactive({
    name: '',
    orderNum: 0,
  });
  const nodeRules = reactive({
    name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
    orderNum: [{ required: true, message: '请输入排序', trigger: 'blur' }],
  });
  const nodeFormRef = ref();
  const dialogTitle = ref('');
  const onConfirm = () => {
    nodeFormRef.value.validate((valid) => {
      if (valid) {
        if (dialogTitle.value === '添加子级') {
          const newChild = {
            id: id++,
            label: nodeForm.name,
            orderNum: nodeForm.orderNum,
            children: [],
          };
          if (!currentNode.value.children) {
            currentNode.value.children = [];
          }
          currentNode.value.children.push(newChild);
          currentNode.value.children.sort((a, b) => a.orderNum - b.orderNum);
          dataSource.value = [...dataSource.value];
        } else {
          currentNode.value.label = nodeForm.name;
          currentNode.value.orderNum = nodeForm.orderNum;
          parentNode.value.data.children.sort((a, b) => a.orderNum - b.orderNum);
          dataSource.value = [...dataSource.value];
        }
        showNode.value = false;
      }
    });
  };
  const append = (data) => {
    dialogTitle.value = '添加子级';
    currentNode.value = data;
    showNode.value = true;
    nextTick(() => {
      nodeFormRef.value.resetFields();
    });
  };
  const edit = (node, data) => {
    parentNode.value = node.parent;
    dialogTitle.value = '编辑节点';
    currentNode.value = data;
    nodeForm.name = data.label;
    nodeForm.orderNum = data.orderNum;
    showNode.value = true;
  };
  const remove = (node, data) => {
    const parent = node.parent;
    const children = parent.data.children || parent.data;
    const index = children.findIndex((d) => d.id === data.id);
    children.splice(index, 1);
    dataSource.value = [...dataSource.value];
  };
  const isEditOrg = ref(false);
  const changeOrgEdit = () => {
    isEditOrg.value = !isEditOrg.value;
  };
  //保存机构
  const onSaveOrg = () => {
    changeOrgEdit();
  };
</script>

<style lang="scss" scoped>
  :deep(.el-descriptions) {
    .el-descriptions__body {
      background-color: transparent;
    }
    .el-descriptions__label {
      color: $color-tip-text;
    }
  }

  .actions {
    display: none;
  }

  .edit-tree {
    &:hover {
      .actions {
        display: inline-block;
      }
    }
  }
</style>
