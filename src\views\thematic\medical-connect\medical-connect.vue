<template>
  <div class="flex h-full flex-col overflow-hidden">
    <div class="bg-w pl-5 text-xl font-semibold">
      <h2 class="flex h-[60px] w-fit cursor-pointer items-center" @click="router.back()">
        <el-icon class="mr-2" color="#939899">
          <ArrowLeft />
        </el-icon>
        <span>关联数据源字段</span>
      </h2>
    </div>

    <div class="m-5 flex h-0 flex-1">
      <div class="mr-[10px] h-full w-[280px] rounded bg-w">
        <el-scrollbar height="100%" class="px-4 pt-4">
          <el-tree
            ref="treeRef"
            node-key="id"
            :props="treeProps"
            :load="loadNode"
            lazy
            :default-expanded-keys="defaultExpanded"
            :current-node-key="currentNode"
            @node-click="onNodeClick"
          />
        </el-scrollbar>
      </div>

      <div class="flex w-0 flex-1 flex-col rounded-md bg-w pt-5">
        <div class="flex px-10">
          <el-button type="primary" :disabled="checkList.length <= 0" :loading="batchLoading" @click="onConnect">
            关联字段
          </el-button>
          <el-button link type="primary" @click="viewConn"> 已关联字段 </el-button>
        </div>

        <div class="mt-3 h-0 w-full flex-1 px-10">
          <el-table
            v-loading="tableLoading"
            height="100%"
            :data="tableData"
            style="width: 100%"
            class="c-table-header"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" :selectable="checkSelectable" />
            <el-table-column type="index" width="55" label="序号" />
            <el-table-column prop="name" label="字段名称" />
            <el-table-column prop="dataType" label="字段类型" />
            <!-- <el-table-column label="是否源数据映射">
              <template #default="{ row }">
                <div>{{ row.hasFieldInDataSource ? '是' : '否' }}</div>
              </template>
            </el-table-column> -->
            <el-table-column prop="description" label="描述" min-width="100px" />
            <!-- <el-table-column label="操作" width="180">
              <template #default="{ row }">
                <el-button link type="primary" @click="onViewFiled(row)">查看字段</el-button>
                <el-button link type="primary" @click="onEdit(row)">编辑</el-button>
                <el-popconfirm title="确定删除此项？" @confirm="onDel(row)">
                  <template #reference>
                    <el-button link type="primary">删除</el-button>
                  </template>
                </el-popconfirm>
              </template>
            </el-table-column> -->
          </el-table>
        </div>

        <!-- <div class="pagination-bottom">
          <el-pagination
            background
            layout="total, sizes, prev, pager, next, jumper"
            :page-size="pagination.pageSize"
            :total="tableData.length"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div> -->
      </div>
    </div>
  </div>

  <el-dialog v-model="showConnected" title="已关联字段" @close="showConnected = false">
    <div class="mb-4">
      <el-button type="primary" :disabled="!connCheckList.length" :loading="cancelLoading" @click="cancelConn">
        取消关联
      </el-button>
    </div>
    <el-table
      v-loading="connectedLoading"
      height="100%"
      :data="connectedData"
      style="width: 100%"
      class="c-table-header"
      @selection-change="handleConnSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column type="index" width="55" label="序号" />
      <el-table-column prop="name" label="字段名称" />
      <el-table-column prop="dataType" label="字段类型" />
      <el-table-column prop="description" label="描述" min-width="100px" />
    </el-table>
  </el-dialog>
</template>

<script setup>
  import {
    findAllDb,
    findAllTableVOByDbId,
    associateCBDField,
    disassociateCBDField,
    findAllFieldVOByDbIdTblId,
    findCBDDefFieldByMfId,
  } from '@/api/index';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { useRouter } from 'vue-router';
  const router = useRouter();

  const props = defineProps({
    id: { type: String },
  });

  //目录
  const treeRef = ref();
  const treeProps = {
    isLeaf: 'leaf',
  };
  const defaultExpanded = ref([]);
  const currentNode = ref('');
  const loadNode = async (node, resolve) => {
    if (node.level === 0) {
      const data = await fetchDb();
      resolve(data);
    } else {
      const data = await fetchTable(node.data.id);
      resolve(data);
    }
  };

  async function fetchDb() {
    try {
      const { data } = await findAllDb();
      // defaultExpanded.value = [data[0].id];
      return data.map((item) => {
        item.label = item.databaseName;
        return item;
      });
    } catch (error) {
      console.log(error);
    }
  }
  async function fetchTable(id) {
    try {
      const { data } = await findAllTableVOByDbId(id);
      return data[0]
        .filter((item) => item.hasTableInDataSource)
        .map((item) => {
          item.label = item.tableName;
          item.leaf = true;
          return item;
        });
    } catch (error) {
      console.log(error);
    }
  }
  const onNodeClick = (data) => {
    if (data.leaf) {
      fetchField(data.dataBaseId, data.id);
    }
  };

  //表格
  const checkList = ref([]);
  const handleSelectionChange = (val) => {
    checkList.value = val;
  };
  function checkSelectable(row) {
    let flag = true;
    connectedData.value.forEach((item) => {
      if (item.id == row.id) {
        flag = false;
      }
    });
    return flag;
  }
  const tableData = ref([]);
  const tableLoading = ref(false);
  async function fetchField(dataBaseId, tableId) {
    try {
      tableLoading.value = true;
      const { data } = await findAllFieldVOByDbIdTblId(dataBaseId, tableId);
      tableData.value = data[0].filter((item) => item.hasFieldInDataSource);
    } catch (error) {
      console.log(error);
    } finally {
      tableLoading.value = false;
    }
  }
  //关联字段
  const batchLoading = ref(false);
  const onConnect = async () => {
    try {
      batchLoading.value = true;
      const cfdFieldId = checkList.value.map((item) => item.id);
      await associateCBDField(props.id, cfdFieldId);
      ElMessage({ type: 'success', message: '关联成功' });
      fetchConn();
    } catch (error) {
      console.log(error);
    } finally {
      batchLoading.value = false;
    }
  };
  //查看已关联
  const showConnected = ref(false);
  const viewConn = () => {
    fetchConn();
    showConnected.value = true;
  };
  const connCheckList = ref([]);
  const handleConnSelectionChange = (val) => {
    connCheckList.value = val;
  };
  const connectedData = ref([]);
  const connectedLoading = ref(false);
  async function fetchConn() {
    try {
      connectedLoading.value = true;
      const { data } = await findCBDDefFieldByMfId(props.id);
      connectedData.value = data;
    } catch (error) {
      console.log(error);
    } finally {
      connectedLoading.value = false;
    }
  }
  const cancelLoading = ref(false);
  const cancelConn = async () => {
    ElMessageBox.confirm('确定取消关联？', '操作提示', { type: 'warning' })
      .then(async () => {
        try {
          cancelLoading.value = true;
          const cfdFieldId = connCheckList.value.map((item) => item.id);
          await disassociateCBDField(props.id, cfdFieldId);
          ElMessage({ type: 'success', message: '取消关联成功' });
          fetchConn();
        } catch (error) {
          console.log(error);
        } finally {
          cancelLoading.value = false;
        }
      })
      .catch(() => {});
  };
  fetchConn();
</script>
