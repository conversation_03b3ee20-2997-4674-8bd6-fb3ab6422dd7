<template>
  <el-drawer v-model="drawer" class="relative" @close="onCancel">
    <template #header>
      <h4 class="text-m">上传成果</h4>
    </template>

    <el-form ref="FormRef" label-position="top" :model="form" :rules="rules">
      <el-form-item label="选择上传类型" prop="type">
        <el-select v-model="form.type" placeholder="请选择上传类型" style="width: 100%">
          <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>

      <template v-if="form.type === '1'">
        <el-form-item label="申请ID" prop="applyId">
          <el-input v-model="form.applyId" placeholder="请输入申请ID" maxlength="50" />
        </el-form-item>
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入标题" maxlength="200" />
        </el-form-item>
        <el-form-item label="第一作者" prop="firstAuthor">
          <el-input v-model="form.firstAuthor" placeholder="请输入第一作者" maxlength="50" />
        </el-form-item>
        <el-form-item label="杂志" prop="magazine">
          <el-input v-model="form.magazine" placeholder="请输入杂志" maxlength="200" />
        </el-form-item>
        <el-form-item label="出版年份" prop="publicationYear">
          <el-date-picker v-model="form.publicationYear" type="year" placeholder="请选择出版年份" style="width: 100%" />
        </el-form-item>
        <el-form-item label="DOI" prop="doi">
          <el-input v-model="form.doi" placeholder="请输入DOI" maxlength="200" />
        </el-form-item>
        <el-form-item label="Pubmed ID" prop="pubmedId">
          <el-input v-model="form.pubmedId" placeholder="请输入Pubmed ID" maxlength="200" />
        </el-form-item>
        <el-form-item label="文件" prop="fileList">
          <UploadButton v-model="form.manuscriptsFile" />
        </el-form-item>
      </template>

      <template v-if="form.type === '2'">
        <el-form-item label="派生变量数量" prop="count">
          <el-input v-model="form.count" placeholder="请输入派生变量数量" maxlength="10" />
        </el-form-item>
        <el-form-item label="衍生变量描述（即参与者人数、变量类型和测量单位等）" prop="derivedDesc">
          <el-input
            v-model="form.derivedDesc"
            placeholder="请输入衍生变量描述"
            maxlength="300"
            type="textarea"
            :rows="4"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="返回的变量是否是对先前返回的数据集的更新" prop="isUpdate">
          <el-radio-group v-model="form.isUpdate">
            <el-radio :label="true"> 是 </el-radio>
            <el-radio :label="false"> 否 </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="返回的变量是否会在以后更新以供他人使用" prop="isForOther">
          <el-radio-group v-model="form.isForOther">
            <el-radio :label="true"> 是 </el-radio>
            <el-radio :label="false"> 否 </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="此数据文件涉及的出版物（包括第一作者、标题、期刊、年份）">
          <el-input
            v-model="form.publication"
            placeholder="请输入此数据文件涉及的出版物"
            maxlength="300"
            type="textarea"
            :rows="4"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="发布这些数据的许可要求的详细信息">
          <el-input
            v-model="form.detail"
            placeholder="请输入发布这些数据的许可要求的详细信息"
            maxlength="300"
            type="textarea"
            :rows="4"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="文件" prop="fileList">
          <UploadButton v-model="form.dataFile" />
        </el-form-item>
      </template>

      <template v-if="form.type === '3'">
        <el-form-item label="描述（例如衍生变量的生成、分析结果的生成）" prop="desc">
          <el-input
            v-model="form.desc"
            placeholder="请输入描述"
            maxlength="300"
            type="textarea"
            :rows="4"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="此代码涉及的出版物（包括第一作者、标题、期刊、年份）">
          <el-input
            v-model="form.codePublication"
            placeholder="请输入此代码涉及的出版物"
            maxlength="300"
            type="textarea"
            :rows="4"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="语言" prop="language">
          <el-input v-model="form.language" placeholder="请输入语言" />
        </el-form-item>
        <el-form-item label="用于执行代码的平台">
          <el-select v-model="form.platform" placeholder="请选择用于执行代码的平台" style="width: 100%">
            <el-option v-for="item in platformOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="代码的DOI">
          <el-input v-model="form.codeDoi" placeholder="请输入代码的DOI" />
        </el-form-item>
        <el-form-item label="文件">
          <UploadButton v-model="form.codeFile" />
        </el-form-item>
      </template>

      <template v-if="form.type === '4'">
        <el-form-item label="文件说明" prop="fileDesc">
          <el-input
            v-model="form.fileDesc"
            placeholder="请输入文件说明"
            maxlength="300"
            type="textarea"
            :rows="4"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="此附加文件的出版物（包括第一作者、标题、期刊、年份）">
          <el-input
            v-model="form.additionPublication"
            placeholder="请输入此附加文件的出版物"
            maxlength="300"
            type="textarea"
            :rows="4"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="文件">
          <UploadButton v-model="form.additionFile" />
        </el-form-item>
      </template>
    </el-form>

    <template #footer>
      <el-button @click="onCancel"> 取消 </el-button>
      <el-button type="primary" @click="onSave"> 保存 </el-button>
    </template>
  </el-drawer>
</template>

<script setup>
  /*上传成果*/
  import { watchEffect } from 'vue';
  import UploadButton from '@/components/UploadButton.vue';

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
      required: true,
    },
    data: {
      type: Object,
      required: true,
    },
  });
  const drawer = ref(false);
  const typeOptions = ref([
    {
      value: '1',
      label: '手稿',
    },
    {
      value: '2',
      label: '数据文件',
    },
    {
      value: '3',
      label: '程序源代码',
    },
    {
      value: '4',
      label: '附加文件',
    },
  ]);
  const platformOptions = ref([
    {
      value: '1',
      label: 'Windows',
    },
    {
      value: '2',
      label: 'Linux',
    },
    {
      value: '3',
      label: 'MacOS',
    },
    {
      value: '4',
      label: 'APP',
    },
  ]);
  let form = reactive({
    type: '1',
    applyId: '',
    title: '',
    firstAuthor: '',
    magazine: '',
    publicationYear: '',
    doi: '',
    pubmedId: '',
    manuscriptsFile: [],

    count: '',
    derivedDesc: '',
    isUpdate: true,
    isForOther: true,
    publication: '',
    detail: '',
    dataFile: [],

    desc: '',
    codePublication: '',
    language: '',
    platform: '',
    codeDoi: '',
    codeFile: [],

    fileDesc: '',
    additionPublication: '',
    additionFile: [],
  });
  const rules = reactive({
    type: [{ required: true, message: '请选择上传类型', trigger: 'blur' }],
    applyId: [{ required: true, message: '请输入申请ID', trigger: 'blur' }],
    title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
    firstAuthor: [{ required: true, message: '请输入第一作者', trigger: 'blur' }],
    magazine: [{ required: true, message: '请输入杂志', trigger: 'blur' }],
    publicationYear: [{ required: true, message: '请选择出版年份', trigger: 'blur' }],
    doi: [{ required: true, message: '请输入DOI', trigger: 'blur' }],

    count: [{ required: true, message: '请输入派生变量数量', trigger: 'blur' }],
    derivedDesc: [{ required: true, message: '请输入衍生变量描述', trigger: 'blur' }],
    isUpdate: [{ required: true, message: '请选择', trigger: 'blur' }],
    isForOther: [{ required: true, message: '请选择', trigger: 'blur' }],

    desc: [{ required: true, message: '请输入描述', trigger: 'blur' }],
    language: [{ required: true, message: '请输入语言', trigger: 'blur' }],

    fileDesc: [{ required: true, message: '请输入文件说明', trigger: 'blur' }],
  });

  watchEffect(() => {
    drawer.value = props.modelValue;
    form = Object.assign(form, props.data);
  });

  const emit = defineEmits(['update:modelValue', 'success']);
  const onCancel = () => {
    emit('update:modelValue', false);
  };

  const FormRef = ref();
  const onSave = () => {
    FormRef.value.validate((valid) => {
      if (valid) {
        onCancel();
        emit('success');
      }
    });
  };
</script>
