<template>
  <div class="flex h-full flex-col">
    <h2 class="flex h-[60px] items-center bg-w pl-[28px] text-xl font-bold">个人用户管理</h2>

    <div class="m-5 flex h-0 flex-1 flex-col rounded-md bg-w pt-5">
      <div class="px-10">
        <el-button type="primary" @click="onApply"> 新用户注册审批 </el-button>
        <el-button type="primary" @click="onAdd"> 添加 </el-button>
      </div>

      <div class="mt-3 h-0 w-full flex-1 px-10">
        <el-table height="100%" :data="tableData" style="width: 100%" class="c-table-header">
          <el-table-column prop="userId" label="ID" min-width="100px" />
          <el-table-column prop="name" label="姓名" />
          <el-table-column prop="email" label="邮箱" />
          <el-table-column prop="contact" label="联系方式" />
          <el-table-column prop="org" label="所属机构" />
          <el-table-column label="操作" width="110">
            <template #default="{ row }">
              <el-button link type="primary" @click="onEdit(row)"> 编辑 </el-button>
              <el-popconfirm title="确定删除此项？" @confirm="onDel(row)">
                <template #reference>
                  <el-button link type="primary"> 删除 </el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="pagination-bottom">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :page-size="pagination.pageSize"
          :total="tableData.length"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
  import { useRouter } from 'vue-router';
  const router = useRouter();

  //新机构注册审批
  const onApply = () => {
    // router.push({ name: 'PersonalProjectApplication' });
  };
  const onAdd = () => {};

  //表格
  const tableData = ref([
    {
      id: 1,
      userId: '154623254542',
      name: '张三',
      email: '<EMAIL>',
      contact: '13245641231',
      org: '信息学院',
    },
  ]);
  const onEdit = (row) => {};
  const onDel = (row) => {};

  //页码
  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  const handleCurrentChange = (e) => {
    pagination.page = e;
  };
  const handleSizeChange = (e) => {
    pagination.pageSize = e;
  };
</script>
