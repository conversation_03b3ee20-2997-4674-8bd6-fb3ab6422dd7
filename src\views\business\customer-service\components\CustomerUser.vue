<template>
  <div class="mt-4 flex h-0 flex-1 rounded bg-w">
    <UserAside :id="id" ref="asideRef" @select="onAsideSelect" />

    <div class="flex h-full w-0 flex-1 flex-col pt-5">
      <el-scrollbar height="100%" class="h-0 flex-1">
        <div class="px-10 pb-10">
          <h3 class="mb-5 text-xl font-bold">申请信息</h3>

          <div class="mb-4 flex items-center">
            <div class="h-[13px] w-[3px] bg-p" />
            <span class="ml-2">个人信息</span>
          </div>
          <div v-if="applicationInfo.id" class="text-sm">
            <el-row>
              <el-col :span="12">
                <div class="flex flex-col justify-start">
                  <span class="mb-2 text-[#939899]">用户名</span>
                  <span>{{ applicationInfo.userName }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="flex flex-col justify-start">
                  <span class="mb-2 text-[#939899]">邮箱</span>
                  <span>{{ applicationInfo.email }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row class="mt-4">
              <el-col :span="12">
                <div class="flex flex-col justify-start">
                  <span class="mb-2 text-[#939899]">姓名</span>
                  <span>{{ applicationInfo.name }}</span>
                </div>
              </el-col>
              <!-- <el-col :span="12">
                <div class="flex flex-col justify-start">
                  <span class="mb-2 text-[#939899]">性别</span>
                  <span>{{ applicationInfo.sex }}</span>
                </div>
              </el-col> -->
              <el-col :span="12">
                <div class="flex flex-col justify-start">
                  <span class="mb-2 text-[#939899]">手机号</span>
                  <span>{{ applicationInfo.phone }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row class="mt-4">
              <el-col :span="12">
                <div class="flex flex-col justify-start">
                  <span class="mb-2 text-[#939899]">证件类型</span>
                  <!-- <span>{{ applicationInfo.documentType }}</span> -->
                  <span>身份证</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="flex flex-col justify-start">
                  <span class="mb-2 text-[#939899]">证件号码</span>
                  <span>{{ applicationInfo.idCard }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row class="mt-4">
              <el-col :span="24">
                <div v-loading="fileLoading" class="flex flex-col justify-start">
                  <span class="mb-2 text-[#939899]">证明材料</span>
                  <FileList v-if="fileList.length > 0" :list="fileList" @view="viewFile" @download="downloadFile" />
                  <span v-else>无</span>
                </div>
              </el-col>
            </el-row>
            <!-- <el-row class="mt-4">
              <el-col :span="12">
                <div class="flex flex-col justify-start">
                  <span class="mb-2 text-[#939899]">学历</span>
                  <span>{{ applicationInfo.degree }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="flex flex-col justify-start">
                  <span class="mb-2 text-[#939899]">职称</span>
                  <span>{{ applicationInfo.jobTitle }}</span>
                </div>
              </el-col>
            </el-row> -->
          </div>
          <div v-else>暂无数据</div>
          <!-- <div class="mb-4 mt-10 flex items-center">
            <div class="h-[13px] w-[3px] bg-p" />
            <span class="ml-2">所属机构</span>
          </div>
          <div class="text-sm">
            <el-row>
              <el-col :span="12">
                <div class="flex flex-col justify-start">
                  <span class="mb-2 text-[#939899]">机构名称</span>
                  <span>{{ adminInfo.name }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="flex flex-col justify-start">
                  <span class="mb-2 text-[#939899]">机构邮箱</span>
                  <span>{{ adminInfo.email }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row class="mt-4">
              <el-col :span="12">
                <div class="flex flex-col justify-start">
                  <span class="mb-2 text-[#939899]">部门</span>
                  <span>{{ adminInfo.department }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="flex flex-col justify-start">
                  <span class="mb-2 text-[#939899]">职位</span>
                  <span>{{ adminInfo.position }}</span>
                </div>
              </el-col>
            </el-row>
          </div> -->
        </div>
      </el-scrollbar>

      <div v-if="applicationInfo.id" class="border-t border-border px-10 py-5">
        <template v-if="applicationInfo.rltSybole.lockFlag === '1'">
          <div class="mb-1 text-sm font-bold">审批意见:</div>
          <el-input
            v-model="opinion"
            :rows="4"
            type="textarea"
            placeholder="请输入审批意见"
            show-word-limit
            maxlength="200"
          />
          <div class="mt-5">
            <el-button plain :icon="Check" color="#007f99" :loading="agreeeLoading" @click="onAgree(0)">
              同意
            </el-button>
            <el-button plain :icon="Close" color="#e74c4c" :loading="refuseLoading" @click="onAgree(3)">
              拒绝
            </el-button>
          </div>
        </template>
        <template v-else>
          <div class="text-sm font-bold text-tip">审批意见:</div>
          <div v-loading="opinionLoading" class="mt-2 text-sm">
            {{ opinion || '无' }}
          </div>
        </template>
      </div>
    </div>
  </div>

  <el-image-viewer v-if="showViewer" style="width: 100px; height: 100px" :url-list="imageUrl" @close="closeImgViewer">
  </el-image-viewer>
</template>

<script setup lang="ts">
  import { Check, Close } from '@element-plus/icons-vue';
  import UserAside from './CustomerUserAside.vue';
  import FileList from '@/components/FileList.vue';
  import UploadButton from '@/components/UploadButton.vue';
  import { ElMessage, ElMessageBox, UploadFiles } from 'element-plus';
  import {
    findAttachmentMaterialByEnrollmentId,
    findVerifyResult,
    verifyEnroll,
    findEnrollmentByUserId,
    getAttachmentMaterialfile,
  } from '@/api/index';
  import { useUsers } from '@/store/user-info.js';
  import { convertToImage } from '@/utils/util';
  import request, { download } from '@/utils/request';
  const store = useUsers();

  const id = ref('');
  const enrollmentId = ref(0);
  let applicationInfo = reactive({
    id: 0,
    userName: '',
    email: '',
    phone: '',
    rltSybole: { lockFlag: '1' },
    name: '',
    idCard: '',
    opinion: '',
  });
  const onAsideSelect = (e) => {
    Object.assign(applicationInfo, e);
    if (applicationInfo.id) {
      fetchFiles();
    }
    opinion.value = '';
    if (e.rltSybole.lockFlag !== '1') {
      fetchOpinion();
    }
  };

  const adminInfo = reactive({
    id: 1,
    name: '华中科技大学同济医学院附属协和医院',
    email: '<EMAIL>',
    department: 'XXXXX部门 / 技术部 / 开发组',
    position: '开发工程师',
  });

  //证明材料
  const fileList = ref<FileListData[]>([]);
  const fileLoading = ref(false);
  async function fetchFiles() {
    try {
      fileLoading.value = true;
      const res = await findEnrollmentByUserId(applicationInfo.id, 1, 1, 100, {} as any);
      const data2: any = res.data;
      if (!data2 || !data2.content || data2.content.length === 0) {
        fileLoading.value = false;
        return;
      }
      const id = data2.content[0].id;
      enrollmentId.value = id;

      const { data } = await findAttachmentMaterialByEnrollmentId(id, 1, 100, {} as any);
      let arr: FileListData[] = [];
      if (data?.content) {
        data.content.forEach((item: any) => {
          if (!item.contentEmpty) {
            arr.push({
              name: item.title + '.' + item.documentType,
              id: item.id,
              type: item.documentType,
            });
          }
        });
      }
      fileList.value = arr;
    } catch (error) {
      console.log(error);
    } finally {
      fileLoading.value = false;
    }
  }

  const imageUrl = ref<string[]>([]);
  const showViewer = ref(false);
  async function viewFile(item: FileListData) {
    try {
      fileLoading.value = true;
      const { data } = await getAttachmentMaterialfile(item.id, {} as any);
      if (data) {
        imageUrl.value = [`data:image/${item.type};base64,` + data];
        showViewer.value = true;
      }
    } catch (error) {
      console.log(error);
    } finally {
      fileLoading.value = false;
    }
  }
  const closeImgViewer = () => {
    showViewer.value = false;
  };

  async function downloadFile(item: FileListData) {
    try {
      fileLoading.value = true;
      await download(`/attachmentMaterial/downloadAttachmentMaterialFile/${item.id}`, {
        method: 'post',
      });
      ElMessage({ type: 'success', message: '下载成功' });
    } catch (error) {
      console.log(error);
    } finally {
      fileLoading.value = false;
    }
  }

  const opinion = ref('');
  const opinionLoading = ref(false);
  //查找用户审批意见
  const fetchOpinion = async () => {
    try {
      opinionLoading.value = true;
      const { data } = await findVerifyResult(applicationInfo.id, {} as any);
      opinion.value = data?.description ?? '';
    } catch (error) {
      console.log(error);
    } finally {
      opinionLoading.value = false;
    }
  };

  //同意、拒绝
  const asideRef = ref();
  const agreeeLoading = ref(false);
  const refuseLoading = ref(false);
  const onAgree = (conclusion: number) => {
    if (opinion.value === '') {
      ElMessage({ type: 'warning', message: '请输入审批意见' });
      return;
    }

    ElMessageBox.confirm(`确定${conclusion === 0 ? '同意' : '拒绝'}该申请？`, '操作提示', { type: 'warning' })
      .then(async () => {
        try {
          if (conclusion === 0) {
            agreeeLoading.value = true;
          } else {
            refuseLoading.value = true;
          }
          await verifyEnroll({
            userId: store.user.id,
            enrollmentId: enrollmentId.value,
            conclusion,
            description: opinion.value,
          });
          ElMessage({ type: 'success', message: '操作成功' });
          asideRef.value.fetchData();
        } catch (error) {
          console.log(error);
        } finally {
          if (conclusion === 0) {
            agreeeLoading.value = false;
          } else {
            refuseLoading.value = false;
          }
        }
      })
      .catch(() => {});
  };
</script>

<style lang="scss" scoped></style>
