<template>
  <div class="tag-group">
    <el-tag
      v-for="tag in dynamicTags"
      :key="tag"
      class="mx-1"
      :closable="!readonly"
      :disable-transitions="false"
      @close="handleClose(tag)"
    >
      {{ tag }}
    </el-tag>
    <template v-if="!readonly">
      <el-input
        v-if="inputVisible"
        ref="InputRef"
        v-model="inputValue"
        class="ml-1"
        style="width: 80px"
        size="small"
        @keyup.enter="handleInputConfirm"
        @blur="handleInputConfirm"
      />
      <template v-else>
        <el-button v-if="dynamicTags.length < maxlength" class="ml-1" size="small" @click="showInput">
          + {{ addText }}
        </el-button>
      </template>
    </template>
  </div>
</template>

<script setup>
  const props = defineProps({
    modelValue: {
      type: String,
      default: '',
    },
    addText: {
      type: String,
      default: '新标签',
    },
    maxlength: {
      type: [String, Number],
      default: 4,
    },
    readonly: {
      type: Boolean,
      default: false,
    },
  });
  const emit = defineEmits(['update:modelValue']);

  const dynamicTags = ref([]);
  watchEffect(() => {
    dynamicTags.value = props.modelValue ? props.modelValue.split(',') : [];
  });

  const inputValue = ref('');
  const inputVisible = ref(false);
  const InputRef = ref();

  const emitUpdate = () => {
    emit('update:modelValue', dynamicTags.value.join(','));
  };

  const handleClose = (tag) => {
    dynamicTags.value.splice(dynamicTags.value.indexOf(tag), 1);
    emitUpdate();
  };

  const showInput = () => {
    inputVisible.value = true;
    nextTick(() => {
      InputRef.value.input.focus();
    });
  };

  const handleInputConfirm = () => {
    if (inputValue.value) {
      dynamicTags.value.push(inputValue.value);
      emitUpdate();
    }
    inputVisible.value = false;
    inputValue.value = '';
  };
</script>
