<template>
  <div class="search">
    <h2 class="title">数据搜索</h2>
    <div class="input-container">
      <el-input
        v-model="searchValue"
        size="large"
        class="input-search"
        maxlength="50"
        placeholder="请输入关键词"
        :suffix-icon="Search"
        @change="onSearch"
      />
    </div>
  </div>
</template>

<script setup>
  import { Search } from '@element-plus/icons-vue';

  const searchValue = ref('');

  function onSearch() {
    console.log(searchValue.value);
  }
</script>

<style scoped lang="scss">
  .search {
    background: url('@/assets/img/data-resource/bg-search.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    height: 260px;
    width: 100%;
    padding-top: 56px;
  }

  .title {
    text-align: center;
    font-size: 36px;
    font-weight: 700;
    line-height: 52px;
  }

  .input-container {
    margin-top: 43px;
    display: flex;
    justify-content: center;

    :deep(.el-input--large .el-input__wrapper) {
      --el-input-height: 48px;
      border-radius: 100px;
      padding-left: 30px;
      padding-right: 30px;
      font-size: 16px;
      box-shadow: 0px 2px 12px var(--el-box-shadow-light);
    }

    :deep(.el-input__suffix) {
      color: $color-primary;
      font-size: 19px;
    }
  }

  .input-search {
    width: 840px;
  }
</style>
