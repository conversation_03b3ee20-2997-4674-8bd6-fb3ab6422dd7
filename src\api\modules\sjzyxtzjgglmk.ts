/*
 * @OriginalName: 数据资源系统中机构管理模块
 * @Description: 机构的创建、更新和查询
 */
import { request } from '@/utils/request';

/**
 * 移除项机构中的用户
 * @description 按Org的Id，删除一个或多个机构用户的关联记录。移除项目中的团队。移除项机构中的用户。
 */
export function deleteOrgUserById(data: Array<OrgUserId>) {
  return request<R>(`/org/removeUserFromOrg`, {
    method: 'post',
    data,
  });
}

/**
 * 新建或更新机构
 * @description 按orgDTO的信息，新建或更新机构的信息。
 */
export function newOrUpdateOrg(data: OrgDTO) {
  return request<ROrgVO>(`/org/newOrUpdateOrg`, {
    method: 'post',
    data,
  });
}

/**
 * 查找机构
 * @description 按动态条件，获取满足相应条件的机构的基本信息。如果有主键，按主键精确查找，若无主键，则其它条件按与操作进行模糊查询。所有条件均为空时，返回全部记录。
 */
export function findOrgVOByCriteria_1(data: OrgCriteria) {
  return request<REntityVOPage>(`/org/findOrgVOByCriteria`, {
    method: 'post',
    data,
  });
}

/**
 * 向机构内添加一个用户
 * @description 按orgUserDTO的信息，新建机构用户关联的信息，向机构内添加一个用户。
 */
export function newOrUpdateOrgUser(data: Array<OrgUserDTO>) {
  return request<RListOrgUserVO>(`/org/addUserToOrg`, {
    method: 'post',
    data,
  });
}

/**
 * 查找机构内的用户
 * @description 按Org的Id，查找组织机构内的用户。后续设计为分页查找。
 */
export function findUserByOrgId(orgId: number, params?: { orgId: number }) {
  return request<RListOrgUserVO>(`/org/findUserByOrgId/${orgId}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找机构内的团队
 * @description 按Org的Id，查找组织机构内的团队。后续设计为分页查找。
 */
export function findTeamByOrgId(orgId: number, params?: { orgId: number }) {
  return request<RListTeamVO>(`/org/findTeamByOrgId/${orgId}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找机构
 * @description 按Org的Id，精确查找数据记录。
 */
export function findOrgById(orgId: number, params?: { orgId: number }) {
  return request<ROrgVO>(`/org/findOrgById/${orgId}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找机构
 * @description 按Org的Id，精确查找数据记录。
 */
export function findAllOrgById(orgId: Array<number>, params?: { orgId: Array<number> }) {
  return request<RListOrgVO>(`/org/findAllOrgById/${orgId}`, {
    method: 'get',
    params,
  });
}

/**
 * 分页显示全部机构
 * @description 分页显示全部机构
 */
export function findAllOrg(pageNum: number, pageSize: number, params?: { pageNum: number; pageSize: number }) {
  return request<REntityVOPage>(`/org/findAllOrg/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除机构
 * @description 按Org的Id，删除一个或多个机构的记录。
 */
export function deleteOrgById(orgId: Array<number>, params?: { orgId: Array<number> }) {
  return request<R>(`/org/deleteOrgById/${orgId}`, {
    method: 'get',
    params,
  });
}
