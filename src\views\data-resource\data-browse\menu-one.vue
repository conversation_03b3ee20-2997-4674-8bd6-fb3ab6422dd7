<template>
  <div class="px-10 pt-5">
    <el-breadcrumb :separator-icon="ArrowRight">
      <el-breadcrumb-item v-for="(item, index) in breadList" :key="index">
        {{ item.label }}
      </el-breadcrumb-item>
    </el-breadcrumb>

    <h2 class="mt-[25px] text-[28px] font-bold">{{ breadList[0].label }}（ID-{{ id }}）</h2>
    <p class="mt-5 border-b border-border pb-[26px]">
      {{ description }}
    </p>

    <template v-if="tableData.length > 0">
      <h3 class="mt-[26px] text-xl">子类别（{{ tableData.length }}）</h3>
      <el-table
        :data="tableData"
        style="width: 100%"
        class="c-table-header mb-4 mt-4"
        @selection-change="handleSelectionChange"
      >
        <el-table-column prop="id" label="类别ID">
          <template #default="{ row }">
            <el-button link type="primary" @click="gotoPage(row)">
              {{ row.id }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="title" label="名称" min-width="100px" />
        <el-table-column prop="dataItemCount" label="数据字段" />
        <el-table-column prop="childrenCount" label="子类别" />
        <el-table-column prop="description" label="说明" />
        <el-table-column v-if="isSelectMode" type="selection" width="55" />
      </el-table>
    </template>

    <!-- <h3 class="mt-[26px] text-xl">数据统计</h3>
    <ul class="chart-wrapper">
      <li class="chart" v-for="(item, index) in optionList" :key="index">
        <Chart :option="item" />
      </li>
    </ul> -->
  </div>
</template>

<script setup>
  /* 数据浏览-一层目录 */
  import { getCatalogue, getChildCatalogue } from '@/api/index';
  import { ArrowRight } from '@element-plus/icons-vue';
  // import Chart from '@/components/Chart.vue';
  // import { line, bar, pie, barLine, lineArea, scatter } from './components/chart';
  // const optionList = ref([line, bar, pie, barLine, lineArea, scatter]);
  import { useRouter } from 'vue-router';
  const router = useRouter();
  import { useDataBrowse } from '@/store/data-browse';
  const store = useDataBrowse();
  const isSelectMode = computed(() => {
    return store.dataBrowse.isSelectMode;
  });
  const id = computed(() => {
    return store.dataBrowse.menuId;
  });

  const breadList = ref([{ label: '', url: '' }]);

  const tableData = ref([
    // {
    //   typeId: '2',
    //   name: '持续特征',
    //   count: '4',
    // },
    // {
    //   typeId: '10094',
    //   name: '基线特征',
    //   count: '6+25',
    // },
  ]);
  const checkList = ref([]);
  const handleSelectionChange = (val) => {
    checkList.value = val;
  };

  const gotoPage = (row) => {
    router.push({ name: 'DataBrowDataType' });
    store.setData({ menuId: row.id });
    store.informMenuChange();
  };

  const description = ref('');
  let childrenCount = 0;
  onMounted(async () => {
    await fetchData();
    if (childrenCount > 0) {
      fetchChildren();
    }
  });
  async function fetchData() {
    try {
      const { data } = await getCatalogue(id.value);
      breadList.value[0].label = data.title;
      description.value = data.description;
      childrenCount = data.childrenCount;
    } catch (error) {
      console.log(error);
    }
  }
  async function fetchChildren() {
    try {
      const { data } = await getChildCatalogue(id.value);
      tableData.value = data;
    } catch (error) {
      console.log(error);
    }
  }
</script>

<style lang="scss" scoped>
  .chart-wrapper {
    margin-top: 18px;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;

    .chart {
      height: 380px;
    }
  }
</style>
