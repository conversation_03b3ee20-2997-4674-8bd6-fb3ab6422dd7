<template>
  <div class="pl-10 pt-4">
    <h4 class="text-xl font-bold">基本信息</h4>

    <div class="my-5 flex w-full items-center">
      <div class="h-[13px] w-[3px] bg-p" />
      <span class="ml-2">{{ isPersonal ? '个人信息' : '机构信息' }}</span>
    </div>

    <el-form
      ref="baseFormRef"
      v-loading="loading"
      class="base-form"
      :class="{ 'is-text': isEdit === false }"
      label-position="top"
      :inline="true"
      :model="baseForm"
      :rules="baseRules"
    >
      <el-form-item :label="isPersonal ? '姓名：' : '机构名称'" prop="name">
        <el-input v-show="isEdit" v-model="baseForm.name" placeholder="请输入" maxlength="20" />
        <div v-show="!isEdit">
          {{ baseForm.name }}
        </div>
      </el-form-item>
      <!-- <el-form-item label="性别：" prop="sex">
        <el-select v-show="isEdit" v-model="form.sex" class="w-full" placeholder="请选择性别">
          <el-option v-for="item in sexOptions" :key="item" :label="item" :value="item" />
        </el-select>
        <div v-show="!isEdit">
          {{ form.sex }}
        </div>
      </el-form-item> -->
      <!-- <el-form-item label="证件类型：" prop="certificatetype">
        <el-select v-show="isEdit" v-model="form.certificatetype" class="w-full" placeholder="请选择证件类型">
          <el-option v-for="item in idTypeOptions" :key="item" :label="item" :value="item" />
        </el-select>
        <div>身份证</div>
      </el-form-item> -->
      <el-form-item :label="isPersonal ? '证件号码：' : '统一社会信用代码'" prop="idCard">
        <el-input v-show="isEdit" v-model="baseForm.idCard" placeholder="请输入" />
        <div v-show="!isEdit">
          {{ baseForm.idCard }}
        </div>
      </el-form-item>
      <!-- <el-form-item label="学历" prop="education">
        <el-select v-show="isEdit" v-model="form.education" class="w-full" placeholder="请选择学历">
          <el-option v-for="item in educationOptions" :key="item" :label="item" :value="item" />
        </el-select>
        <div v-show="!isEdit">
          {{ form.education }}
        </div>
      </el-form-item> -->
      <!-- <el-form-item label="职称：" prop="professional">
        <el-select v-show="isEdit" v-model="form.professional" class="w-full" placeholder="请选择职称">
          <el-option v-for="item in rankOptions" :key="item" :label="item" :value="item" />
        </el-select>
        <div v-show="!isEdit">
          {{ form.professional }}
        </div>
      </el-form-item> -->
      <el-form-item :label="isPersonal ? '手机号：' : '联系方式：'" prop="phone">
        <el-input v-show="isEdit" v-model="baseForm.phone" placeholder="请输入" maxlength="11" />
        <div v-show="!isEdit">
          {{ baseForm.phone }}
        </div>
      </el-form-item>
      <el-form-item label="邮箱：" prop="email">
        <el-input v-show="isEdit" v-model="baseForm.email" placeholder="请输入邮箱" />
        <div v-show="!isEdit">
          {{ baseForm.email }}
        </div>
      </el-form-item>
      <el-form-item label="地址：" prop="address">
        <el-input v-show="isEdit" v-model="baseForm.address" placeholder="请输入" maxlength="200" />
        <div v-show="!isEdit">
          {{ baseForm.address }}
        </div>
      </el-form-item>
      <el-form-item v-if="isPersonal" label="昵称：" prop="nickName">
        <el-input v-show="isEdit" v-model="baseForm.nickName" placeholder="请输入昵称" maxlength="10" />
        <div v-show="!isEdit">
          {{ baseForm.nickName }}
        </div>
      </el-form-item>
    </el-form>

    <div class="pb-2">
      <template v-if="isEdit">
        <el-button color="#007f99" type="primary" @click="onSubmit"> 保存 </el-button>
        <el-button @click="cancelBase"> 取消 </el-button>
      </template>
      <template v-else>
        <el-button color="#007f99" type="primary" :loading="saveLoading" @click="changeEdit(true)"> 编辑 </el-button>
      </template>
    </div>

    <!-- <template v-if="store.isEnable">
      <div class="mb-5 flex items-center">
        <div class="h-[13px] w-[3px] bg-p" />
        <span class="ml-2">所属机构</span>
        <div v-if="isEdit" class="ml-5 flex cursor-pointer select-none items-center justify-start" @click="onAdd">
          <el-icon color="#007f99">
            <CirclePlus />
          </el-icon>
          <span class="ml-1 text-sm text-p">添加机构</span>
        </div>
      </div>
      <ul class="org-list">
        <li v-for="(item, index) in formList" :key="item.id" class="mb-5 flex items-center">
          <div
            class="relative w-[1000px] rounded-sm bg-[#f5f7fa] pl-10 pr-0 pt-4"
            :class="{ 'is-text': item.isEdit === false }"
          >
            <el-form ref="orgForm" :inline="true" label-position="top" :model="item" :rules="listRules">
              <el-form-item label="机构名称" prop="name">
                <el-select v-show="item.isEdit" v-model="item.name" class="w-full" placeholder="请选择机构名称">
                  <el-option v-for="org in orgOptions" :key="org" :label="org" :value="org" />
                </el-select>
                <div v-show="!item.isEdit">
                  {{ item.name }}
                </div>
              </el-form-item>
              <el-form-item label="机构邮箱" prop="email">
                <el-input v-show="item.isEdit" v-model="item.email" placeholder="请输入机构邮箱" />
                <div v-show="!item.isEdit">
                  {{ item.email }}
                </div>
              </el-form-item>
              <el-form-item label="部门">
                <el-input v-show="item.isEdit" v-model="item.department" placeholder="请输入部门" />
                <div v-show="!item.isEdit">
                  {{ item.department || '暂无' }}
                </div>
              </el-form-item>
              <el-form-item label="职位">
                <el-input v-show="item.isEdit" v-model="item.position" placeholder="请输入职位" />
                <div v-show="!item.isEdit">
                  {{ item.position || '暂无' }}
                </div>
              </el-form-item>
            </el-form>

            <div v-if="item.pass && !item.isEdit" class="status-bar">
              <div class="status-innner">通过</div>
            </div>
          </div>

          <div v-if="isEdit" class="select-none text-sm text-p">
            <template v-if="item.isEdit">
              <a class="ml-5" @click="onSave(item, index)">保存</a>
              <el-popconfirm v-if="formList.length > 1" title="确定删除此项？" @confirm="onDel(item, index)">
                <template #reference>
                  <a class="ml-5">删除</a>
                </template>
              </el-popconfirm>
            </template>

            <template v-else>
              <a class="ml-5" @click="onEdit(item, index)">编辑</a>
              <el-popconfirm v-if="formList.length > 1" title="确定删除此项？" @confirm="onDel(item, index)">
                <template #reference>
                  <a class="ml-5">删除</a>
                </template>
              </el-popconfirm>
              <a class="ml-5" :class="{ 'text-disable': item.pass }" @click="onAudit(item)">送审</a>
            </template>
          </div>
        </li>
      </ul>
    </template> -->
  </div>
</template>

<script setup lang="ts">
  /* 基本信息 */
  import { getUserInfor, newOrUpdateUser } from '@/api/index';
  import { useUsers } from '@/store/user-info';
  const store = useUsers();
  import { ElMessage, FormInstance } from 'element-plus';
  import { validateIDCard, validatePhoneNumber } from '@/utils/validator';
  // const sexOptions = ['男', '女'];
  // const idTypeOptions = ['身份证'];
  // const educationOptions = ['本科', '专科', '硕士', '博士'];
  // const rankOptions = ['初级工程师', '中级工程师', '高级工程师'];
  const orgOptions = ['xx单位'];
  const isEdit = ref(false);
  const isPersonal = computed(() => store.user.userType === '个人用户');

  const loading = ref(false);
  let baseForm = reactive({
    // education: '',
    // professional: '',
    // certificatetype: '',
    // sex: '',
    // userName: '',
    // password: '',
    id: 0,
    name: '',
    idCard: '',
    phone: '',
    address: '',
    nickName: '',
    email: '',
  });
  let baseRules = {
    name: [{ required: true, message: '请输入' }],
    email: [{ required: true, message: '邮箱格式不正确', type: 'email' }],
    // certificatetype: [{ required: true, message: '请选择证件类型' }],
    // idCard: [{ required: true, validator: validateIDCard }],
    idCard: [{ required: true }],
    phone: [
      {
        required: true,
        // validator: validatePhoneNumber,
      },
    ],
  };

  interface FormListItem {
    id: number;
    name: string;
    email: string;
    department: string;
    position: string;
    pass: boolean;
    isEdit: boolean;
  }
  const formList = ref<FormListItem[]>([
    // {
    //   id: 1,
    //   name: '华中科技大学同济医学院附属协和医院',
    //   email: '<EMAIL>',
    //   department: 'XXXXX部门 / 技术部 / 开发组',
    //   position: '开发工程师',
    //   pass: true,
    //   isEdit: false,
    // },
  ]);
  const listRules = {
    name: [{ required: true, message: '请输入机构名称' }],
    email: [
      { required: true, message: '请输入机构邮箱' },
      { type: 'email', message: '邮箱格式有误' },
    ],
  };
  const orgForm = ref<FormInstance[]>();
  const onSave = async (item, index) => {
    try {
      await orgForm.value![index].validate();
      item.isEdit = false;
    } catch (error) {
      // console.log(error);
    }
  };
  const onCancel = (item, index) => {
    item.isEdit = false;
  };
  const onEdit = (item, index) => {
    item.isEdit = true;
  };
  const onDel = (item, index) => {
    formList.value.splice(index, 1);
  };
  const onAudit = (item) => {
    if (item.pass === true) {
      return;
    }
  };
  const onAdd = () => {
    formList.value.push({
      id: 0,
      name: '',
      email: '',
      department: '',
      position: '',
      pass: false,
      isEdit: true,
    });
  };

  const baseFormRef = ref<FormInstance>();
  const saveLoading = ref(false);
  const cancelBase = () => {
    fetchBase();
    isEdit.value = false;
  };
  const onSubmit = async () => {
    try {
      saveLoading.value = true;
      await baseFormRef.value!.validate();
      await newOrUpdateUser(baseForm);
      ElMessage({ type: 'success', message: '保存成功' });
      cancelBase();
    } catch (error) {
      // console.log(error);
    } finally {
      saveLoading.value = false;
    }
  };
  const changeEdit = (value) => {
    isEdit.value = value;
  };

  fetchBase();
  async function fetchBase() {
    try {
      loading.value = true;
      const { data } = await getUserInfor(store.user.username, { userName: store.user.username });
      if (data) {
        Object.keys(data).forEach((key) => {
          if (baseForm.hasOwnProperty(key)) {
            baseForm[key] = data[key];
          }
        });
      }
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }
</script>

<style scoped lang="scss">
  .base-form {
    .el-form-item {
      width: 480px;
    }
  }

  :deep(.el-input__inner) {
    color: $color-main-text;
  }

  .org-list {
    .el-form-item {
      width: 440px;
    }
  }

  .is-text {
    --el-text-color-regular: #939899;
  }

  $status-success: #24b383;
  .status-bar {
    width: 72px;
    height: 72px;
    border-radius: 100%;
    border: 4px solid rgba(36, 179, 131, 0.5);
    position: absolute;
    top: 50%;
    right: 40px;
    transform: translateY(-50%);
    padding: 6px;
    color: $status-success;

    .status-innner {
      height: 100%;
      border-radius: 100%;
      border: 1px solid $status-success;
      padding: 8px;
      transform: rotate(-40deg);
    }
  }
</style>
