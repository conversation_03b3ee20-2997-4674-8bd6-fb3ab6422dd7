<template>
  <el-drawer v-model="drawer" class="relative" @close="onCancel">
    <template #header>
      <h4 class="text-m">{{ action }}脱敏算法模板</h4>
    </template>

    <el-form ref="formRef" label-position="top" :model="form" :rules="rules">
      <el-form-item label="模板名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入模板名称" maxlength="50" :disabled="readonly" />
      </el-form-item>
      <el-form-item label="脱敏算法" prop="arithmetic">
        <el-select v-model="form.arithmetic" placeholder="请选择脱敏算法" style="width: 100%" :disabled="readonly">
          <el-option v-for="item in arithmeticOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="state">
        <el-switch v-model="form.state" :active-value="true" :inactive-value="false" inline-prompt />
      </el-form-item>
    </el-form>

    <template v-if="!readonly" #footer>
      <el-button @click="onCancel"> 取消 </el-button>
      <el-button type="primary" @click="onSave"> 保存 </el-button>
    </template>
  </el-drawer>
</template>

<script setup>
  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
      required: true,
    },
    data: {
      type: Object,
    },
    readonly: {
      type: Boolean,
      default: false,
    },
  });

  const arithmeticOptions = ref([
    {
      value: '1',
      label: '算法1',
    },
    {
      value: '2',
      label: '算法2',
    },
  ]);
  const form = reactive({
    id: '',
    name: '',
    type: '',
    state: true,
  });
  const rules = reactive({
    name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
    arithmetic: [{ required: true, message: '请选择脱敏算法', trigger: 'blur' }],
    state: [{ required: true, message: '请选择状态', trigger: 'blur' }],
  });

  const action = computed(() => {
    return props.data?.id ? (props.readonly ? '查看' : '编辑') : '添加';
  });
  const drawer = ref(false);
  watchEffect(() => {
    drawer.value = props.modelValue;
  });
  watch(
    () => props.data,
    (value) => {
      for (const key in form) {
        form[key] = value[key];
      }
      if (action.value === '添加') {
        form.type = '1';
        form.shiftNum = 0;
      }
    }
  );

  const emit = defineEmits(['update:modelValue', 'success']);
  const formRef = ref();
  const onCancel = () => {
    emit('update:modelValue', false);
    formRef.value.resetFields();
  };
  const onSave = () => {
    formRef.value.validate((valid) => {
      if (valid) {
        onCancel();
        emit('success');
      }
    });
  };
</script>
