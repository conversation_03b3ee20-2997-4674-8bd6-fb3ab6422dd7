import type { RouterItem } from 'types/router';

//系统管理员路由
export const background: RouterItem = {
  path: '/background',
  name: 'Background',
  redirect: '/background/user-manage',
  component: () => import('@/views/background/index.vue'),
  children: [
    {
      path: 'user-manage',
      meta: { title: '账号管理', icon: 'icon-yonghuguanli' },
      component: () => import('@/views/background/user-manage/index.vue'),
    },
    {
      path: 'role-manage',
      meta: { title: '角色管理', icon: 'icon-jiaoseguanli' },
      component: () => import('@/views/background/role-manage/index.vue'),
    },
    {
      path: 'dictionary',
      meta: { title: '字典管理', icon: 'icon-zidianguanli' },
      component: () => import('@/views/background/dictionary/index.vue'),
    },
    {
      path: 'org-manage',
      meta: { title: '机构管理', icon: 'icon-jigouguanli' },
      component: () => import('@/views/background/org-manage/index.vue'),
    },
    {
      path: 'team-manage',
      meta: { title: '团队管理', icon: 'icon-tuanduiguanli-tuanduiguanli' },
      component: () => import('@/views/background/team-manage/index.vue'),
    },
  ],
};
