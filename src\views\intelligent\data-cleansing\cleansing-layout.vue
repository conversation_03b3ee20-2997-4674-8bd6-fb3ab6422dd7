<template>
  <div class="flex h-full">
    <div class="aside bg-w">
      <asidemenu />
    </div>

    <div class="w-0 flex-1">
      <router-view />
    </div>
  </div>
</template>

<script setup>
  import asidemenu from '@/components/aside.vue';
  import { useRoute } from 'vue-router';
  let route = useRoute();

  let activeId = ref('0');
  let menuList = ref([
    {
      title: '数据标准管理',
      id: '1',
      pathname: 'CleansingStandard',
      svgname: 'icon-zhuye',
    },
    {
      title: '清洗任务配置',
      id: '2',
      pathname: 'CleansingTask',
      svgname: 'icon-shezhi',
    },
  ]);

  watch(
    () => route.name,
    (val, old) => {
      menuList.value.forEach((e) => {
        if (e.pathname == val) {
          activeId.value = e.id;
        }
      });
    },
    {
      immediate: true,
    }
  );
  provide('datalist', menuList);
  provide('openid', activeId);
</script>

<style scoped lang="scss">
  .aside {
    width: 256px;
    height: 100%;
    box-shadow: 2px 0 12px 0 rgb(0 0 0 / 10%);
    z-index: 10;
  }
</style>
