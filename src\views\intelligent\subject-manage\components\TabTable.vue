<template>
  <div class="flex h-full p-5">
    <div class="mr-[10px] flex w-[280px] flex-col rounded bg-w pt-4">
      <h3 class="pl-5">数据库列表</h3>
      <el-scrollbar class="h-0 flex-1">
        <CustomMenu v-model="menuId" :data="catalogData" />
      </el-scrollbar>
    </div>

    <div class="flex h-full w-0 flex-1 flex-col rounded-md bg-w pt-5">
      <div class="flex justify-between px-10">
        <div>
          <el-button type="primary" @click="onAdd"> 新增 </el-button>
          <el-button @click="onUpdate"> 立即更新 </el-button>
          <el-button @click="onStop"> 中止 </el-button>
        </div>
      </div>

      <div class="mt-3 h-0 w-full flex-1 px-10">
        <el-table
          height="100%"
          :data="tableData"
          style="width: 100%"
          class="c-table-header"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="name" label="表名称" />
          <el-table-column prop="desc" label="表说明" min-width="100px" />
          <el-table-column prop="frequency" label="更新频率" />
          <el-table-column label="建表语句">
            <template #default="{ row }">
              <el-button link type="primary">
                {{ row.statement }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80">
            <template #default="{ row }">
              <el-popconfirm title="确定删除此项？" @confirm="onDel(row)">
                <template #reference>
                  <el-button link type="primary"> 删除 </el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="pagination-bottom">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :page-size="pagination.pageSize"
          :total="tableData.length"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>

  <TabTableDrawer v-model="showDrawer" @success="onSuccess" />
</template>

<script setup>
  import TabTableDrawer from './TabTableDrawer.vue';
  import CustomMenu from '@/components/custom-menu/CustomMenu.vue';
  const menuId = ref('1');
  const catalogData = ref([
    {
      id: '1',
      title: 'XXXXXX医院库1',
    },
    {
      id: '2',
      title: 'XXXXXX医院库2',
    },
    {
      id: '3',
      title: 'XXXXXX医院库3',
    },
  ]);

  const showDrawer = ref(false);
  const onAdd = () => {
    showDrawer.value = true;
  };
  const onUpdate = () => {};
  const onStop = () => {};
  const onSuccess = () => {};

  //表格
  const tableData = ref([
    {
      id: 1,
      name: 'XXXXXX医院库1',
      desc: '说明说明说明说明说明',
      frequency: '每周',
      statement: 'XX医院库建表语句.doc',
    },
  ]);
  const checkList = ref([]);
  const handleSelectionChange = (val) => {
    checkList.value = val;
  };
  const onDel = (row) => {};

  //页码
  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  const handleCurrentChange = (e) => {
    pagination.page = e;
  };
  const handleSizeChange = (e) => {
    pagination.pageSize = e;
  };
</script>
