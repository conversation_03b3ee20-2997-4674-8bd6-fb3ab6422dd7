<template>
  <div class="flex h-full flex-col">
    <h2 class="flex h-[60px] cursor-pointer items-center bg-w pl-5 text-xl font-bold" @click="onBack">
      <el-icon class="mr-2" color="#939899">
        <ArrowLeft />
      </el-icon>
      审批中心
    </h2>

    <div class="m-5 flex h-0 flex-1">
      <ApproveAside :id="id" @select="onAsideSelect" />

      <div class="ml-5 flex-1 rounded bg-w pt-5">
        <div class="pl-10">
          <h3 class="mb-6 text-xl font-bold">申请信息</h3>

          <div>
            <h4 class="item-title">个人信息</h4>
            <div class="mt-3 w-full text-xs 2xl:text-sm">
              <el-row>
                <el-col :span="12">
                  <div class="flex flex-col justify-start">
                    <span class="mb-2 text-[#939899]">姓名</span>
                    <span>{{ message.userName }}</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="flex flex-col justify-start">
                    <span class="mb-2 text-[#939899]">性别</span>
                    <span>{{ message.gender ? '女' : '男' }}</span>
                  </div>
                </el-col>
              </el-row>
              <el-row class="mt-3">
                <el-col :span="12">
                  <div class="flex flex-col justify-start">
                    <span class="mb-2 text-[#939899]">证件类型</span>
                    <span>{{ message.documentType }}</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="flex flex-col justify-start">
                    <span class="mb-2 text-[#939899]">证件号码</span>
                    <span>{{ message.idnumber }}</span>
                  </div>
                </el-col>
              </el-row>
              <el-row class="mt-3">
                <el-col :span="12">
                  <div class="flex flex-col justify-start">
                    <span class="mb-2 text-[#939899]">学历</span>
                    <span>{{ message.degree }}</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="flex flex-col justify-start">
                    <span class="mb-2 text-[#939899]">职称</span>
                    <span>{{ message.jobTitle }}</span>
                  </div>
                </el-col>
              </el-row>
              <el-row class="mt-3">
                <el-col :span="12">
                  <div class="flex flex-col justify-start">
                    <span class="mb-2 text-[#939899]">手机号</span>
                    <span>{{ message.phone }}</span>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>

          <div class="mt-6">
            <h4 class="item-title">机构信息</h4>
            <div class="mt-3 w-full text-xs 2xl:text-sm">
              <el-row>
                <el-col :span="12">
                  <div class="flex flex-col justify-start">
                    <span class="mb-2 text-[#939899]">机构名称</span>
                    <span>{{ message.ormes.orname }}</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="flex flex-col justify-start">
                    <span class="mb-2 text-[#939899]">机构邮箱</span>
                    <span>{{ message.ormes.oremail }}</span>
                  </div>
                </el-col>
              </el-row>
              <el-row class="mt-3">
                <el-col :span="12">
                  <div class="flex flex-col justify-start">
                    <span class="mb-2 text-[#939899]">部门</span>
                    <span>{{ message.department }}</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="flex flex-col justify-start">
                    <span class="mb-2 text-[#939899]">职位</span>
                    <span>{{ message.posts }}</span>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>
        </div>

        <div class="mb-5 mt-10 border border-border" />

        <div v-if="message.status == 0" class="pl-10">
          <div class="mb-1 text-sm font-bold">审批意见:</div>
          <el-input
            v-model="opinion"
            :rows="4"
            type="textarea"
            placeholder="请输入审批意见"
            show-word-limit
            maxlength="200"
          />
          <div class="mt-5">
            <el-button plain :icon="Check" color="#007f99" @click="onAgree"> 同意 </el-button>
            <el-button plain :icon="Close" color="#e74c4c" @click="onReject"> 拒绝 </el-button>
          </div>
        </div>

        <div v-else class="pl-10">
          <div class="text-sm font-bold text-tip">审批意见:</div>
          <div class="mt-2 text-sm">
            {{ message.opinion }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import ApproveAside from './components/ApproveAside.vue';
  import { Check, Close } from '@element-plus/icons-vue';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { useRouter } from 'vue-router';
  const router = useRouter();
  const props = defineProps({ id: { type: [String, Number] } });

  let message = ref({
    ormes: {
      orname: '华中科技大学同济医学院',
      oremail: '<EMAIL>',
      department: 'xxxx部/技术部/开发组',
      position: '技术经理',
    },
  });
  const onAsideSelect = (e) => {
    Object.assign(message.value, e);
  };
  const opinion = ref('');
  //同意
  const onAgree = () => {
    ElMessageBox.confirm('确定同意该申请？', '操作提示', { type: 'warning' })
      .then(() => {
        ElMessage({ type: 'success', message: '操作成功' });
      })
      .catch(() => {});
  };
  //拒绝
  const onReject = () => {
    ElMessageBox.confirm('确定拒绝该申请？', '操作提示', { type: 'warning' })
      .then(() => {
        ElMessage({ type: 'success', message: '操作成功' });
      })
      .catch(() => {});
  };
  // if (isEmpty(applylist)) {
  //   joinorapply('1')
  //     .then((res) => {
  //       console.log(res);
  //       if (res.data.code == 0) {
  //         applylist.value = res.data.data;

  //         applylist.value.forEach(async (ele, index) => {
  //           await copyAndDeleteProperty(ele.customer, 'id', 'userid');
  //           applylist.value[index] = reactive({
  //             title: '加入机构申请',
  //             ...ele,
  //             ...ele.customer,
  //           });
  //         });
  //       }
  //     })
  //     .catch((err) => {
  //       console.log(err);
  //     });
  // }
  const onBack = () => {
    router.back();
  };
</script>

<style lang="scss" scoped>
  .item-title {
    font-weight: 700;
    font-size: 16px;

    &::before {
      content: '';
      display: inline-block;
      width: 3px;
      height: 12px;
      background: $color-primary;
      margin-right: 9px;
    }
  }
</style>
