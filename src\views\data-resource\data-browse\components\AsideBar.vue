<template>
  <div class="aside text-sm">
    <el-scrollbar height="100%" class="scrollbar">
      <div class="aside-header flex items-center">
        <el-icon class="mr-2 cursor-pointer" color="#939899" size="20px" @click="router.back()">
          <ArrowLeft />
        </el-icon>
        <div class="header-title cursor-pointer" @click="onBackHome">数据浏览</div>
      </div>

      <el-tree
        ref="treeRef"
        class="c-tree"
        node-key="id"
        :expand-on-click-node="false"
        check-on-click-node
        check-strictly
        accordion
        :props="treeProps"
        lazy
        :load="loadNode"
        @node-click="clickNode"
      >
        <template #default="{ node, data }">
          <div
            class="node-item flex h-full w-full items-center justify-between"
            :class="{ 'node-actived': currentId === data.id }"
          >
            <div class="flex flex-wrap break-words">
              <div>{{ node.label }}</div>
              <div v-if="isSelectMode && data.num">({{ data.num || 0 }})</div>
            </div>
            <div
              v-if="data.childrenCount"
              class="node-icon relative flex items-center px-2"
              @click="handleNodeExpand(node)"
            >
              <el-icon v-if="node.expanded" :color="currentId === data.id ? '#007f99' : '#B4B6B8'">
                <CaretBottom />
              </el-icon>
              <el-icon v-else color="#B4B6B8">
                <CaretRight />
              </el-icon>
            </div>
          </div>
        </template>
      </el-tree>
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
  /* 数据浏览侧边栏 */
  import type Node from 'element-plus/es/components/tree/src/model/node';
  import { getTopBasicCatalogues, getChildCatalogue } from '@/api/index';
  import { useDataBrowse } from '@/store/data-browse';
  import { nextTick } from 'vue';
  import { useRouter } from 'vue-router';
  import { ElTree } from 'element-plus';

  const router = useRouter();
  const store = useDataBrowse();
  interface Props {
    orderId: string | undefined;
  }
  const props = defineProps<Props>();
  const id = computed(() => {
    return store.dataBrowse.id;
  });
  const currentId = ref();

  const isSelectMode = computed(() => {
    return store.dataBrowse.isSelectMode;
  });

  const treeProps = {
    label: 'title',
    isLeaf: (data, node) => {
      return data.childrenCount <= 0;
    },
  };

  // el-tree 懒加载load 手动触发load更新
  const handleNodeExpand = (node) => {
    nextTick(() => {
      if (!node.loaded) {
        let _node = treeRef.value!.getNode(node);
        //  设置未进行懒加载状态
        _node.loaded = false;
        // 重新展开节点就会间接重新触发load达到刷新效果
        _node.expand();
      } else {
        node.expanded = !node.expanded;
      }
    });
  };
  const loadNode = async (node: Node, resolve) => {
    if (node.level === 0) {
      const data = await fetchTop();
      resolve(data);
      //选中第一个
      if (data?.length && props.orderId) {
        nextTick(() => {
          treeRef.value!.setCurrentNode(data[0] as any);
          currentId.value = data[0].id!;
          store.setData({ id: currentId.value });
          //展开第一个
          // const _node = treeRef.value!.getNode(data[0].id!);
          // _node.loaded = false;
          // _node.expand();
        });
      }
    } else {
      const data = await fetchChild(node.data.id);
      resolve(data);
      //选中第一个子级
      // if (data?.length && props.orderId) {
      //   nextTick(() => {
      //     treeRef.value!.setCurrentNode(data[0] as any);
      //     currentId.value = data[0].id!;
      //     store.setData({ id: currentId.value });
      //   });
      // }
    }
  };
  const treeRef = ref<InstanceType<typeof ElTree>>();

  const clickNode = (data, node) => {
    currentId.value = data.id;
    store.setData({ id: currentId.value });
  };

  const setNodeNum = (num: number) => {
    const currentNode = treeRef.value!.getNode(currentId.value);
    currentNode.data.num = num;
  };

  const onBackHome = () => {
    currentId.value = '';
    store.setData({ id: currentId.value });
  };

  async function fetchTop() {
    try {
      const { data } = await getTopBasicCatalogues();
      return data;
    } catch (error) {
      console.log(error);
    }
  }
  async function fetchChild(id) {
    try {
      const { data } = await getChildCatalogue(id);
      return data;
    } catch (error) {
      console.log(error);
    }
  }

  watch(id, () => {
    currentId.value = id.value;
  });

  watch(
    store.dataBrowse.fileds,
    () => {
      if (store.dataBrowse.isSelectMode && treeRef.value) {
        setNodeNum(store.getFieldLength(currentId.value));
      }
    },
    { deep: true }
  );
</script>

<style lang="scss" scoped>
  .aside {
    height: 100%;
    width: 320px;
  }

  .scrollbar {
    padding-left: 20px;
    padding-right: 20px;
  }

  .el-scrollbar__wrap {
    overflow-x: auto;
    height: calc(100% + 20px);
  }

  .aside-header {
    padding-top: 20px;
    border-bottom: 1px solid #e0e0e0;
    padding-bottom: 15px;
    margin-bottom: 12px;

    .header-title {
      font-size: 18px;
      line-height: 26px;
      font-weight: 700;
    }

    .title--active {
      color: $color-primary;
    }

    .header-list {
      margin-top: 14px;
      display: flex;

      .li {
        margin-right: 20px;
        color: $color-tip-text;
      }

      .li--active {
        color: $color-primary;
      }
    }
  }

  .c-tree {
    display: inline-block; /* 让el-tree元素横向显示 */
    white-space: nowrap; /* 防止元素换行 */
    --el-tree-node-content-height: 44px;
    background: transparent;

    .node-item {
      color: $color-main-text;
    }

    .node-actived {
      background-image: url('@/assets/img/workbench/menu-selected.png');
      background-repeat: no-repeat;
      background-size: cover;
      border-right: 2px solid $color-primary;
      color: $color-primary;
    }

    :deep(.el-tree-node__content:hover) {
      background-color: var(--el-menu-hover-bg-color);
    }

    // 自定义图标中隐藏自带箭头
    :deep(.el-tree-node__content) {
      position: relative;
    }

    :deep(.el-tree-node__content > .el-tree-node__expand-icon) {
      position: absolute;
      opacity: 0;
    }
  }
</style>
