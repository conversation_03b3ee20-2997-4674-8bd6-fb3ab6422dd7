<template>
  <el-dialog
    v-model="modelValue"
    title="导入到平台数据库"
    width="800px"
    class="fixed-height-dialog"
    destroy-on-close
    top="10vh"
    style="height: 80vh; overflow: hidden; display: flex; flex-direction: column"
  >
    <div class="flex h-full flex-col items-center">
      <el-steps style="width: 600px" class="mb-3" :active="step" align-center>
        <el-step title="选择表" :icon="Grid" />
        <el-step title="填写数据集信息" :icon="Edit" />
      </el-steps>
      <div class="h-0 w-[600px] flex-1">
        <div v-show="step === 1" class="flex h-full w-full flex-col">
          <div class="mb-3">
            请选择要导入到系统中的表，共{{ tableData.length }}条数据，已选{{ checkList.length }}条数据
          </div>
          <el-table
            ref="multipleTableRef"
            v-loading="loading"
            :data="tableData"
            style="width: 100%"
            height="100%"
            class="c-table-header h-0 flex-1"
            destroy-on-close
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="name" label="表名称" />
          </el-table>
        </div>

        <div v-show="step === 2" class="mt-4 h-full">
          <el-scrollbar height="100%">
            <div class="mx-auto h-full max-w-[600px]">
              <DatasetForm ref="datasetFormRef" show-database />
            </div>
          </el-scrollbar>
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="onCancel">取消</el-button>
        <el-button v-if="step !== 1" type="primary" @click="onPrev">上一步</el-button>
        <el-button :loading="saveLoading" type="primary" @click="onSave">{{
          step === maxStep ? '确定' : '下一步'
        }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ElMessage } from 'element-plus';
  import { Grid, Edit, HomeFilled } from '@element-plus/icons-vue';
  import DatasetForm from '../../components/DatasetForm.vue';
  import { newOrUpdateEntity_14 } from '@/api';

  const modelValue = defineModel<boolean>({ required: true });
  interface Props {
    id: number;
  }
  const props = defineProps<Props>();

  const maxStep = 2;
  const step = ref(1);
  const loading = ref(false);
  const tableData = ref<any[]>([]);
  const datasetFormRef = ref<any>(null);
  const saveLoading = ref(false);

  async function fetchData() {
    try {
      loading.value = true;
      // const { data } = await findTeamUserRoleVOByTeamId(Number(props.id), { teamId: props.id });
      // tableData.value = data || [];
      tableData.value = [
        { name: '表1' },
        { name: '表2' },
        { name: '表3' },
        { name: '表4' },
        { name: '表5' },
        { name: '表5' },
        { name: '表5' },
        { name: '表5' },
        { name: '表5' },
        { name: '表5' },
        { name: '表5' },
        { name: '表5' },
        { name: '表5' },
        { name: '表5' },
        { name: '表5' },
        { name: '表5' },
        { name: '表5' },
        { name: '表5' },
        { name: '表5' },
        { name: '表5' },
        { name: '表5' },
        { name: '表5' },
        { name: '表5' },
        { name: '表5' },
        { name: '表5' },
        { name: '表5' },
        { name: '表5' },
        { name: '表5' },
        { name: '表5' },
        { name: '表5' },
        { name: '表5' },
        { name: '表5' },
      ];
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }
  const checkList = ref([]);
  const handleSelectionChange = (val) => {
    checkList.value = val;
  };

  const emit = defineEmits<{ success: [] }>();
  const onCancel = () => {
    modelValue.value = false;
  };
  const onPrev = () => {
    step.value -= 1;
  };

  //填写数据集信息
  const onAddConfirm = async () => {
    datasetFormRef.value.formRef.validate(async (valid) => {
      try {
        if (valid) {
          saveLoading.value = true;
          console.log('🚀 ', datasetFormRef.value.addForm);
          // await newOrUpdateEntity_14(datasetFormRef.value.addForm);
          // step.value += 1;
          onCancel();
          emit('success');
        }
      } catch (error) {
        console.log(error);
      } finally {
        saveLoading.value = false;
      }
    });
  };

  const onSave = () => {
    if (step.value === 1) {
      // if (!checkList.value.length) {
      //   return ElMessage.error('请选择要导入的表');
      // }
      step.value += 1;
    } else if (step.value === 2) {
      if (datasetFormRef.value && datasetFormRef.value.formRef) {
        onAddConfirm();
      }
    }
  };

  watch(modelValue, (value) => {
    if (value) {
      fetchData();
    } else {
      step.value = 1;
    }
  });
</script>
