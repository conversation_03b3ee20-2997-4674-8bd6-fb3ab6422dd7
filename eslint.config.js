import { defineConfig } from 'eslint/config';
import globals from 'globals';
import js from '@eslint/js';
import tseslint from 'typescript-eslint';
import pluginVue from 'eslint-plugin-vue';
import { resolve } from 'path';
import { readFileSync } from 'fs';

const autoImportConfigPath = resolve(process.cwd(), '.eslintrc-auto-import.json');
let autoImportGlobals = {};
try {
  const autoImportConfig = JSON.parse(readFileSync(autoImportConfigPath, 'utf-8'));
  autoImportGlobals = autoImportConfig.globals || {};
} catch (error) {
  console.warn(`Failed to load .eslintrc-auto-import.json: ${error.message}`);
}

export default defineConfig([
  { files: ['**/*.{js,mjs,cjs,ts,vue}'] },
  {
    files: ['**/*.{js,mjs,cjs,ts,vue}'],
    languageOptions: { globals: { ...globals.browser, ...globals.node } },
  },
  { files: ['**/*.{js,mjs,cjs,ts,vue}'], plugins: { js }, extends: ['js/recommended'] },
  tseslint.configs.recommended,
  pluginVue.configs['flat/essential'],
  { files: ['**/*.vue'], languageOptions: { parserOptions: { parser: tseslint.parser } } },
  { languageOptions: { globals: { ...autoImportGlobals } } },
  {
    rules: {
      'vue/multi-word-component-names': 'off',
      '@typescript-eslint/no-unused-vars': 'off',
      '@typescript-eslint/no-explicit-any': 'off',
      'no-undef': 'off',
      'vue/no-unused-vars': 'off',
    },
  },
]);
