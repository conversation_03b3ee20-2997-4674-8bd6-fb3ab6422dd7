<template>
  <div class="flex h-full flex-col">
    <div class="h-0 flex-1">
      <el-scrollbar height="100%">
        <ul v-if="tableData.length" class="overflow-hidden px-5 pt-5">
          <li v-for="(item, index) in tableData" :key="index" class="mb-5 rounded bg-w px-7 py-4">
            <div class="flex items-center justify-between">
              <span>团队{{ index + 1 }}</span>
            </div>

            <el-form label-position="right" label-width="100" :model="item" class="mt-5">
              <div class="flex items-start justify-center">
                <div class="flex w-[200px] items-center pt-1">
                  <div class="h-[13px] w-[3px] bg-p" />
                  <span class="ml-2">选择机构</span>
                </div>
                <el-form-item label="研究机构:">
                  <el-input v-model="item.org" disabled style="width: 480px" />
                </el-form-item>
              </div>

              <div class="mt-5 flex items-start justify-center">
                <div class="flex w-[200px] items-center pt-1">
                  <div class="h-[13px] w-[3px] bg-p" />
                  <span class="ml-2">人员配置</span>
                </div>
                <el-form-item label="首席研究员:">
                  <el-input v-model="item.leadResearcher" disabled style="width: 480px" />
                </el-form-item>
              </div>

              <div class="flex justify-center">
                <div class="w-[200px]" />
                <el-form-item label="合作者:" label-width="150">
                  <ul class="flex flex-col">
                    <div
                      v-for="(cooperatorItem, cooperatorIndex) in item.cooperator"
                      :key="cooperatorIndex"
                      class="relative"
                    >
                      <el-input v-model="cooperatorItem.name" disabled style="width: 480px" class="mb-4" />
                      <el-checkbox
                        v-if="item.deputy === cooperatorIndex"
                        :checked="true"
                        label="代表"
                        disabled
                        class="absolute right-[-16px] top-[-5px]"
                      />
                    </div>
                  </ul>
                </el-form-item>
              </div>

              <div class="flex items-start justify-center">
                <div class="flex w-[200px] items-center pt-1">
                  <div class="h-[13px] w-[3px] bg-p" />
                  <span class="ml-2">材料转让协议信息</span>
                </div>
                <el-form-item label="机构官方名称:">
                  <el-input v-model="item.orgName" disabled style="width: 480px" />
                </el-form-item>
              </div>

              <div class="flex justify-center">
                <div class="w-[200px]" />
                <el-form-item label="机构联系人:">
                  <el-input v-model="item.orgContact" disabled style="width: 480px" />
                </el-form-item>
              </div>
            </el-form>
          </li>
        </ul>
        <el-empty v-else description="暂无团队信息" />
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup>
  const tableData = ref([
    {
      org: 'XXXXXXX大学',
      leadResearcher: '<EMAIL>',
      cooperator: [{ name: '<EMAIL>' }, { name: '<EMAIL>' }, { name: '<EMAIL>' }],
      deputy: 0,
      orgName: 'XXXXXXXXXX机构',
      orgContact: '<EMAIL>',
    },
  ]);
</script>
