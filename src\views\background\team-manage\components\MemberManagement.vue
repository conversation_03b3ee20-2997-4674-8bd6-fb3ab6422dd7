<template>
  <el-drawer v-model="showMembers" title="成员管理" size="50%" @close="onClose">
    <div class="flex h-full flex-col">
      <div class="flex">
        <el-button type="primary" @click="onAddMember">添加成员</el-button>
        <el-button type="danger" :disabled="selectedMembers.length === 0" @click="onBatchRemove">批量删除</el-button>
      </div>

      <div class="mt-3 flex-1">
        <el-table
          :data="memberTableData"
          style="width: 100%"
          class="c-table-header"
          @selection-change="handleMemberSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column type="index" width="60" label="序号" />
          <el-table-column prop="userName" label="用户名" />
          <el-table-column prop="name" label="姓名" />
          <!-- <el-table-column prop="roleName" label="角色" />
          <el-table-column prop="updateTime" label="更新时间">
            <template #default="{ row }">
              <div>{{ row.rltTime?.updateTime || '' }}</div>
            </template>
          </el-table-column> -->
          <el-table-column fixed="right" label="操作" width="100px">
            <template #default="{ row }">
              <el-popconfirm title="确定移除？" @confirm="onRemoveMember(row)">
                <template #reference>
                  <el-tooltip content="移除" effect="dark">
                    <el-button link type="primary" icon="delete" />
                  </el-tooltip>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 添加成员对话框 -->
    <el-dialog v-model="showAddMember" title="添加成员" width="600px" @close="onAddMemberClose">
      <div class="flex gap-4">
        <el-input
          v-model="nameFilter"
          placeholder="请输入用户名或姓名查找"
          style="width: 200px"
          clearable
          @clear="onUserSearch"
          @keyup.enter="onUserSearch"
        />
        <el-button type="default" @click="onUserSearch">查询</el-button>
      </div>

      <div class="mt-3">
        <el-table
          :data="userList"
          style="width: 100%"
          class="c-table-header"
          row-key="id"
          @selection-change="handleUserSelectionChange"
        >
          <el-table-column type="selection" width="55" reserve-selection />
          <el-table-column prop="userName" label="用户名" />
          <el-table-column prop="name" label="姓名" />
          <!-- <el-table-column prop="email" label="邮箱" /> -->
        </el-table>
      </div>

      <div class="mt-3">
        <el-pagination
          background
          layout="total, prev, pager, next, jumper"
          :page-size="userPagination.pageSize"
          :total="userTotal"
          @current-change="handleUserPageChange"
        />
      </div>

      <template #footer>
        <span>
          <el-button @click="onAddMemberClose">取消</el-button>
          <el-button type="primary" :loading="addMemberLoading" @click="onAddMemberConfirm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </el-drawer>
</template>

<script setup lang="ts">
  import {
    findTeamUserRoleVOByTeamId,
    deleteTeamUserById,
    newOrUpdateTeamUser,
    findUserByIdNotIn_02,
  } from '@/api/index';
  import { ElMessage, ElMessageBox } from 'element-plus';

  const props = defineProps<{
    currentTeam: TeamVO | null;
  }>();

  // 显示成员抽屉
  const showMembers = ref(false);
  const memberTableData = ref<TeamUserRoleVO[]>([]);
  const loading = ref(false);
  const selectedMembers = ref<TeamUserRoleVO[]>([]);

  // 添加成员相关
  const showAddMember = ref(false);
  const addMemberLoading = ref(false);
  const userLoading = ref(false);
  const nameFilter = ref('');
  const userList = ref<any[]>([]);
  const userTotal = ref(0);
  const selectedUserIds = ref<number[]>([]);

  // 用户分页
  const userPagination = reactive({
    page: 1,
    pageSize: 10,
  });

  /**
   * 打开成员管理抽屉
   */
  const open = async (team: TeamVO) => {
    showMembers.value = true;
    await fetchTeamMembers(team.id);
  };

  /**
   * 获取团队成员列表
   */
  async function fetchTeamMembers(teamId?: number) {
    try {
      loading.value = true;
      if (props.currentTeam?.id || teamId) {
        const { data } = await findTeamUserRoleVOByTeamId(props.currentTeam?.id || teamId!);
        memberTableData.value = data || [];
      }
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  /**
   * 关闭成员管理抽屉
   */
  const onClose = () => {
    showMembers.value = false;
  };

  /**
   * 打开添加成员对话框
   */
  const onAddMember = () => {
    userPagination.page = 1;
    showAddMember.value = true;
    fetchUserList();
  };

  /**
   * 关闭添加成员对话框
   */
  const onAddMemberClose = () => {
    showAddMember.value = false;
    nameFilter.value = '';
    selectedUserIds.value = [];
  };

  /**
   * 获取用户列表
   */
  async function fetchUserList() {
    try {
      userLoading.value = true;
      if (props.currentTeam?.id) {
        const { data } = await findUserByIdNotIn_02({
          teamId: props.currentTeam.id,
          pageNum: userPagination.page,
          pageSize: userPagination.pageSize,
          searchInput: nameFilter.value,
        });
        userTotal.value = data?.totalElement || 0;
        userList.value = data?.content || [];
      }
    } catch (error) {
      console.log(error);
    } finally {
      userLoading.value = false;
    }
  }

  /**
   * 用户查询
   */
  const onUserSearch = () => {
    userPagination.page = 1;
    fetchUserList();
  };

  /**
   * 用户分页变化
   */
  const handleUserPageChange = (page: number) => {
    userPagination.page = page;
    fetchUserList();
  };

  /**
   * 用户表格选择变化
   */
  const handleUserSelectionChange = (val: any[]) => {
    selectedUserIds.value = val.map((item) => item.id);
  };

  /**
   * 提交添加成员表单
   */
  const onAddMemberConfirm = async () => {
    try {
      if (selectedUserIds.value.length === 0) {
        ElMessage({ type: 'warning', message: '请至少选择一个用户' });
        return;
      }

      if (!props.currentTeam?.id) {
        ElMessage({ type: 'warning', message: '未找到当前团队' });
        return;
      }

      addMemberLoading.value = true;

      // 创建批量添加用户的请求
      const promises = selectedUserIds.value.map((userId) => {
        const teamUserDTO: TeamUserDTO = {
          teamUserId: {
            teamId: props.currentTeam!.id,
            userId: userId,
          },
        };
        return newOrUpdateTeamUser(teamUserDTO);
      });

      // 等待所有请求完成
      await Promise.all(promises);

      ElMessage({ type: 'success', message: `成功添加${selectedUserIds.value.length}名成员` });
      onAddMemberClose();
      await fetchTeamMembers(props.currentTeam.id);
    } catch (error) {
      console.log(error);
    } finally {
      addMemberLoading.value = false;
    }
  };

  /**
   * 团队成员选择变化
   */
  const handleMemberSelectionChange = (val: TeamUserRoleVO[]) => {
    selectedMembers.value = val;
  };

  /**
   * 批量移除团队成员
   */
  const onBatchRemove = () => {
    if (selectedMembers.value.length === 0) {
      ElMessage({ type: 'warning', message: '请至少选择一个成员' });
      return;
    }

    ElMessageBox.confirm(`确定要移除选中的 ${selectedMembers.value.length} 位成员吗？`, '批量移除成员', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(async () => {
        try {
          loading.value = true;
          const teamUserIds = selectedMembers.value
            .filter((member) => member.teamUserId?.teamId && member.teamUserId?.userId)
            .map((member) => ({
              teamId: member.teamUserId!.teamId,
              userId: member.teamUserId!.userId,
            }));

          if (teamUserIds.length === 0) {
            ElMessage({ type: 'warning', message: '没有有效的成员可移除' });
            return;
          }

          await deleteTeamUserById(teamUserIds);
          ElMessage({ type: 'success', message: `成功移除 ${teamUserIds.length} 名成员` });
          await fetchTeamMembers(props.currentTeam?.id);
          selectedMembers.value = [];
        } catch (error) {
          console.log(error);
        } finally {
          loading.value = false;
        }
      })
      .catch(() => {
        // 用户取消操作，不做任何处理
      });
  };

  /**
   * 移除团队成员
   */
  const onRemoveMember = async (row: TeamUserRoleVO) => {
    try {
      loading.value = true;
      if (row.teamUserId?.teamId && row.teamUserId?.userId) {
        await deleteTeamUserById([
          {
            teamId: row.teamUserId.teamId,
            userId: row.teamUserId.userId,
          },
        ]);
        ElMessage({ type: 'success', message: '移除成功' });
        await fetchTeamMembers(props.currentTeam?.id);
      }
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  };

  defineExpose({
    open,
  });
</script>
