<template>
  <div class="flex h-full flex-col">
    <h2 class="flex h-[60px] items-center bg-w pl-[28px] text-xl font-bold">数据源</h2>

    <div class="m-5 flex h-0 flex-1 flex-col rounded-md bg-w pt-5">
      <div class="flex justify-between px-10">
        <div>
          <el-button type="primary" @click="onAdd"> 新增 </el-button>
          <!-- <el-button :disabled="checkList.length <= 0" @click="onBatchDel">批量删除</el-button> -->
        </div>

        <!-- <div class="flex">
          <el-input v-model="search" placeholder="请输入数据库名称 ">
            <template #append>
              <el-button :icon="Search" @click="onSearch" />
            </template>
          </el-input>
          <el-button class="ml-3" :icon="Refresh" @click="onRefresh"></el-button>
          <el-button class="ml-3" :icon="Operation" @click="changeSort"></el-button>
        </div> -->
      </div>

      <div class="mt-3 h-0 w-full flex-1 px-10">
        <el-table
          v-loading="tableLoading"
          height="100%"
          :data="tableData"
          style="width: 100%"
          class="c-table-header"
          @selection-change="handleSelectionChange"
        >
          <!-- <el-table-column type="selection" width="55" /> -->
          <el-table-column type="index" width="55" label="序号" />
          <el-table-column prop="databaseName" label="数据库名称" min-width="100px" />
          <el-table-column label="同步状态">
            <template #default="{ row }">
              <span class="status" :class="synchStateClass[+row.synchronized]">
                {{ synchStateText[+row.synchronized] }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="address" label="主机" />
          <!-- <el-table-column label="状态">
            <template #default="{ row }">
              <span class="status" :class="metadata.statusClass[row.state]">{{ metadata.statusText[row.state] }}</span>
            </template>
          </el-table-column> -->
          <el-table-column prop="startDate" label="创建时间" />
          <el-table-column label="操作" width="150">
            <template #default="{ row }">
              <el-button link type="primary" @click="onView(row)"> 查看 </el-button>
              <el-button link type="primary" @click="onEdit(row)"> 编辑 </el-button>
              <el-popconfirm title="确定删除此项？" @confirm="onDel(row)">
                <template #reference>
                  <el-button link type="primary"> 删除 </el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- <div class="pagination-bottom">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :page-size="pagination.pageSize"
          :total="tableData.length"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div> -->
    </div>
  </div>

  <DataSourceDrawer v-model="showDrawer" :data="drawerData" />
</template>

<script setup>
  import { synchStateClass, synchStateText } from '@/utils/constants';
  import { findAllDb, deleteDatabaseById } from '@/api/index';
  import DataSourceDrawer from './components/DataSourceDrawer.vue';
  import { Search, Operation, Refresh } from '@element-plus/icons-vue';
  import { ElMessageBox, ElMessage } from 'element-plus';
  import { useRouter } from 'vue-router';
  const router = useRouter();

  const onAdd = () => {
    router.push({ name: 'ThematicDataSourceAdd' });
  };
  const onBatchDel = () => {
    ElMessageBox.confirm('确定删除所有选中项？', '操作提示', { type: 'warning' })
      .then(() => {
        ElMessage({ type: 'success', message: '删除成功' });
      })
      .catch(() => {});
  };

  const search = ref('');
  const onSearch = () => {};
  const onRefresh = () => {};
  const changeSort = () => {};

  //表格
  const tableData = ref([]);
  const checkList = ref([]);
  const handleSelectionChange = (val) => {
    checkList.value = val;
  };

  let drawerData = reactive({});
  const showDrawer = ref(false);
  const onView = (row) => {
    drawerData = row;
    showDrawer.value = true;
  };
  const onEdit = (row) => {
    router.push({ name: 'ThematicDataSourceAdd', params: { id: row.id } });
  };
  const onDel = async (row) => {
    try {
      await deleteDatabaseById(row.id, true);
      ElMessage({ type: 'success', message: '删除成功' });
      fetchData();
    } catch (error) {
      console.log(error);
    }
  };

  //页码
  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  const handleCurrentChange = (e) => {
    pagination.page = e;
  };
  const handleSizeChange = (e) => {
    pagination.pageSize = e;
  };

  const tableLoading = ref(false);
  fetchData();
  async function fetchData() {
    try {
      tableLoading.value = true;
      const { data } = await findAllDb();
      tableData.value = data;
    } catch (error) {
      console.log(error);
    } finally {
      tableLoading.value = false;
    }
  }
</script>
