<template>
  <el-upload
    ref="uploadRef"
    v-model:file-list="fileList"
    drag
    :multiple="multiple"
    :show-file-list="showFileList"
    :auto-upload="false"
    :accept="accept"
    :limit="limit"
    :on-exceed="handleExceed"
    :on-change="onChange"
  >
    <el-icon class="el-icon--upload"><upload-filled /></el-icon>
    <div class="el-upload__text">将文件拖到此处或 <em>点击上传</em></div>
    <template #tip>
      <div class="el-upload__tip">{{ tip }}</div>
    </template>
  </el-upload>
</template>

<script setup lang="ts">
  import { ElMessage, UploadFile, UploadFiles } from 'element-plus';
  import { upload } from '@/utils/request';

  const fileList = defineModel<UploadFile[]>({ default: [] });
  const props = defineProps({
    accept: {
      type: String,
      default: '.pdf', //".pdf, .doc, .docx, .xls, .xlsx"
    },
    limit: {
      type: Number,
      default: 10,
    },
    maxSize: {
      type: Number,
      default: 200,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    showFileList: {
      type: Boolean,
      default: true,
    },
    autoUpload: Boolean, //自动上传
    tip: {
      type: String,
      default: '',
    },
  });
  const emit = defineEmits<{ success: [] }>();

  const loading = ref(false);
  const uploadRef = ref();
  const handleExceed = () => {
    ElMessage.warning(`文件数量不能超过${props.limit}个`);
  };
  const onChange = (file: UploadFile, files: UploadFiles) => {
    if (file.size! / 1024 / 1024 > props.maxSize) {
      uploadRef.value.clearFiles();
      ElMessage.warning(`文件大小不能超过${props.maxSize}MB`);
      return;
    }
    save(file, files);
  };

  async function save(file: UploadFile, files: UploadFiles) {
    if (props.autoUpload) {
      try {
        loading.value = true;
        const uploadTasks = files.map(async (item) => {
          // TODO
          await upload('', { method: 'post', data: { documentFile: item.raw } });
        });
        await Promise.all(uploadTasks);
        uploadRef.value.clearFiles();
        ElMessage({ type: 'success', message: '上传成功' });
        emit('success');
      } catch (error) {
        console.log(error);
      } finally {
        uploadRef.value.clearFiles();
        loading.value = false;
      }
    }
  }
</script>
