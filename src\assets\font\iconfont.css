@font-face {
  font-family: "iconfont"; /* Project id 4392466 */
  src: url('iconfont.woff2?t=1703841608486') format('woff2'),
       url('iconfont.woff?t=1703841608486') format('woff'),
       url('iconfont.ttf?t=1703841608486') format('truetype'),
       url('iconfont.svg?t=1703841608486#iconfont') format('svg');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-clear:before {
  content: "\e601";
}

.icon-service:before {
  content: "\e600";
}

