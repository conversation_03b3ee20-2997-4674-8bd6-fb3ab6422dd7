<template>
  <div class="flex h-full w-full flex-col overflow-hidden">
    <h2 class="flex h-[60px] items-center bg-w pl-5 text-xl font-semibold">目录发布</h2>

    <div class="m-5 flex h-0 flex-1 flex-col justify-start rounded bg-w px-10 py-5">
      <div class="mb-4 flex w-full justify-end">
        <el-input v-model="search" placeholder="请输入关键字" style="width: 500px">
          <template #prepend>
            <el-select v-model="searchPre" style="width: 130px">
              <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </template>
          <template #append>
            <el-button :icon="Search" @click="onSearch" />
          </template>
        </el-input>
      </div>

      <div class="w-full">
        <el-table :data="tableData" class="c-table-header">
          <el-table-column prop="id" label="信息资源代码" />
          <el-table-column prop="name" label="信息资源名称" />
          <el-table-column prop="abstract" label="信息资源摘要" />
          <el-table-column label="信息资源状态">
            <template #default="{ row }">
              <span class="status" :class="statusClass[row.state]">{{ statusText[row.state] }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200px">
            <template #default="{ row }">
              <el-button v-if="row.state === 1" link type="primary" @click="onPublish(row)"> 发布 </el-button>
              <div v-else>
                <el-popconfirm title="确定撤销发布？" @confirm="onUnpublish(row)">
                  <template #reference>
                    <el-button link type="primary"> 撤销发布 </el-button>
                  </template>
                </el-popconfirm>
                <el-button link type="primary" @click="onCatalogMaintain(row)"> 目录维护 </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>

  <RegisterDrawer v-model="showDrawer" :data="drawerData" :readonly="false" @success="onSuccess" />
</template>

<script setup>
  import RegisterDrawer from '../components/RegisterDrawer.vue';
  import { Search } from '@element-plus/icons-vue';
  import { useRouter } from 'vue-router';
  let router = useRouter();

  const searchPre = ref('信息资源代码');
  const search = ref('');
  const options = [
    {
      value: '信息资源代码',
      label: '信息资源代码',
    },
    {
      value: '信息资源名称',
      label: '信息资源名称',
    },
    {
      value: '信息资源摘要',
      label: '信息资源摘要',
    },
  ];
  const onSearch = () => {};

  const tableData = [
    {
      id: '9065321351S313321',
      name: '脑疾病研究',
      abstract: '信息资源摘要',
      state: 1,
    },
    {
      id: '7835321351S313321',
      name: '腿部疾病研究',
      abstract: '信息资源摘要',
      state: 2,
    },
    {
      id: '90853217813833321',
      name: '神经疾病研究',
      abstract: '信息资源摘要',
      state: 1,
    },
    {
      id: '8545321351S313321',
      name: '骨疾病研究',
      abstract: '信息资源摘要',
      state: 2,
    },
  ];
  const statusText = {
    1: '未发布',
    2: '已发布',
  };
  const statusClass = {
    1: 'status-gray',
    2: 'status-green',
  };
  const onPublish = (row) => {
    router.push({ name: 'IntelligentPublish', params: { id: row.id } });
  };
  const onUnpublish = (row) => {};

  const showDrawer = ref(false);
  const drawerData = ref({});
  const onCatalogMaintain = (row) => {
    drawerData.value = row;
    drawerData.value.treeId = 1;
    showDrawer.value = true;
  };
</script>
