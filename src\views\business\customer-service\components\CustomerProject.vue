<template>
  <div class="bg-w mt-4 flex h-0 flex-1 rounded">
    <ProjectAside :id="id" ref="asideRef" @select="onAsideSelect" />

    <div class="flex h-full w-0 flex-1 flex-col pt-5" :key="applicationInfo.id">
      <el-scrollbar height="100%" class="h-0 flex-1">
        <div class="px-10 pb-10">
          <h3 class="mb-5 text-xl font-bold">申请信息</h3>
          <div>
            <div class="mb-4 flex items-center">
              <div class="bg-p h-[13px] w-[3px]" />
              <span class="ml-2">项目信息</span>
            </div>
            <div v-if="applicationInfo.id" class="text-sm">
              <el-row class="mt-4">
                <el-col :span="24">
                  <div class="flex flex-col justify-start">
                    <div class="mb-2 text-[#939899]">项目名称</div>
                    <span>{{ applicationInfo.title }}</span>
                  </div>
                </el-col>
              </el-row>
              <el-row class="mt-4">
                <el-col :span="24">
                  <div class="flex flex-col justify-start">
                    <div class="mb-2 text-[#939899]">项目问题和目标</div>
                    <span>{{ applicationInfo.questionObjective }}</span>
                  </div>
                </el-col>
              </el-row>
              <el-row class="mt-4">
                <el-col :span="24">
                  <div class="flex flex-col justify-start">
                    <div class="mb-2 text-[#939899]">项目背景与科学原理</div>
                    <span>{{ applicationInfo.backgroundPrinciple }}</span>
                  </div>
                </el-col>
              </el-row>
              <el-row class="mt-4">
                <el-col :span="24">
                  <div class="flex flex-col justify-start">
                    <div class="mb-2 text-[#939899]">使用方法说明</div>
                    <span>{{ applicationInfo.methodDescription }}</span>
                  </div>
                </el-col>
              </el-row>
              <el-row class="mt-4">
                <el-col :span="24">
                  <div class="flex flex-col justify-start">
                    <div class="mb-2 text-[#939899]">所需数据集的类型和大小</div>
                    <span>{{ applicationInfo.datasetTypeVolume }}</span>
                  </div>
                </el-col>
              </el-row>
              <el-row class="mt-4">
                <el-col :span="24">
                  <div class="flex flex-col justify-start">
                    <div class="mb-2 text-[#939899]">研究的预期价值</div>
                    <span>{{ applicationInfo.researchValue }}</span>
                  </div>
                </el-col>
              </el-row>
              <el-row class="mt-4">
                <el-col :span="24">
                  <div class="flex flex-col justify-start">
                    <div class="mb-2 text-[#939899]">项目关键词</div>
                    <span>
                      {{
                        [
                          applicationInfo.keyWord1,
                          applicationInfo.keyWord2,
                          applicationInfo.keyWord3,
                          applicationInfo.keyWord4,
                        ]
                          .filter(Boolean)
                          .join(',')
                      }}
                    </span>
                  </div>
                </el-col>
              </el-row>
              <el-row class="mt-4">
                <el-col :span="24">
                  <div class="flex flex-col justify-start">
                    <div class="mb-2 text-[#939899]">简述项目摘要，说明目标、科学原理、项目持续时间和公共卫生影响</div>
                    <span>{{ applicationInfo.summary }}</span>
                  </div>
                </el-col>
              </el-row>
              <el-row class="mt-4">
                <el-col :span="24">
                  <div class="flex flex-col justify-start">
                    <div class="mb-2 text-[#939899]">项目是否会产生从现有复杂数据集衍生的任何新数据字段</div>
                    <span>{{ applicationInfo.haveDerivant ? '是' : '否' }}</span>
                  </div>
                </el-col>
              </el-row>
              <!-- <el-row v-if="applicationInfo.haveDerivant" class="mt-4">
                <el-col :span="24">
                  <div class="flex flex-col justify-start">
                    <div class="mb-2 text-[#939899]">打算生成的变量概述</div>
                    <span>{{ applicationInfo.newDataIntro }}</span>
                  </div>
                </el-col>
              </el-row> -->
              <el-row class="mt-4">
                <el-col :span="24">
                  <div class="flex flex-col justify-start">
                    <div class="mb-2 text-[#939899]">项目预计持续时间</div>
                    <span>{{ applicationInfo.duration }}个月</span>
                  </div>
                </el-col>
              </el-row>
            </div>
            <div v-else>暂无数据</div>
          </div>

          <div class="mt-5">
            <div class="mb-4 flex items-center">
              <div class="bg-p h-[13px] w-[3px]" />
              <span class="ml-2">团队信息</span>
            </div>
            <div v-if="teamData.length" class="text-sm">
              <el-table :data="teamData" style="width: 100%" class="c-table-header">
                <el-table-column type="index" width="55" />
                <el-table-column prop="teamName" label="团队名称" min-width="100px" />
                <el-table-column label="操作" width="150">
                  <template #default="{ row }">
                    <el-button link type="primary" @click="onTeam(row)">团队成员</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div v-else>暂无数据</div>
          </div>

          <div class="mt-5">
            <div class="mb-4 flex items-center">
              <div class="bg-p h-[13px] w-[3px]" />
              <span class="ml-2">数据字段</span>
            </div>
            <DataField v-if="orderTable.length" :order="orderTable" :readonly="true" />
            <div v-else>暂无数据</div>
          </div>
        </div>
      </el-scrollbar>

      <div v-if="applicationInfo.id" class="border-border border-t px-10 py-5">
        <template v-if="applicationInfo.state === '待审批'">
          <div class="mb-1 text-sm font-bold">审批意见:</div>
          <el-input
            v-model="opinion"
            :rows="4"
            type="textarea"
            placeholder="请输入审批意见"
            show-word-limit
            maxlength="200"
          />
          <div class="mt-5">
            <el-button plain :icon="Check" color="#007f99" :loading="agreeeLoading" @click="onAgree(0)">
              同意
            </el-button>
            <el-button plain :icon="Close" color="#e74c4c" :loading="refuseLoading" @click="onAgree(3)">
              拒绝
            </el-button>
          </div>
        </template>
        <template v-else>
          <div class="text-tip text-sm font-bold">审批意见:</div>
          <div class="mt-2 text-sm">
            {{ opinion || '无' }}
          </div>
        </template>
      </div>
    </div>
  </div>

  <TeamCreatedMember :id="teamId" v-model="showMember" readonly />
</template>

<script setup lang="ts">
  import { Check, Close } from '@element-plus/icons-vue';
  import ProjectAside from './CustomerProjectAside.vue';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import {
    findApplicationTeamByAppId,
    findOrderByApplicationId,
    findVerifyResult,
    findVerifyResult_1,
    verifyApplication,
  } from '@/api';
  import { useUsers } from '@/store/user-info';
  import TeamCreatedMember from '@/views/logged-layout/user-center/my-team/TeamCreatedMember.vue';
  import DataField from '@/views/workbench/personal/project/project-application/components/DataField.vue';

  const store = useUsers();

  const id = ref('');
  let applicationInfo = reactive({
    backgroundPrinciple: '',
    datasetTypeVolume: '',
    duration: 0,
    haveDerivant: false,
    id: 0,
    keyWord1: '',
    keyWord2: '',
    keyWord3: '',
    keyWord4: '',
    methodDescription: '',
    questionObjective: '',
    researchValue: '',
    state: '',
    summary: '',
    title: '',
  });

  //团队信息
  const teamData = ref<ApplicationTeamVO[]>([]);
  const showMember = ref(false);
  const teamId = ref(0);

  async function fetchTeam() {
    try {
      // loading.value = true;
      const { data } = await findApplicationTeamByAppId(applicationInfo.id);
      teamData.value = data || [];
    } catch (error) {
      console.log(error);
    } finally {
      // loading.value = false;
    }
  }

  const onTeam = (item: ApplicationTeamVO) => {
    showMember.value = true;
    teamId.value = item.applicationTeamId?.teamId || 0;
  };

  //数据字段
  const orderTable = ref<OrderVO[]>([]);
  async function fetchFieldOrder() {
    try {
      // fieldLoading.value = true;
      const { data } = await findOrderByApplicationId(applicationInfo.id, 1, 100);
      orderTable.value = data?.content || [];
    } catch (error) {
      console.log('🚀 ~ fetchFieldOrder ~ error:', error);
    } finally {
      // fieldLoading.value = false;
    }
  }

  //审批意见
  const opinion = ref('');
  const opinionLoading = ref(false);
  const fetchOpinion = async () => {
    try {
      opinionLoading.value = true;
      opinion.value = '';
      const { data } = await findVerifyResult_1(applicationInfo.id);
      opinion.value = data?.description ?? '';
    } catch (error) {
      console.log(error);
    } finally {
      opinionLoading.value = false;
    }
  };

  //同意、拒绝
  const asideRef = ref();
  const agreeeLoading = ref(false);
  const refuseLoading = ref(false);
  const onAgree = (conclusion: number) => {
    if (opinion.value === '') {
      ElMessage({ type: 'warning', message: '请输入审批意见' });
      return;
    }

    ElMessageBox.confirm(`确定${conclusion === 0 ? '同意' : '拒绝'}该申请？`, '操作提示', { type: 'warning' })
      .then(async () => {
        try {
          if (conclusion === 0) {
            agreeeLoading.value = true;
          } else {
            refuseLoading.value = true;
          }
          await verifyApplication({
            userId: store.user.id,
            applicationId: applicationInfo.id,
            resolution: conclusion === 0 ? '审核通过' : '审核未通过',
            resolutionType: '审核',
            description: opinion.value,
          });
          ElMessage({ type: 'success', message: '操作成功' });
          asideRef.value.fetchData();
        } catch (error) {
          console.log(error);
        } finally {
          if (conclusion === 0) {
            agreeeLoading.value = false;
          } else {
            refuseLoading.value = false;
          }
        }
      })
      .catch(() => {});
  };

  const onAsideSelect = (e) => {
    Object.assign(applicationInfo, e);
    opinion.value = '';
    fetchTeam();
    fetchFieldOrder();
    if (e.state !== '待审批') {
      fetchOpinion();
    }
  };
</script>
