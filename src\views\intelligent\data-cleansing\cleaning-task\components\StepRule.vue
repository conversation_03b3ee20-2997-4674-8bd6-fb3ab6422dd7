<template>
  <div class="flex h-full flex-col">
    <el-scrollbar class="h-0 flex-1 px-5">
      <div class="h-5" />
      <div class="flex justify-center rounded bg-w pb-[22px] pt-10">
        <el-form ref="formRef" label-width="200" label-position="right" :model="form" :rules="rules" class="w-[700px]">
          <el-form-item label="集成数据目标:" prop="target">
            <SelectGroup v-model="form.target" />
          </el-form-item>
          <el-form-item label="目标表自动匹配规则:" prop="rule">
            <el-input v-model="form.rule" placeholder="请输入目标表自动匹配规则" style="width: 480px" maxlength="100">
              <template #prepend>
                {{ form.target.split(',')[0] }}
              </template>
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" plain @click="onExec"> 执行 </el-button>
          </el-form-item>
        </el-form>
      </div>

      <div class="mt-3 rounded bg-w px-10 py-7">
        <div class="flex justify-end text-sm">
          <el-checkbox v-model="isAutoCreate" label="未匹配到目标端表时自动建表" />
          <el-button class="ml-7" type="primary" link :icon="QuestionFilled" @click="onViewAutoRule">
            自动建表规则
          </el-button>
        </div>
        <el-table :data="tableData" style="width: 100%" class="c-table-header mt-6">
          <el-table-column prop="sourceTable" label="源端表名" align="center" />
          <el-table-column prop="targetTable" label="目标端表名" align="center" />
          <el-table-column label="操作" width="120">
            <template #default="{ row }">
              <el-popconfirm title="确定删除此项？" @confirm="onDel(row)">
                <template #reference>
                  <el-button link type="primary"> 删除 </el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="h-5" />
    </el-scrollbar>

    <div class="box-shadow flex h-[68px] items-center justify-center bg-w">
      <el-button type="primary" @click="onSave"> 下一步 </el-button>
      <el-button @click="lastStep"> 上一步 </el-button>
    </div>
  </div>
</template>

<script setup>
  import { QuestionFilled } from '@element-plus/icons-vue';
  import SelectGroup from '@/components/SelectGroup.vue';
  import { useRouter } from 'vue-router';
  const router = useRouter();

  const form = reactive({
    target: 'oracle,zw_test_esaeynous',
    rule: '',
  });
  const rules = reactive({
    target: [{ required: true, message: '请选择集成数据目标', trigger: 'blur' }],
    rule: [{ required: true, message: '请输入目标表自动匹配规则', trigger: 'blur' }],
  });

  const formRef = ref();
  const onExec = () => {
    formRef.value.validate((valid) => {
      if (valid) {
        console.log(1);
      }
    });
  };

  const isAutoCreate = ref(true);
  const onViewAutoRule = () => {
    // TODO
  };
  const tableData = ref([
    {
      sourceTable: 'ahhhhhh',
      targetTable: 'bhhhhhhh',
    },
    {
      sourceTable: 'ahhhhhh',
      targetTable: 'bhhhhhhh',
    },
    {
      sourceTable: 'ahhhhhh',
      targetTable: 'bhhhhhhh',
    },
    {
      sourceTable: 'ahhhhhh',
      targetTable: 'bhhhhhhh',
    },
    {
      sourceTable: 'ahhhhhh',
      targetTable: 'bhhhhhhh',
    },
    {
      sourceTable: 'ahhhhhh',
      targetTable: 'bhhhhhhh',
    },
  ]);
  const onDel = () => {};

  const emit = defineEmits(['success']);
  const onSave = () => {
    emit('success', 1);
  };
  const lastStep = () => {
    emit('success', -1);
  };
</script>

<style lang="scss" scoped>
  :deep(.el-input-group__prepend) {
    width: 165px;
  }
</style>
