<template>
  <el-tabs v-model="activeName" class="tabs">
    <el-tab-pane label="数据" name="1">
      <div class="p-4 text-m">
        <p class="text-sm">317,677 项数据可用，涵盖 317,677 名参与者，使用 Data-Coding 1007 编码。</p>
        <div class="mb-4 h-[260px]">
          <Chart :option="option" />
        </div>
        <p class="text-sm">参与者/项目的计数最后更新于2022年2月10日。</p>
      </div>
    </el-tab-pane>
    <el-tab-pane label="注释" name="2"> 注释 </el-tab-pane>
    <el-tab-pane label="关联类别" name="4"> 关联类别 </el-tab-pane>
    <el-tab-pane label="关联字段" name="5"> 关联字段 </el-tab-pane>
    <el-tab-pane label="资源" name="6"> 资源 </el-tab-pane>
  </el-tabs>
</template>

<script setup>
  import Chart from '@/components/Chart.vue';
  const activeName = ref('1');
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        data: ['可用账户', '无法使用账户'],
        boundaryGap: true,
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: '#565B5C',
        },
        axisLine: {
          lineStyle: {
            color: '#C8C9CC',
          },
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        axisLabel: {
          color: '#939899',
        },
      },
    ],
    series: [
      {
        type: 'bar',
        barWidth: '50px',
        label: {
          show: true,
          position: 'top',
        },
        data: [274.356, 24.356],
        color: '#1e9bb6',
      },
    ],
  };
</script>

<style lang="scss" scoped>
  .tabs {
    --el-border-color-light: #e1e3e6;
  }
</style>
