<template>
  <Topbar />

  <div class="main">
    <div class="header">
      <div class="center-content">
        <h2 class="title">
          {{ title }}
        </h2>
        <div class="description">
          <p>{{ description1 }}</p>
          <p class="header-p">
            {{ description2 }}
          </p>
        </div>
      </div>
    </div>

    <div class="center-content card-ctontainer">
      <div v-for="platform in platforms" :key="platform.id" class="card" @click="clikplatform(platform)">
        <img class="card-img" :src="platform.img" alt="" />
        <h3 class="card-title">
          {{ platform.text }}
        </h3>
      </div>
    </div>
  </div>

  <div class="placeholder" />
  <div class="footer">
    <div class="image">
      <img class="img1" :src="img4" alt="" />
      <img :src="img5" alt="" />
    </div>
    <div class="copyright">
      {{ detail1 }}
    </div>
    <div class="detail">
      <span>{{ details.d1 }}</span>
      <span>{{ details.d2 }}</span>
      <span>{{ details.d3 }}</span>
      <span>{{ details.d4 }}</span>
      <span>{{ details.d5 }}</span>
      <span>{{ details.d6 }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
  import Topbar from '@/components/Topbar.vue';
  import { ref, reactive } from 'vue';
  import img1 from '@/assets/img/data-source2x.png';
  import img2 from '@/assets/img/work2x.png';
  import img3 from '@/assets/img/zhineng2x.png';
  import img4 from '@/assets/img/beijing-univ.png';
  import img5 from '@/assets/img/english-char.png';
  import { useUsers } from '@/store/user-info';
  const store = useUsers();
  import { useRouter } from 'vue-router';
  import { ElMessage } from 'element-plus';
  let router = useRouter();
  import { fetchUser } from '@/utils/common-api';

  const title = ref('国家脑疾病临床大数据平台');
  const description1 = ref(
    '国家脑疾病临床大数据平台是我国首个面向脑疾病临床数据的大规模平台，提供了数据存储、获取、展示、分析等一系列功能，方便科研人员开展脑疾病研究，促进脑疾病研究领域发展。'
  );
  const description2 = ref(
    '国家脑疾病临床大数据平台下设数据资源库、工作台、智能分析平台，分别为科研人员提供脑疾病数据多维展示、脑疾病研究全流程管理、智能分析资源支持等功能。'
  );
  const detail1 = ref('Copyright © Institute of Software, CAS. All rights reserved.info(at)iscas.ac.cn');
  const details = reactive({
    d1: '版权所有©中国科学院软件研究所',
    d2: '京ICP备05046678号-1',
    d3: '文保网安备1101080077',
    d4: '电话： 86-10-62661012',
    d5: '传真： 86-10-62562533',
    d6: '电子邮箱 ： <EMAIL>',
  });

  const platforms = reactive([
    {
      id: 1,
      pathname: 'DataResource',
      img: img1,
      text: '数据资源库',
    },
    {
      id: 2,
      pathname: 'workbench',
      img: img2,
      text: '工作台',
    },
    {
      id: 3,
      pathname: 'IntelligentHome',
      img: img3,
      text: '智能分析平台',
    },
  ]);
  let clikplatform = async (e) => {
    if (e.pathname === 'workbench') {
      if (!store.user.username) {
        router.push({ name: 'Login' });
        return;
      }
      await fetchUser();
      if (store.loggedHome === 'Index') {
        ElMessage({ type: 'warning', message: '功能开发中' });
        return;
      }
      router.push({ name: store.loggedHome });
    } else if (e.pathname === 'IntelligentHome') {
      // //智能分析平台跳转
      // let name = 'Login';
      // switch (store.user.role) {
      //   case 3:
      //     name = 'IntelligentHome';
      //     break;
      // }
      // router.push({ name });
      ElMessage({ type: 'warning', message: '功能开发中' });
    } else {
      router.push({ name: e.pathname });
    }
  };
</script>

<style lang="scss" scoped>
  .main {
    font-size: 14px;
  }

  .homeHeader {
    width: 100%;
    height: 8vh;
    padding: 0px;
    box-shadow: 0px 2px 2px #5c5959;
    max-height: 55px;
    position: relative;
    z-index: 1;
  }

  .header {
    height: 300px;
    background-image: url('@/assets/img/background1.png');
    min-width: $main-width;
    background-repeat: no-repeat;
    background-size: 100% auto;
    background-position-y: center;
    background-color: #007790;

    .center-content {
      padding-top: 50px;
      color: #fff;
    }

    .title {
      font-size: 36px;
      font-weight: bold;
    }

    .description {
      margin-top: 30px;
      color: rgba(255, 255, 255, 0.9);
      width: 640px;
    }

    .header-p {
      margin-top: 16px;
    }
  }

  .card-ctontainer {
    box-sizing: border-box;
    margin-top: 60px;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    column-gap: 40px;

    .card {
      box-sizing: border-box;
      box-shadow: 1px 1px 10px rgba(0, 0, 0, 0.1);
      border-radius: 4px;
      border: 1px solid rgba(230, 230, 230, 1);
      cursor: pointer;

      .card-img {
        width: 100%;
        height: 240px;
        margin: 0;
      }

      .card-title {
        font-size: 20px;
        text-align: center;
        padding: 20px 0;
      }
    }
  }

  $footer-height: 150px;
  .placeholder {
    margin-top: 16px;
    height: $footer-height;
  }

  .footer {
    font-size: 14px;
    min-width: $main-width;
    height: $footer-height;
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    background-color: #f0f2f5;
    margin-top: 30px;
    box-sizing: border-box;
    padding-top: 20px;

    .text {
      text-align: center;
      color: #929597;
    }

    .image {
      display: flex;
      justify-content: center;
      height: 32px;
    }

    .img1 {
      display: inline-block;
      margin-right: 50px;
    }

    .copyright {
      margin-top: 20px;
      color: #666;
      text-align: center;
    }

    .detail {
      margin-top: 10px;
      color: #666;
      text-align: center;

      span {
        margin-left: 20px;
      }
    }
  }
</style>
