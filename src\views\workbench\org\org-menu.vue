<template>
  <div class="flex h-full">
    <div class="aside bg-w">
      <asidemenu />
    </div>

    <div class="w-0 flex-1">
      <el-scrollbar height="100%">
        <router-view />
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup>
  /* 机构左侧带单栏 */
  import asidemenu from '@/components/aside.vue';
  import { useRoute } from 'vue-router';
  let route = useRoute();

  let activeId = ref('0');
  let menulist = ref([
    { id: '1', title: '主页', pathname: 'Org', svgname: 'icon-zhuye' },
    {
      id: '2',
      title: '机构信息',
      pathname: 'OrgInfo',
      svgname: 'icon-yingyongjigouguanli',
    },
    {
      id: '3',
      title: '人员管理',
      pathname: 'OrgPersonManage',
      svgname: 'icon-renyuanguanli',
    },
    // {
    //   id: '4',
    //   title: '数据管理',
    //   pathname: 'OrgDataManage',
    //   svgname: 'icon-tongjitu',
    // },
  ]);

  watch(
    () => route.name,
    (val, old) => {
      menulist.value.forEach((e) => {
        if (e.pathname == val) {
          activeId.value = e.id;
        }
      });
    },
    {
      immediate: true,
    }
  );
  provide('datalist', menulist);
  provide('openid', activeId);
</script>

<style scoped lang="scss">
  .aside {
    width: 256px;
    height: 100%;
    box-shadow: 2px 0 12px 0 rgb(0 0 0 / 10%);
    z-index: 10;
  }

  .main-header {
    padding-top: 15px;
    padding-left: 28px;
    background: #fff;
  }
</style>
