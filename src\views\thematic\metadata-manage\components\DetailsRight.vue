<template>
  <el-scrollbar class="w-[400px] rounded bg-w">
    <div class="px-5 py-4">
      <h3 class="mb-4">变更记录</h3>
      <el-steps direction="vertical" class="c-steps">
        <el-step v-for="item in datalist" :key="item" status="process">
          <template #icon>
            <el-icon color="#007f99" size="20px">
              <CircleCheckFilled />
            </el-icon>
          </template>
          <template #description>
            <div class="flex w-full flex-col pb-5">
              <div v-if="item.events.length > 0" class="flex flex-col">
                <span v-for="(et, index) in item.events" :key="index" class="leading-7">{{ et }}</span>
              </div>
              <div v-if="item.remark !== ''">
                <span>备注：</span>
                <span> {{ item.remark }}</span>
              </div>
              <div class="mt-3 text-tip">
                <span>{{ item.date }}</span>
              </div>
            </div>
          </template>
        </el-step>
      </el-steps>
    </div>
  </el-scrollbar>
</template>

<script setup>
  let datalist = ref([
    {
      id: '1',
      events: ['字段庙述由 patient_name 修改为患者年龄', '数据默认值由 - 修改为 20'],
      remark:
        '这甲是备注信的这甲是备注信的涞甲是备注信的汉甲是备注 息这里是备注信息这里是备注倍息这里是备注信息这里是备注倍备注信息这甲是备注信息',
      date: '2022-06-01 17:30:50',
    },
    {
      id: '1',
      events: ['字段庙述由 patient_name 修改为患者年龄', '数据默认值由 - 修改为 20'],
      remark:
        '这甲是备注信的这甲是备注信的涞甲是备注信的汉甲是备注 息这里是备注信息这里是备注倍息这里是备注信息这里是备注倍备注信息这甲是备注信息',
      date: '2022-06-01 17:30:50',
    },
    {
      id: '1',
      events: ['字段庙述由 patient_name 修改为患者年龄', '数据默认值由 - 修改为 20'],
      remark: '',
      date: '2022-06-01 17:30:50',
    },
    {
      id: '1',
      events: ['字段庙述由 patient_name 修改为患者年龄', '数据默认值由 - 修改为 20'],
      remark: '',
      date: '2022-06-01 17:30:50',
    },
    {
      id: '1',
      events: ['字段庙述由 patient_name 修改为患者年龄', '数据默认值由 - 修改为 20'],
      remark:
        '这甲是备注信的这甲是备注信的涞甲是备注信的汉甲是备注 息这里是备注信息这里是备注倍息这里是备注信息这里是备注倍备注信息这甲是备注信息',
      date: '2022-06-01 17:30:50',
    },
    {
      id: '1',
      events: ['字段庙述由 patient_name 修改为患者年龄', '数据默认值由 - 修改为 20'],
      remark:
        '这甲是备注信的这甲是备注信的涞甲是备注信的汉甲是备注 息这里是备注信息这里是备注倍息这里是备注信息这里是备注倍备注信息这甲是备注信息',
      date: '2022-06-01 17:30:50',
    },
    {
      id: '1',
      events: ['字段庙述由 patient_name 修改为患者年龄', '数据默认值由 - 修改为 20'],
      remark:
        '这甲是备注信的这甲是备注信的涞甲是备注信的汉甲是备注 息这里是备注信息这里是备注倍息这里是备注信息这里是备注倍备注信息这甲是备注信息',
      date: '2022-06-01 17:30:50',
    },

    {
      id: '1',
      events: ['字段庙述由 patient_name 修改为患者年龄', '数据默认值由 - 修改为 20'],
      remark:
        '这甲是备注信的这甲是备注信的涞甲是备注信的汉甲是备注 息这里是备注信息这里是备注倍息这里是备注信息这里是备注倍备注信息这甲是备注信息',
      date: '2022-06-01 17:30:50',
    },
    {
      id: '1',
      events: ['字段庙述由 patient_name 修改为患者年龄', '数据默认值由 - 修改为 20'],
      remark:
        '这甲是备注信的这甲是备注信的涞甲是备注信的汉甲是备注 息这里是备注信息这里是备注倍息这里是备注信息这里是备注倍备注信息这甲是备注信息',
      date: '2022-06-01 17:30:50',
    },
    {
      id: '1',
      events: ['字段庙述由 patient_name 修改为患者年龄', '数据默认值由 - 修改为 20'],
      remark:
        '这甲是备注信的这甲是备注信的涞甲是备注信的汉甲是备注 息这里是备注信息这里是备注倍息这里是备注信息这里是备注倍备注信息这甲是备注信息',
      date: '2022-06-01 17:30:50',
    },
  ]);
</script>
