@font-face {
  font-family: "iconfont"; /* Project id 4392466 */
  src: url('iconfont.woff2?t=1744708066975') format('woff2'),
       url('iconfont.woff?t=1744708066975') format('woff'),
       url('iconfont.ttf?t=1744708066975') format('truetype'),
       url('iconfont.svg?t=1744708066975#iconfont') format('svg');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-tuanduiguanli-tuanduiguanli:before {
  content: "\e625";
}

.icon-jigouguanli:before {
  content: "\e6af";
}

.icon-caidan08:before {
  content: "\e607";
}

.icon-msg-system:before {
  content: "\e67b";
}

.icon-jiaoseguanli:before {
  content: "\e612";
}

.icon-yonghuguanli:before {
  content: "\e67f";
}

.icon-zidianguanli:before {
  content: "\e624";
}

.icon-clear:before {
  content: "\e601";
}

.icon-service:before {
  content: "\e600";
}

