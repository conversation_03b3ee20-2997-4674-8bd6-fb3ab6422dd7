<template>
  <div class="r mr-5 flex w-[400px] flex-col rounded bg-w">
    <div class="top h-[300px] pl-[40px] pr-10 pt-[40px] text-w">
      <div class="text-[24px] font-bold">{{ username }}，欢迎登录本系统！</div>
      <!-- <p class="mt-4 text-sm opacity-80">上次登录时间：2022-03-12 12:00:00</p>
      <p class="mt-2 text-sm opacity-80">上次登录地点：山东省济南市</p>
      <p class="mt-10 flex cursor-pointer items-center text-[18px]" @click="enterSystem">
        点击进入系统
        <el-icon class="ml-2">
          <Right />
        </el-icon>
      </p> -->
    </div>

    <!-- <div class="mt-5 flex h-0 flex-1 flex-col">
      <div class="px-10 text-lg">相关文件</div>
      <el-scrollbar class="mt-4 h-0 flex-1 px-10">
        <ul class="list-disc pb-3 text-sm">
          <li v-for="(item, index) in relevantDocument" :key="index" class="mb-3 flex items-center justify-between">
            <div class="dot-text">
              {{ item.text }}
            </div>
            <div class="text-tip">
              {{ item.time }}
            </div>
          </li>
        </ul>
      </el-scrollbar>
    </div> -->
  </div>
</template>

<script setup>
  import { useRouter } from 'vue-router';
  const router = useRouter();
  import { useUsers } from '@/store/user-info';
  const username = computed(() => {
    return useUsers().user.username;
  });
  const enterSystem = () => {
    router.push({ name: 'BusinessOrgManage' });
  };

  const relevantDocument = ref([
    {
      text: '新机构审批注意事项',
      time: '2022-03-28',
    },
    {
      text: '新机构审批注意事项',
      time: '2022-03-28',
    },
    {
      text: '新机构审批注意事项',
      time: '2022-03-28',
    },
    {
      text: '新机构审批注意事项',
      time: '2022-03-28',
    },
    {
      text: '新机构审批注意事项',
      time: '2022-03-28',
    },
    {
      text: '新机构审批注意事项',
      time: '2022-03-28',
    },

    {
      text: '新机构审批注意事项',
      time: '2022-03-28',
    },
    {
      text: '新机构审批注意事项',
      time: '2022-03-28',
    },
    {
      text: '新机构审批注意事项',
      time: '2022-03-28',
    },
    {
      text: '新机构审批注意事项',
      time: '2022-03-28',
    },
    {
      text: '新机构审批注意事项',
      time: '2022-03-28',
    },
    {
      text: '新机构审批注意事项',
      time: '2022-03-28',
    },
    {
      text: '新机构审批注意事项',
      time: '2022-03-28',
    },
    {
      text: '新机构审批注意事项',
      time: '2022-03-28',
    },
    {
      text: '新机构审批注意事项',
      time: '2022-03-28',
    },
    {
      text: '新机构审批注意事项',
      time: '2022-03-28',
    },
    {
      text: '新机构审批注意事项',
      time: '2022-03-28',
    },
    {
      text: '新机构审批注意事项',
      time: '2022-03-28',
    },
  ]);
</script>

<style lang="scss" scoped>
  .top {
    background-image: url('@/assets/img/business/aside-bg.png');
    background-size: 100% 100%;
  }

  .dot-text::before {
    content: '';
    display: inline-block;
    width: 6px;
    height: 6px;
    background: $color-primary;
    border-radius: 999px;
    margin-right: 12px;
  }
</style>
