import type { UserConfig, ConfigEnv } from 'vite';
import { loadEnv } from 'vite';
import vue from '@vitejs/plugin-vue';
import { resolve } from 'path';
import autoImport from 'unplugin-auto-import/vite';
import VueDevTools from 'vite-plugin-vue-devtools';
import tailwindcss from '@tailwindcss/vite'

export default (mode: ConfigEnv): UserConfig => {
  const root = process.cwd();
  const env = loadEnv(mode.mode, root);

  return {
    plugins: [
      vue(),
      VueDevTools(),
      autoImport({
        imports: ['vue', 'vue-router'], // 需要引入的类型来源
        vueTemplate: true,
        eslintrc: {
          enabled: false, // 使用 eslint 配置，第一次生成后关闭，避免重复生成
        },
      }),
      tailwindcss(),
    ],
    root: process.cwd(), // 项目根目录
    base: mode.command === 'serve' ? './' : env.VITE_PUBLIC_PATH,
    server: {
      port: env.VITE_PORT as unknown as number,
      open: env.VITE_OPEN === 'true',
      proxy: {
        '/resources': {
          //服务器接口路径地址，根据路径设置
          target: env.VITE_PROXY_PATH, //你的服务器地址
          changeOrigin: true, // 允许跨域
        },
        '/api': {
          //服务器接口路径地址，根据路径设置
          target: env.VITE_PROXY_PATH, //你的服务器地址
          changeOrigin: true, // 允许跨域
          rewrite: (path) => path.replace(/^\/api/, ''),
        },
      },
    },
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'), // 路径别名
      },
    },
    // 全局css变量
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "@/styles/variables.scss" as *;`,
          api: 'modern-compiler',
        },
      },
    },
    define: {
      __INTLIFY_PROD_DEVTOOLS__: JSON.stringify(false),
      __VERSION__: JSON.stringify(process.env.npm_package_version),
      __NEXT_NAME__: JSON.stringify(process.env.npm_package_name),
    },
  };
};
