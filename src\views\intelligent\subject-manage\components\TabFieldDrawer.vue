<template>
  <el-drawer v-model="drawer" class="relative" @close="onCancel">
    <template #header>
      <h4 class="text-m">新增字段</h4>
    </template>

    <el-form ref="formRef" label-position="top" :model="form" :rules="rules">
      <el-form-item label="字段名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入字段名称" maxlength="50" />
      </el-form-item>
      <el-form-item label="字段类型" prop="type">
        <el-select v-model="form.type" placeholder="请选择字段类型" style="width: 100%">
          <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="长度" prop="size">
        <el-input-number v-model="form.size" :min="1" :max="9999" />
      </el-form-item>
      <el-form-item label="注释" prop="annotation">
        <el-input
          v-model="form.annotation"
          placeholder="请输入注释"
          maxlength="300"
          type="textarea"
          :rows="4"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="onCancel"> 取消 </el-button>
      <el-button type="primary" @click="onSave"> 保存 </el-button>
    </template>
  </el-drawer>
</template>

<script setup>
  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
      required: true,
    },
  });
  const drawer = ref(false);
  const form = reactive({
    name: '',
    type: '',
    size: 1,
    annotation: '',
  });
  const rules = reactive({
    name: [{ required: true, message: '请输入字段名称', trigger: 'blur' }],
    type: [{ required: true, message: '请选择字段类型', trigger: 'blur' }],
    size: [{ required: true, message: '请输入长度', trigger: 'blur' }],
    annotation: [{ required: true, message: '请输入注释', trigger: 'blur' }],
  });
  const typeOptions = ref([
    { label: '文字', value: '1' },
    { label: '整数', value: '2' },
    { label: '小数', value: '3' },
    { label: '日期', value: '4' },
  ]);

  watchEffect(() => {
    drawer.value = props.modelValue;
  });
  const emit = defineEmits(['update:modelValue', 'success']);
  const formRef = ref();
  const onCancel = () => {
    emit('update:modelValue', false);
    formRef.value.resetFields();
  };
  const onSave = () => {
    formRef.value.validate((valid) => {
      if (valid) {
        onCancel();
        emit('success');
      }
    });
  };
</script>
