<template>
  <el-scrollbar height="100%">
    <div v-loading="loading" class="page-wrapper mx-auto p-4">
      <div class="bg-w rounded-lg p-4">
        <el-descriptions>
          <template #title>
            <div class="flex cursor-pointer items-center" @click="router.back()">
              <el-icon size="20px"><Back /></el-icon>
              <div class="ml-2">数据集详情</div>
            </div>
          </template>
          <el-descriptions-item label="数据集名称(中文)">{{ detailData.datasetNameCn }}</el-descriptions-item>
          <el-descriptions-item label="数据集名称(英文)">{{ detailData.datasetName }}</el-descriptions-item>
          <el-descriptions-item label="数据集说明">{{ detailData.description }}</el-descriptions-item>
          <el-descriptions-item label="疾病类型">
            {{ detailData.diseaseTypeAnnotation }}
          </el-descriptions-item>
          <!-- <el-descriptions-item label="更新日期">
            {{ detailData?.rltTime?.updateTime }}
          </el-descriptions-item> -->
          <el-descriptions-item label="所属项目">{{ detailData?.projectName || '' }}</el-descriptions-item>
          <el-descriptions-item label="所属项目负责人">{{ detailData?.projectLeader || '' }}</el-descriptions-item>
          <el-descriptions-item label="计划入组人数">{{ detailData?.plannedMembers || '' }}</el-descriptions-item>
        </el-descriptions>
        <el-button type="primary" size="default" @click="onViewData">查看数据字段</el-button>
      </div>

      <div class="bg-w mt-4 rounded-lg p-4">
        <div>
          <h2 class="font-bold">单位简介</h2>
          <p class="text-tip mt-2 text-sm">这里是单位简介</p>
        </div>
        <el-divider />

        <div>
          <h2 class="font-bold">课题组简介</h2>
          <p class="text-tip mt-2 text-sm">{{ detailData.projectName || '这是课题组简介' }}</p>
        </div>
        <el-divider />

        <div>
          <h2 class="mb-2 font-bold">负责人简介</h2>
          <div class="flex">
            <el-image :src="imageUrl" class="h-[200px] w-[150px] rounded-lg"></el-image>
            <div class="text-regular ml-4 w-0 flex-1 text-sm">
              <div class="flex items-center">
                <h3 class="text-lg">{{ detailData.projectLeader || '张三' }}</h3>
                <div class="text-regular ml-2">正高级(<EMAIL>)</div>
              </div>
              <div>xx大学</div>

              <div class="my-2 flex gap-2">
                <el-tag v-for="tag in ['人工智能', '大数据', '信息安全']" :key="tag" :disable-transitions="false">
                  {{ tag }}
                </el-tag>
              </div>

              <p class="text-tip indent-[2em]">
                简介信息简介信息简介信息简介信息简介信息简介信息简介信息简介信息简介信息简介信息简介信息简介信息简介信息简介信息简介信息简介信息简介信息简介信息简介信息简介信息简介信息简介信息简介信息简介信息简介信息简介信息简介信息简介信息简介信息简介信息简介信息简介信息简介信息简介信息简介信息简介信息简介信息简介信息简介信息简介信息简介信息简介信息简介信息简介信息简介信息
              </p>

              <h3 class="bg-p text-w mt-3 py-1 pl-2 text-base">主要荣誉</h3>
              <ul class="mt-1">
                <li>xxxx一等奖（2018）</li>
                <li>xxxx一等奖（2017）</li>
                <li>xxxx一等奖（2015）</li>
                <li>xxxx一等奖（2015）</li>
                <li>xxxx一等奖（2015）</li>
                <li>xxxx一等奖（2015）</li>
                <li>xxxx一等奖（2015）</li>
                <li>xxxx一等奖（2015）</li>
              </ul>

              <h3 class="bg-p text-w mt-3 py-1 pl-2 text-base">科研项目</h3>
              <ul class="mt-1">
                <li>[1] “基于xxxx”.国家自然科学基金重点项目,2018-2021.</li>
                <li>[2] “基于xxxx”.国家自然科学基金重点项目,2018-2021.</li>
                <li>[3] “基于xxxx”.国家自然科学基金重点项目,2018-2021.</li>
                <li>[4] “基于xxxx”.国家自然科学基金重点项目,2018-2021.</li>
              </ul>

              <h3 class="bg-p text-w mt-3 py-1 pl-2 text-base">联系信息</h3>
              <div class="mt-1">
                <div>电子邮箱：<EMAIL></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-scrollbar>
</template>

<script setup lang="ts">
  import { useRouter } from 'vue-router';
  // import { findEntityById_46 } from '@/api/index';
  import { topicConfig } from '@/config/topic-config';

  export type FileInfoVOCustom = FileInfoVO & {
    projectName?: string;
    projectLeader?: string;
    plannedMembers?: number;
  };

  const router = useRouter();
  interface Props {
    id: string;
  }
  const props = defineProps<Props>();

  const loading = ref(false);
  const detailData = ref<FileInfoVOCustom>({} as any);
  const imageUrl = ref('https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg');

  async function fetchData() {
    // try {
    //   loading.value = true;
    //   const { data } = await findEntityById_46(+props.id);
    //   const dataCustom: FileInfoVOCustom = data!;
    //   const fItem = topicConfig.find((t) => t.datasetName === dataCustom?.datasetName);
    //   if (fItem) {
    //     dataCustom.projectName = fItem.projectName;
    //     dataCustom.projectLeader = fItem.projectLeader;
    //     dataCustom.plannedMembers = fItem.plannedMembers;
    //   }
    //   detailData.value = dataCustom;
    // } catch (error) {
    //   console.log(error);
    // } finally {
    //   loading.value = false;
    // }
  }

  const onViewData = async () => {
    router.push({ name: 'DataSetField', params: { id: props.id } });
  };

  watchEffect(() => {
    if (props.id) {
      fetchData();
    }
  });
</script>

<style lang="scss" scoped>
  :deep(.el-descriptions) {
    .el-descriptions__body {
      background-color: transparent;
    }
    .el-descriptions__label {
      color: $color-tip-text;
    }
  }

  :deep(.el-scrollbar__view) {
    min-height: 100%;
    background: #f0f2f5;
  }
</style>
