<template>
  <div class="flex h-full flex-col">
    <h2 class="flex h-[60px] items-center bg-w pl-[28px] text-xl font-bold">机构管理</h2>

    <div class="m-5 flex h-0 flex-1 flex-col rounded-md bg-w pt-5">
      <div class="px-10">
        <el-button type="primary" @click="onApply"> 新机构注册审批 </el-button>
        <el-button type="primary" @click="onAdd"> 添加 </el-button>
      </div>

      <div class="mt-3 h-0 w-full flex-1 px-10">
        <el-table height="100%" :data="tableData" style="width: 100%" class="c-table-header">
          <el-table-column prop="name" label="机构名称" min-width="100px" />
          <el-table-column prop="state" label="统一编码" />
          <el-table-column prop="time" label="注册时间" />
          <el-table-column prop="administrator" label="管理员" />
          <el-table-column prop="operate" label="数据集数目" />
          <el-table-column prop="count" label="成员数目" />
          <el-table-column label="操作" width="110">
            <template #default="{ row }">
              <el-button link type="primary" @click="onEdit(row)"> 编辑 </el-button>
              <el-popconfirm title="确定删除此项？" @confirm="onDel(row)">
                <template #reference>
                  <el-button link type="primary"> 删除 </el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="pagination-bottom">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :page-size="pagination.pageSize"
          :total="tableData.length"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
  import { useRouter } from 'vue-router';
  const router = useRouter();

  //新机构注册审批
  const onApply = () => {
    // router.push({ name: 'PersonalProjectApplication' });
  };
  const onAdd = () => {};

  //表格
  const tableData = ref([
    {
      id: 1,
      name: 'XXXXX机构',
      state: '2S458733245464',
      time: '2022-03-12 12:00:00',
      administrator: '张三',
      operate: 1223,
      count: 566,
    },
    {
      id: 2,
      name: 'XXXXX机构',
      state: '2S458733245464',
      time: '2022-03-12 12:00:00',
      administrator: '张三',
      operate: 1223,
      count: 566,
    },
    {
      id: 3,
      name: 'XXXXX机构',
      state: '2S458733245464',
      time: '2022-03-12 12:00:00',
      administrator: '张三',
      operate: 1223,
      count: 566,
    },
    {
      id: 4,
      name: 'XXXXX机构',
      state: '2S458733245464',
      time: '2022-03-12 12:00:00',
      administrator: '张三',
      operate: 1223,
      count: 566,
    },
  ]);
  const onEdit = (row) => {};
  const onDel = (row) => {};

  //页码
  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  const handleCurrentChange = (e) => {
    pagination.page = e;
  };
  const handleSizeChange = (e) => {
    pagination.pageSize = e;
  };
</script>
