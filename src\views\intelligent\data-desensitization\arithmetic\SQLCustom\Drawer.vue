<template>
  <el-drawer v-model="drawer" class="relative" @close="onCancel">
    <template #header>
      <h4 class="text-m">{{ action }}脚本脱敏</h4>
    </template>

    <div class="mb-4 flex items-center">
      <div class="h-[13px] w-[3px] bg-p" />
      <span class="ml-2">配置</span>
    </div>
    <el-form ref="formRef" label-position="top" :model="form" :rules="rules">
      <el-form-item label="名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入名称" maxlength="32" show-word-limit :disabled="readonly" />
      </el-form-item>
      <el-form-item label="类型" prop="type">
        <el-radio-group v-model="form.type" :disabled="readonly">
          <el-radio label="1"> 专用 </el-radio>
          <el-radio label="2"> 通用 </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="敏感词" prop="sensitive">
        <el-select v-model="form.sensitive" placeholder="请选择敏感词" style="width: 100%" :disabled="readonly">
          <el-option v-for="item in sensitiveOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="算法级别" prop="level">
        <el-select v-model="form.level" placeholder="请选择算法级别" style="width: 100%" :disabled="readonly">
          <el-option v-for="item in levelOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="算法介绍">
        <el-input
          v-model="form.introduce"
          placeholder="请输入算法介绍"
          maxlength="300"
          type="textarea"
          :rows="4"
          show-word-limit
        />
      </el-form-item>

      <div class="mb-4 mt-7 flex items-center">
        <div class="h-[13px] w-[3px] bg-p" />
        <span class="ml-2">脚本信息</span>
      </div>
      <el-form-item label="算法脚本" prop="script">
        <el-input
          v-model="form.script"
          placeholder="请输入算法脚本"
          maxlength="2000"
          type="textarea"
          :rows="4"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <div class="mb-4 mt-7 flex items-center">
      <div class="h-[13px] w-[3px] bg-p" />
      <span class="ml-2">测试</span>
    </div>
    <el-form ref="testRef" label-position="top" :model="testForm" :rules="testRules" hide-required-asterisk>
      <el-form-item label="输入测试文本" prop="originData">
        <div class="flex w-full">
          <el-input v-model="form.originData" class="mr-4 flex-1" placeholder="请输入测试文本" maxlength="300" />
          <el-button type="primary" plain @click="onTest"> 测试 </el-button>
        </div>
      </el-form-item>
      <el-form-item label="脱敏结果">
        <el-input v-model="result" type="textarea" disabled :rows="4" />
      </el-form-item>
    </el-form>

    <template v-if="!readonly" #footer>
      <el-button @click="onCancel"> 取消 </el-button>
      <el-button type="primary" @click="onSave"> 保存 </el-button>
    </template>
  </el-drawer>
</template>

<script setup>
  import TowNumber from '../components/TowNumber.vue';
  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
      required: true,
    },
    data: {
      type: Object,
    },
    readonly: {
      type: Boolean,
      default: false,
    },
  });

  const sensitiveOptions = ref([
    {
      value: '1',
      label: '身份证',
    },
  ]);
  const levelOptions = ref([
    {
      value: '1',
      label: '中级',
    },
  ]);
  const form = reactive({
    name: '',
    type: '',
    sensitive: '',
    level: '',
    introduce: '',
    script: '',
  });
  const rules = reactive({
    name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
    type: [{ required: true, message: '请选择类型', trigger: 'blur' }],
    sensitive: [{ required: true, message: '请选择敏感词', trigger: 'blur' }],
    level: [{ required: false, message: '请选择算法级别', trigger: 'blur' }],
    script: [{ required: true, message: '请输入算法脚本', trigger: 'blur' }],
  });
  const testForm = reactive({
    originData: '',
  });
  const testRules = reactive({
    originData: [{ required: true, message: '请输入测试文本', trigger: 'blur' }],
  });
  const result = ref('');
  const testRef = ref();
  const onTest = () => {
    testRef.value.validate((valid) => {
      if (valid) {
        console.log('开测');
      }
    });
  };

  const action = computed(() => {
    return props.data?.id ? (props.readonly ? '查看' : '编辑') : '添加';
  });
  const drawer = ref(false);
  watchEffect(() => {
    drawer.value = props.modelValue;
  });
  watch(
    () => props.data,
    (value) => {
      for (const key in form) {
        form[key] = value[key];
      }
      if (action.value === '添加') {
        form.type = '1';
      }
    }
  );

  const emit = defineEmits(['update:modelValue', 'success']);
  const formRef = ref();
  const onCancel = () => {
    emit('update:modelValue', false);
    formRef.value.resetFields();
  };
  const onSave = () => {
    formRef.value.validate((valid) => {
      if (valid) {
        onCancel();
        emit('success');
      }
    });
  };
</script>
