<template>
  <div class="h-full p-5">
    <div class="flex h-full justify-center rounded-md bg-w">
      <div class="w-[600px] pt-10">
        <el-form ref="formRef" label-width="120" label-position="right" :model="form" :rules="rules">
          <el-form-item label="调度类型:" prop="type">
            <el-radio-group v-model="form.type">
              <el-radio label="1"> 单次调度 </el-radio>
              <el-radio label="2"> 周期调度 </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="调度周期:" prop="period">
            <el-select v-model="form.period" placeholder="请选择调度周期" style="width: 100%">
              <el-option v-for="item in periodOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="失败是否重跑:" prop="runAgain">
            <el-switch
              v-model="form.runAgain"
              :active-value="true"
              :inactive-value="false"
              active-text="是"
              inactive-text="否"
              inline-prompt
            />
          </el-form-item>
          <el-form-item label="调度时间:" prop="time">
            <el-time-picker v-model="form.time" placeholder="请选择调度时间" style="width: 100%" />
          </el-form-item>
          <el-form-item label="调度有效期起:" prop="startDate">
            <el-date-picker v-model="form.startDate" placeholder="请选择日期" style="width: 100%" />
          </el-form-item>
          <el-form-item label="调度有效期止:" prop="endDate">
            <el-date-picker v-model="form.endDate" placeholder="请选择日期" style="width: 100%" />
          </el-form-item>
          <el-form-item label="同步策略:" prop="strategy">
            <el-radio-group v-model="form.strategy">
              <el-radio label="1"> 写入前清除已有数据 </el-radio>
              <el-radio label="2"> 目标表追加数据 </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="是否限流:" prop="isLimit">
            <el-radio-group v-model="form.isLimit">
              <el-radio label="1"> 是 </el-radio>
              <el-radio label="2"> 否 </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="DMU:" prop="dmu">
            <el-select v-model="form.dmu" placeholder="请选择DMU" style="width: 100%">
              <el-option v-for="item in dmuOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="任务调度节点:" prop="node">
            <el-select v-model="form.node" placeholder="请选择任务调度节点" style="width: 100%">
              <el-option v-for="item in nodeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSave"> 确定 </el-button>
            <el-button @click="lastStep"> 上一步 </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { useRouter } from 'vue-router';
  const router = useRouter();

  const periodOptions = [
    {
      value: '1',
      label: '每日',
    },
    {
      value: '2',
      label: '每周',
    },
  ];
  const dmuOptions = [
    {
      value: '10',
      label: '10',
    },
    {
      value: '20',
      label: '20',
    },
  ];
  const nodeOptions = [
    {
      value: '1',
      label: 'SJCJJD001',
    },
    {
      value: '2',
      label: 'SJCJJD002',
    },
  ];
  const form = reactive({
    type: '1',
    period: '',
    runAgain: false,
    time: '',
    strategy: '1',
    isLimit: '1',
  });
  const rules = reactive({
    type: [{ required: true, message: '请选择调度类型', trigger: 'blur' }],
    period: [{ required: true, message: '请选择调度周期', trigger: 'blur' }],
    runAgain: [{ required: true, message: '请选择失败是否重跑', trigger: 'blur' }],
    time: [{ required: true, message: '请选择调度时间', trigger: 'blur' }],
    startDate: [{ required: true, message: '请选择日期', trigger: 'blur' }],
    endDate: [{ required: true, message: '请选择日期', trigger: 'blur' }],
    strategy: [{ required: true, message: '请选择同步策略', trigger: 'blur' }],
    isLimit: [{ required: true, message: '请选择是否限流', trigger: 'blur' }],
    dmu: [{ required: true, message: '请选择DMU', trigger: 'blur' }],
    node: [{ required: true, message: '请选择任务调度节点', trigger: 'blur' }],
  });

  const formRef = ref();
  const emit = defineEmits(['success']);
  const onSave = () => {
    formRef.value.validate((valid) => {
      if (valid) {
        router.back();
      }
    });
  };
  const lastStep = () => {
    emit('success', -1);
  };
</script>
