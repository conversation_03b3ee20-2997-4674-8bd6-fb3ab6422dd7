<template>
  <div class="flex h-full flex-col rounded-lg bg-w p-6">
    <div class="flex justify-between">
      <h4 class="text-lg font-bold">我的消息</h4>
      <div class="cursor-pointer" @click="onMore">
        <span class="text-xs text-[#939899]">查看更多</span>
        <el-icon :size="10">
          <ArrowRightBold color="#939899" :size="20" />
        </el-icon>
      </div>
    </div>

    <div class="mt-4 h-0 flex-1">
      <el-scrollbar height="100%">
        <div
          v-for="item in datali"
          :key="item.id"
          class="flex items-center border-b-[1px] border-solid border-[#e8eaed] pb-3 pt-2"
        >
          <div v-if="item.isread" class="flex items-center justify-center rounded-[50%] bg-[#f0f2f5] p-3">
            <el-icon :size="15" color="#939899">
              <Bell />
            </el-icon>
          </div>
          <div v-else class="flex items-center justify-center rounded-[50%] bg-[#ebf5f7] p-3">
            <el-icon :size="15" color="#007f99">
              <Bell />
            </el-icon>
          </div>
          <div v-if="item.isread" style="color: #939899" class="ml-4 w-fit flex-1">
            <span class="text-sm">{{ item.title }}</span>
            <div class="mt-1 flex justify-between">
              <span class="text-[xx-small]">发送人：{{ item.sender }}</span>
              <span class="text-[xx-small]">{{ item.date }}</span>
            </div>
          </div>
          <div v-if="!item.isread" class="ml-4 w-fit flex-1">
            <span class="text-sm">{{ item.title }}</span>
            <div style="color: #939899" class="mt-1 flex justify-between">
              <span class="text-[xx-small]">发送人：{{ item.sender }}</span>
              <span class="text-[xx-small]">{{ item.date }}</span>
            </div>
          </div>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup>
  /* 我的消息 */
  import { useRouter } from 'vue-router';
  const router = useRouter();

  let datali = [
    {
      title: '5367892脑疾病研究',
      sender: '李耀',
      date: '2022-23-30 02:22:10',
      isread: true,
    },
    {
      title: '8743962神经疾病研究',
      sender: '李禹宣',
      date: '2019-11-22 12:05:30',
      isread: true,
    },
    {
      title: '5362322脑疾病研究',
      sender: '李立新',
      date: '2022-03-12 12:23:12',
      isread: false,
    },
    {
      title: '3463462胃部疾病研究',
      sender: '蒲一番',
      date: '2021-12-09 12:10:30',
      isread: false,
    },
    {
      title: '8958462脑部疾病研究',
      sender: '朱玲玲',
      date: '2021-12-09 12:17:30',
      isread: false,
    },
    {
      title: '8958462脑部疾病研究',
      sender: '朱玲玲',
      date: '2021-12-09 12:17:30',
      isread: false,
    },
  ];

  const onMore = () => {
    router.push({ name: 'WorkbenchMsg' });
  };
</script>

<style>
  .isread {
    background-color: #ebf5f7;
  }
  .notread {
    background-color: #f0f2f5;
  }
</style>
