<template>
  <div class="h-full overflow-hidden">
    <el-scrollbar height="100%">
      <ul class="mx-auto grid grid-cols-4 gap-4 p-5">
        <li
          v-for="(item, index) in list"
          :key="index"
          class="flex flex-col overflow-hidden rounded-md p-4 text-justify"
          :style="{
            boxShadow: 'var(--el-box-shadow-light)',
          }"
        >
          <div class="mb-2 h-0 flex-1">
            <div>{{ item.name }}</div>
            <div class="my-2 text-xs text-tip">{{ item.desc }}</div>
          </div>
          <el-button type="primary" style="width: 100px" @click="handleDownload(item)">去下载</el-button>
        </li>
      </ul>
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
  const list = ref([
    {
      name: 'FileZilla',
      url: 'https://filezilla-project.org/download.php?type=client',
      desc: 'FileZilla是一个免费开源的FTP软件，分为客户端版本和服务器版本，具备所有的FTP软件功能。可控性、有条理的界面和管理多站点的简化方式使得Filezilla客户端版成为一个方便高效的FTP客户端工具，而FileZilla Server则是一个小巧并且可靠的支持FTP&SFTP的FTP服务器软件。',
    },
    {
      name: 'bcftools',
      url: 'http://www.htslib.org/download/',
      desc: 'BCFtools 是一款多种实用工具的集合，它可以用于处理VCF文件和二进制的BCF文件。它可以接受VCF格式、压缩的VCF格式以及BCF格式，并能自动检测输入的格式类型。在有索引文件存在的条件下，BCFtools 可以应用于所有场景，在没有索引文件存在时，BCFtools只能应用于部分场景。',
    },
    {
      name: 'bedtools',
      url: 'https://github.com/arq5x/bedtools2/releases',
      desc: 'Bedtools 是一款功能极为丰富、在生物信息学领域广泛应用的开源工具集，主要用于处理和分析以 BED（Browser Extensible Data）格式存储的基因组区间数据 ，当然也能处理其他相关格式，像 GFF、VCF 等。其核心优势在于快速、灵活地执行各类基因组区间的操作',
    },
    {
      name: 'BGEN',
      url: 'http://code.enkre.net/bgen/tarball/release',
      desc: 'BGEN（Binary GENotype format）是一种二进制的基因型数据存储格式，旨在高效存储大规模的人类全基因组关联分析（GWAS）数据 。它相较于传统格式，具有显著的存储和读取优势',
    },
    {
      name: 'bgzip',
      url: 'https://github.com/samtools/htslib/releases',
      desc: 'bgzip是一个用于压缩和索引生物信息学数据文件的工具，可以在压缩文件大小和读取性能之间取得平衡，提高对大型生物信息学文件的处理效率。它是基于GZIP算法，并在其基础上添加了块压缩和索引功能。',
    },
    {
      name: 'BOLT-LMM',
      url: 'https://alkesgroup.broadinstitute.org/BOLT-LMM/downloads/',
      desc: 'BOLT-LMM（Bayesian-optimized Likelihood for Mixed Models）是一款专为全基因组关联分析（GWAS）设计的强大工具。它整合了混合模型方法，用于剖析复杂性状的遗传结构，尤其是在处理大样本量和存在群体分层问题的数据时表现出色',
    },
    {
      name: 'Picard',
      url: 'https://github.com/broadinstitute/picard/releases',
      desc: 'Picard 是一款由 Broad Institute 开发的，用于处理高通量测序数据的 Java 工具集，在基因组学研究流程里占据重要地位',
    },
    {
      name: 'Plato',
      url: 'https://ritchielab.org/software/plato-download',
      desc: 'PLATO 是一个用 C++ 编写的独立程序，是用于各种遗传数据的灵活可扩展分析工具。它包括一组可配置的质量控制和分析步骤，可在单个命令步骤中用于数据的过滤和分析。通过对遗传数据的抽象，只需基本的计算专业知识即可轻松添加自定义分析或过滤步骤。它可以读取 VCF、Beagle、TPED 和 LGEN 文件，支持并行运行，能适应多种不同的数据和分析',
    },
    {
      name: 'plink',
      url: 'https://www.cog-genomics.org/plink/',
      desc: 'Plink 是一款极为常用且功能强大的开源全基因组关联分析（GWAS）工具集，主要用于处理和分析以 PLINK 二进制格式（.bed/.bim/.fam ）存储的基因型数据，在人类遗传学、动植物遗传学等众多遗传学研究领域应用广泛',
    },
    {
      name: 'plink2',
      url: 'https://www.cog-genomics.org/plink/2.0/',
      desc: 'Plink2 是 Plink 的升级版，在功能上更为强大与丰富，延续了处理基因分型数据的核心能力，并在运算速度、内存管理、数据兼容性等诸多方面有所优化',
    },
    {
      name: 'QCTool',
      url: 'https://www.chg.ox.ac.uk/~gav/qctool/documentation/download.html',
      desc: 'QCTool 是一款在遗传学与基因组学领域广泛应用的质量控制（Quality Control，QC）工具',
    },
    {
      name: 'REGENIE',
      url: 'https://github.com/rgcgithub/regenie/releases',
      desc: 'REGENIE 是一个由 Regeneron 遗传学中心开发的基于 C++ 的高效软件，专门用于大规模的全基因组关联研究（GWAS）',
    },
    {
      name: 'sambamba',
      url: 'https://github.com/biod/sambamba/releases',
      desc: 'sambamba 是一款由 Artem Tarasov 开发的用于处理大规模测序数据的高性能生物信息学工具，主要针对 sam/bam 格式的文件',
    },
    {
      name: 'samtools',
      url: 'https://github.com/samtools/samtools/releases',
      desc: 'samtools 是一款广泛应用于生物信息学领域，专门处理高通量测序数据的基础工具集，由 Heng Li 开发。它主要针对 SAM（Sequence Alignment/Map）和 BAM（Binary SAM）格式文件开展操作，在基因组测序数据分析流程里扮演关键角色',
    },
    {
      name: 'seqtk',
      url: 'https://github.com/lh3/seqtk/releases',
      desc: 'seqtk 是一款轻量级、高效的序列处理工具集，专为处理 FASTA 和 FASTQ 格式的核酸序列数据而设计，由生信大神 Heng Li 开发，在高通量测序数据分析流程里应用广泛',
    },
    {
      name: 'tabix',
      url: 'https://github.com/samtools/htslib/releases',
      desc: 'tabix 是一款高效的索引与数据检索工具，主要用于处理基因组学中的各类文本文件，尤其是 VCF（Variant Call Format） 和 BED（Browser Extensible Data）格式，由 Samtools 团队开发，tabix和bgzip二进制文件现在是HTSlib项目的一部分',
    },
    {
      name: 'vcflib',
      url: 'https://github.com/vcflib/vcflib/releases',
      desc: 'vcflib 是一个功能丰富的 C++ 工具集，专为处理 VCF（Variant Call Format）文件而设计，在基因组学研究领域应用广泛',
    },
    {
      name: 'vcftools',
      url: 'https://vcftools.github.io/index.html',
      desc: 'vcftools 是一款在基因组学领域极为常用的工具集，专注于处理 VCF（Variant Call Format）文件，在全基因组关联分析（GWAS）、群体遗传学研究等场景中发挥关键作用',
    },
  ]);

  const handleDownload = (item) => {
    window.open(item.url);
  };
</script>

<style scoped lang="scss"></style>
