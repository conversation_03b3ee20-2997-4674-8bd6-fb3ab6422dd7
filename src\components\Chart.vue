<template>
  <div ref="box" class="echart" />
</template>

<script setup>
  /* echarts基础组件 */
  import * as echarts from 'echarts';
  import { debounce } from 'lodash-es';

  const props = defineProps({
    option: {
      required: true,
      type: Object,
    },
  });
  const emit = defineEmits(['get-instance', 'mouseover', 'mouseout', 'click']);

  let chart = null;
  const box = ref();
  let resize = null;

  watch(
    () => props.option,
    (value) => {
      if (JSON.stringify(value) !== '{}' && chart) {
        setOption();
      }
    },
    { deep: true }
  );

  function init() {
    if (chart) {
      chart.dispose();
    }
    chart = echarts.init(box.value);
    resize = debounce(chart.resize, 500);
    window.addEventListener('resize', resize);
    setEvent();
    emit('get-instance', chart);
    if (JSON.stringify(props.option) !== '{}') {
      setOption();
    }
  }

  function setOption() {
    chart.setOption(props.option, true);
  }

  function setEvent() {
    chart.on('mouseover', (params) => {
      emit('mouseover', params);
    });
    chart.on('mouseout', (params) => {
      emit('mouseout', params);
    });
    chart.on('click', (params) => {
      emit('click', params);
    });
  }

  onMounted(() => {
    init();
  });

  onUnmounted(() => {
    window.removeEventListener('resize', resize);
    if (chart) {
      chart.dispose();
      chart = null;
    }
  });
</script>

<style lang="scss" scoped>
  .echart {
    width: 100%;
    height: 100%;
  }
</style>
