{"css.customData": [".vscode/tailwindcss.json"], "[vue]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[html]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[css]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[scss]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "files.associations": {"/src/*.css": "tailwindcss"}, "editor.formatOnSave": true}