<template>
  <div class="flex h-full flex-col">
    <h2 class="flex h-[60px] items-center bg-w px-[28px] text-xl font-bold">数据更新</h2>

    <div class="flex h-0 flex-1 p-5">
      <div class="mr-[10px] flex w-[280px] flex-col rounded bg-w pt-4">
        <h3 class="pl-5">专题库列表</h3>
        <el-scrollbar class="h-0 flex-1">
          <CustomMenu v-model="menuId" :data="catalogData" />
        </el-scrollbar>
      </div>

      <div class="flex h-full w-0 flex-1 flex-col rounded-md bg-w pt-5">
        <div class="flex justify-between px-10">
          <div>
            <el-button type="primary" @click="onUpdate"> 更新数据 </el-button>
          </div>
        </div>

        <div class="mt-3 h-0 w-full flex-1 px-10">
          <el-table
            height="100%"
            :data="tableData"
            style="width: 100%"
            class="c-table-header"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="name" label="姓名" />
            <el-table-column prop="age" label="年龄" />
            <el-table-column prop="post" label="职位" />
            <el-table-column prop="dataId" label="ID" />
            <el-table-column label="操作" width="80">
              <template #default="{ row }">
                <el-popconfirm title="确定删除此项？" @confirm="onDel(row)">
                  <template #reference>
                    <el-button link type="primary"> 删除 </el-button>
                  </template>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div class="pagination-bottom">
          <el-pagination
            background
            layout="total, sizes, prev, pager, next, jumper"
            :page-size="pagination.pageSize"
            :total="tableData.length"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import CustomMenu from '@/components/custom-menu/CustomMenu.vue';
  const menuId = ref('11');
  const catalogData = ref([
    {
      id: '1',
      title: 'XXXXXX医院库1',
      children: [
        {
          id: '11',
          title: '用户表1',
        },
        {
          id: '12',
          title: '用户表2',
        },
      ],
    },
    {
      id: '2',
      title: 'XXXXXX医院库2',
      children: [
        {
          id: '21',
          title: '报名表1',
        },
        {
          id: '22',
          title: '报名表2',
        },
      ],
    },
  ]);

  //表格
  const tableData = ref([
    {
      id: 1,
      name: '姓名',
      age: 34,
      post: '副教授',
      dataId: '1546232321313',
    },
  ]);
  const checkList = ref([]);
  const handleSelectionChange = (val) => {
    checkList.value = val;
  };
  const onDel = (row) => {};
  const onUpdate = () => {};

  //页码
  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  const handleCurrentChange = (e) => {
    pagination.page = e;
  };
  const handleSizeChange = (e) => {
    pagination.pageSize = e;
  };
</script>
