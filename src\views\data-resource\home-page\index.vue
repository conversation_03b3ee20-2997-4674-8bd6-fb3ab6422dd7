<script setup>
  import img1 from '@/assets/img/symbol1.png';
  import img2 from '@/assets/img/symbol2.png';
  import img3 from '@/assets/img/symbol3.png';
  import img4 from '@/assets/img/symbol4.png';
  const router = useRouter();

  const data = [
    {
      header: '数据浏览',
      text: '通过根据数据项的来源类别查找数据项',
      img: img1,
      pathname: 'DataBrowse',
    },
    {
      header: '数据搜索',
      text: '通过搜索关键字和其他特征来查找数据项',
      img: img2,
      pathname: 'ResourceDataSearch',
    },
    {
      header: '用户须知',
      text: '数据接入和发布相关信息',
      img: img3,
      pathname: 'ResourceUserNotice',
    },
    {
      header: '工具下载',
      text: '下载相关工具',
      img: img4,
      pathname: 'ResourceTools',
    },
  ];

  const onEnter = (e) => {
    router.push({ name: e });
  };
</script>

<template>
  <div class="header">
    <div class="center-content">
      <h2 class="title">数据资源馆</h2>
      <div class="description">
        国家脑疾病临床大数据平台收录了海量脑疾病相关数据，数据资源馆从多个维度对脑疾病数据进行展示，并提供数据检索等相关功能，方便科研人员了解数据详情，从海量数据资源中快速选择完成相应研究所须的数据内容。
      </div>
    </div>
  </div>

  <div class="center-content data-container">
    <div v-for="(item, index) in data" :key="index" class="dataitems">
      <img class="symbol" :src="item.img" />
      <div>
        <div class="dataitems-title">
          {{ item.header }}
        </div>
        <p class="dataitems-p">
          {{ item.text }}
        </p>
        <el-button size="small" color="#007f99" @click="onEnter(item.pathname)"> 立即进入 </el-button>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
  .header {
    height: 300px;
    background-image: url(@/assets/img/data-resource/bg-header.png);
    min-width: $main-width;
    background-repeat: no-repeat;
    background-size: 100% auto;
    background-position-y: center;
    font-size: 14px;

    .center-content {
      padding-top: 50px;
    }

    .title {
      font-size: 36px;
      font-weight: 700;
    }

    .description {
      margin-top: 30px;
      color: rgba(48, 51, 51, 0.9);
      width: 640px;
      text-align: justify;
    }
  }

  .data-container {
    margin-top: 60px;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    font-size: 14px;

    .dataitems {
      display: flex;
      border: 1px solid #e6e6e6;
      padding: 40px 60px;

      &:hover {
        border: 1px solid #007f99;
        background-color: #f2f8fa;

        .dataitems-title {
          color: #007f99;
        }
      }
    }

    .symbol {
      margin-right: 30px;
      width: 52px;
      height: 56px;
    }

    .dataitems-title {
      font-size: 22px;
      font-weight: 700;
      line-height: 32px;
    }

    .dataitems-p {
      margin-top: 10px;
      margin-bottom: 27px;
      color: #565b5c;
    }
  }
</style>
