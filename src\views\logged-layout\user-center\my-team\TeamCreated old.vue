<template>
  <el-scrollbar height="100%">
    <ul v-if="tableData.length" class="overflow-hidden px-5 pt-5">
      <li v-for="(item, index) in tableData" :key="index" class="mb-5 rounded bg-w px-7 py-4">
        <div class="flex items-center justify-end">
          <template v-if="!item.edit">
            <div class="flex">
              <div class="mr-4 flex cursor-pointer items-center text-sm text-p" @click="onEdit(item)">
                <el-icon>
                  <Edit />
                </el-icon>
                <span class="ml-2">编辑</span>
              </div>
              <el-popconfirm title="确定删除？" @confirm="onDel(index)">
                <template #reference>
                  <div class="flex cursor-pointer items-center text-sm text-tip">
                    <el-icon color="#939899">
                      <Delete />
                    </el-icon>
                    <span class="ml-2">删除团队</span>
                  </div>
                </template>
              </el-popconfirm>
            </div>
          </template>
          <span v-else class="flex cursor-pointer items-center text-sm text-tip" @click="onCancel(item)">取消</span>
        </div>

        <el-form ref="formRefs" label-position="right" label-width="110" :model="item" :rules="rules" class="mt-5">
          <div class="flex items-start justify-center">
            <div class="w-[200px]" />
            <el-form-item label="团队名称:" prop="name">
              <el-input v-model="item.name" :disabled="!item.edit" placeholder="请输入团队名称" style="width: 480px" />
            </el-form-item>
          </div>

          <div class="flex items-start justify-center">
            <div class="flex w-[200px] items-center pt-1">
              <div class="h-[13px] w-[3px] bg-p" />
              <span class="ml-2">选择机构</span>
            </div>
            <el-form-item label="研究机构:" prop="org">
              <el-select v-model="item.org" :disabled="!item.edit" placeholder="请选择研究机构" style="width: 480px">
                <el-option
                  v-for="orgItem in orgOptions"
                  :key="orgItem.value"
                  :label="orgItem.label"
                  :value="orgItem.value"
                />
              </el-select>
            </el-form-item>
          </div>

          <div class="mt-5 flex items-start justify-center">
            <div class="flex w-[200px] items-center pt-1">
              <div class="h-[13px] w-[3px] bg-p" />
              <span class="ml-2">人员配置</span>
            </div>
            <el-form-item label="首席研究员:" prop="leadResearcher">
              <el-input
                v-model="item.leadResearcher"
                :disabled="!item.edit"
                placeholder="请输入首席研究员名称"
                style="width: 480px"
              />
            </el-form-item>
          </div>

          <div class="flex justify-center">
            <div class="w-[200px]" />
            <el-checkbox-group v-model="item.deputy" class="flex flex-col" @change="deputyChange">
              <el-form-item
                v-for="(cooperatorItem, cooperatorIndex) in item.cooperator"
                :key="cooperatorIndex"
                :label="cooperatorIndex === 0 ? '合作者' : ''"
                :prop="'cooperator.' + cooperatorIndex + '.name'"
                :rules="{
                  required: true,
                  message: '请输入合作者名称',
                  trigger: 'blur',
                }"
              >
                <div class="relative">
                  <el-input
                    v-model="cooperatorItem.name"
                    :disabled="!item.edit"
                    placeholder="请输入合作者名称"
                    style="width: 480px"
                  />
                  <div v-if="item.edit" class="absolute right-[-116px] top-[0px] flex w-[100px] items-center">
                    <el-checkbox :label="cooperatorIndex"> 代表 </el-checkbox>
                    <el-link
                      v-if="item.cooperator.length > 1"
                      type="primary"
                      :underline="false"
                      class="ml-4"
                      @click="onDelMember(item, cooperatorIndex)"
                    >
                      删除
                    </el-link>
                  </div>
                </div>
              </el-form-item>
            </el-checkbox-group>
          </div>
          <div v-if="item.edit" class="flex justify-center">
            <div class="w-[200px]" />
            <el-form-item>
              <div class="w-[480px]">
                <el-link type="primary" :underline="false" @click="onAddMember(item, cooperatorIndex)">
                  添加合作者
                </el-link>
              </div>
            </el-form-item>
          </div>

          <div class="flex items-start justify-center">
            <div class="flex w-[200px] items-center pt-1">
              <div class="h-[13px] w-[3px] bg-p" />
              <span class="ml-2">材料转让协议信息</span>
            </div>
            <el-form-item label="机构官方名称:" prop="orgName">
              <el-input
                v-model="item.orgName"
                :disabled="!item.edit"
                placeholder="请输入机构官方名称"
                style="width: 480px"
              />
            </el-form-item>
          </div>

          <div class="flex justify-center">
            <div class="w-[200px]" />
            <el-form-item label="机构联系人:" prop="orgContact">
              <el-input
                v-model="item.orgContact"
                :disabled="!item.edit"
                placeholder="请输入机构联系人"
                style="width: 480px"
              />
            </el-form-item>
          </div>

          <div v-if="item.edit" class="flex justify-center">
            <div class="w-[200px]" />
            <el-form-item>
              <div class="w-[480px]">
                <el-button type="primary" @click="onSave(index)"> 保存 </el-button>
              </div>
            </el-form-item>
          </div>
        </el-form>
      </li>
    </ul>
    <el-empty v-else description="暂无团队信息" />

    <div class="flex justify-center pb-5">
      <el-button type="primary" :icon="Plus" @click="onAdd"> 新增团队 </el-button>
    </div>
  </el-scrollbar>
</template>

<script setup>
  import { Plus, Delete, Edit } from '@element-plus/icons-vue';

  const tableData = ref([
    {
      name: '',
      org: '',
      leadResearcher: '',
      cooperator: [{ name: '' }],
      deputy: [0],
      orgName: '',
      orgContact: '',
      edit: false,
    },
  ]);
  const rules = reactive({
    name: [{ required: true, message: '请输入团队名称', trigger: 'blur' }],
    org: [{ required: true, message: '请选择研究机构', trigger: 'blur' }],
    leadResearcher: [{ required: true, message: '请输入首席研究员名称', trigger: 'blur' }],
    orgName: [{ required: true, message: '请输入机构官方名称', trigger: 'blur' }],
    orgContact: [{ required: true, message: '请输入机构联系人', trigger: 'blur' }],
  });
  const orgOptions = [
    {
      value: 1,
      label: '桂林电子科技大学',
    },
    {
      value: 2,
      label: '广西师范大学',
    },
  ];
  const onAdd = () => {
    tableData.value.push({
      org: '',
      leadResearcher: '',
      cooperator: [{ name: '' }],
      deputy: [0],
      orgName: '',
      orgContact: '',
      edit: true,
    });
  };
  const onDel = (index) => {
    tableData.value.splice(index, 1);
  };
  const onEdit = (item) => {
    item.edit = true;
  };

  const onAddMember = (item) => {
    item.cooperator.push({ name: '' });
  };
  const onDelMember = (item, cooperatorIndex) => {
    item.cooperator.splice(cooperatorIndex, 1);
  };
  const deputyChange = (e) => {};
  const formRefs = ref([]);
  const onSave = (index) => {
    formRefs.value[index].validate((valid) => {
      if (valid) {
        console.log(123);
      }
    });
  };
  const onCancel = (item) => {
    item.edit = false;
  };
</script>
