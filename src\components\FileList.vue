<template>
  <ul class="flex flex-wrap">
    <li
      v-for="(item, index) in list"
      :key="index"
      class="file-item relative mb-3 mr-3 min-w-[80px] cursor-pointer rounded bg-[#e5e7ea] px-4 py-2"
    >
      <span>{{ item.name }}</span>
      <div class="file-hover">
        <el-icon size="20px" @click="viewFile(item)">
          <View />
        </el-icon>
        <!-- <a :href="item.url" target="downloadFile" download class="relative bottom-[-3px] ml-3">
        </a> -->
        <el-icon class="ml-3" size="20px" color="#fff" @click="onDownload(item)"><Download /></el-icon>
      </div>
    </li>
  </ul>
</template>

<script setup lang="ts">
  /* 文件预览、下载列表 */
  interface Props {
    list: FileListData[];
  }
  withDefaults(defineProps<Props>(), { list: () => [] });
  const emit = defineEmits<{ view: [item: FileListData]; download: [item: FileListData] }>();

  const viewFile = (item: FileListData) => {
    emit('view', item);
  };
  const onDownload = (item: FileListData) => {
    emit('download', item);
  };
</script>

<style lang="scss" scoped>
  .file-item:hover {
    background-color: rgba(0, 0, 0, 0.65);

    .file-hover {
      display: flex;
    }
  }

  .file-hover {
    display: none;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    color: #fff;
  }
</style>
