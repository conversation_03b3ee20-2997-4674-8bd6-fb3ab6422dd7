<template>
  <div class="h-[33%] w-full bg-p 2xl:h-[25%]" style="min-height: fit-content">
    <div class="relative top-0 box-border flex h-[8vh] max-h-[55px] w-full justify-between bg-p px-[1.5%]">
      <div class="flex h-full w-fit items-center">
        <img class="mr-[1vw] h-[65%]" src="@/assets/a52x.png" />
        <span class="text-sm font-black text-[#ffffff] 2xl:text-base">工作台</span>
      </div>
      <div class="flex h-full w-fit items-center">
        <div class="flex h-full max-h-[55px] w-[8vw] items-center">
          <svgicon
            style="height: 45%; width: min-content; cursor: pointer"
            class-name="allsvg"
            icon-name="icon-a-naolingtixingtongzhi"
            color="#ffffff"
          />
          <svgicon
            style="height: 45%; width: min-content; cursor: pointer"
            icon-name="icon-gouwuche1"
            color="#ffffff"
          />
        </div>
        <div class="ml-5 flex h-full w-[8vw] max-w-fit items-center justify-between">
          <svgicon style="height: 45%; width: 20%" icon-name="icon-touxiangtongyong" color="#ffffff" />
          <el-dropdown>
            <span class="el-dropdown-link">
              {{ username }}
              <el-icon class="el-icon--right">
                <CaretBottom />
              </el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item>Action 1</el-dropdown-item>
                <el-dropdown-item>Action 2</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>
    <div class="ml-[4.7vw] mt-[3vh] flex flex-col">
      <span class="text-lg font-black text-[#ffffff] 2xl:text-xl">{{ username }}，欢迎登录本系统！</span>
      <div class="mt-[2vh] flex flex-col">
        <span class="mb-2 text-xs text-w">上次登录时间：2022-03-12 12：00</span>
        <span class="text-xs text-w">上次登录地点：山东省济南市</span>
      </div>
    </div>
  </div>
  <div class="h-fit w-[100%] bg-[#f0f2f5]">
    <div class="allicon flex w-[25%] flex-wrap justify-between p-[3vw] py-[1vw] 2xl:w-[22%]">
      <div>
        <svgicon style="cursor: pointer; width: 45%" icon-name="icon-shenqing" />
        <span>项目申请</span>
      </div>
      <div>
        <svgicon style="cursor: pointer; width: 45%" icon-name="icon-guanliyuan_guanliyuanrizhi" />
        <span>项目管理</span>
      </div>
      <div>
        <svgicon style="cursor: pointer; width: 45%" color="#ffffff" icon-name="icon-tongjitu" /><span>项目管理</span>
      </div>
      <div><svgicon style="cursor: pointer; width: 45%" icon-name="icon-morentouxiang" /><span>项目管理</span></div>
      <div><svgicon style="cursor: pointer; width: 45%" icon-name="icon-gouwuche" /><span>项目管理</span></div>
      <div><svgicon style="cursor: pointer; width: 45%" icon-name="icon-naoling" /><span>项目管理</span></div>
      <div>
        <svgicon style="cursor: pointer; width: 45%" icon-name="icon-wangzhanzidingyixinxizhanshi" /><span
          >项目管理</span
        >
      </div>
      <div><svgicon style="cursor: pointer; width: 45%" icon-name="icon-shujuku" /><span>项目管理</span></div>
    </div>
  </div>
</template>
<script setup>
  import { ref } from 'vue';
  let username = ref('admin');
</script>
<style scoped>
  .allicon div {
    height: 6vw;
    width: 35%;
    box-sizing: border-box;
    margin: 5%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }
  .allicon div:hover {
    background-color: #007f99;
    box-shadow: 8px 8px 8px #8ec2cf;
    color: white;
    border-radius: 8px;
  }
  .allicon span {
    font-size: small;
    margin-bottom: 6px;
    /* color: #343737; */
  }
  .example-showcase .el-dropdown-link {
    cursor: pointer;
    color: none;
    display: flex;
    align-items: center;
  }
  .el-dropdown span {
    color: white;
  }

  .el-dropdown {
    --el-dropdown-menu-box-shadow: none;
    --el-dropdown-menuItem-hover-fill: none;
    --el-dropdown-menuItem-hover-color: none;
    color: #007f99 !important;
    outline: none;
  }

  .el-dropdown:hover {
    outline: none;
  }
</style>
