/*
 * @OriginalName: 数据资源系统中项目申请管理模块
 * @Description: 项目申请的创建、更新和查询
 */
import { request } from '@/utils/request';

/**
 * 审核项目申请
 * @description 对状态为待审批的项目进行审核。审核结论必须为：审核通过/审核未通过
 */
export function verifyApplication(data: ApplicationProcessingDTO) {
  return request<RApplicationVO>(`/application/verifyApplication`, {
    method: 'post',
    data,
  });
}

/**
 * 同步更新目申请中的数据表
 * @description 按Application的Id，同步更新项目申请的数据表
 */
export function syncCBDDefTablesForApplication(
  applicationId: number,
  data: Array<number>,
  params?: { applicationId: number }
) {
  return request<RListCBDDefTableVO>(`/application/syncCBDDefTablesForApplication/${applicationId}`, {
    method: 'post',
    data,
    params,
  });
}

/**
 * 移除项目中团队的角色
 * @description 按ApplicationTeamRole的Id，删除一个或多个项目团队角色关联的记录。移除项目中团队的角色。
 */
export function deleteApplicationTeaRolemById(data: Array<ApplicationTeamRoleId>) {
  return request<R>(`/application/removeTeamRoleFromApplication`, {
    method: 'post',
    data,
  });
}

/**
 * 移除项目中的团队
 * @description 按ApplicationTeam的Id，删除一个或多个项目团队关联的记录。移除项目中的团队。
 */
export function deleteApplicationTeamById(data: Array<ApplicationTeamId>) {
  return request<R>(`/application/removeTeamFromApplication`, {
    method: 'post',
    data,
  });
}

/**
 * 新建或更新项目申请
 * @description 按ApplicationDTO的信息，新建或更新项目的信息。
 */
export function newOrUpdateApplication(data: ApplicationDTO) {
  return request<RApplicationVO>(`/application/newOrUpdateApplication`, {
    method: 'post',
    data,
  });
}

/**
 * 查找项目申请
 * @description 按动态条件，获取满足相应条件的项目的基本信息。如果有主键，按主键精确查找，若无主键，则其它条件按与操作进行模糊查询。所有条件均为空时，返回全部记录。
 */
export function findApplicationVOByCriteria(data: ApplicationCriteria) {
  return request<REntityVOPage>(`/application/findApplicationVOByCriteria`, {
    method: 'post',
    data,
  });
}

/**
 * 向项目中添加一个团队
 * @description 按ApplicationTeamDTO的信息，新建或更新项目团队关联的信息，向项目中添加一个团队。
 */
export function newOrUpdateApplicationTeam(data: ApplicationTeamDTO) {
  return request<RApplicationTeamVO>(`/application/addTeamToApllication`, {
    method: 'post',
    data,
  });
}

/**
 * 为项目中的团队分配角色
 * @description 按ApplicationTeamRoleDTO的信息，新建或更新项目团队关角色联的信息。为项目中的团队分配角色。
 */
export function newOrUpdateApplicationTeamRole(data: ApplicationTeamRoleDTO) {
  return request<RApplicationTeamRoleVO>(`/application/addTeamRoleToApplication`, {
    method: 'post',
    data,
  });
}

/**
 * 查找审核结论
 * @description 项目申请被审核的结论。
 */
export function findVerifyResult_1(applicationId: number, params?: { applicationId: number }) {
  return request<RApplicationProcessingVO>(`/application/findVerifyResult/${applicationId}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找项目申请中的数据表
 * @description 按Application的Id，查找项目申请的数据表
 */
export function findTableByApplicationId(
  applicationId: number,
  pageNum: number,
  pageSize: number,
  params?: { applicationId: number; searchInput?: string; pageNum: number; pageSize: number }
) {
  return request<RVOPage>(`/application/findTableByApplicationId/${applicationId}/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找项目申请中的订单
 * @description 按Application的Id，查找订单
 */
export function findOrderByApplicationId(
  applicationId: number,
  pageNum: number,
  pageSize: number,
  params?: { applicationId: number; pageNum: number; pageSize: number }
) {
  return request<RVOPage>(`/application/findOrderByApplicationId/${applicationId}/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找项目的参与团队在项目中的角色
 * @description 按Application的Id，查找项目申请的各个参与团队所承担的角色。
 */
export function findApplicationTeamRoleByAppId(appId: number, params?: { appId: number }) {
  return request<RListApplicationTeamRoleVO>(`/application/findApplicationTeamRoleByAppId/${appId}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找项目的参与团队
 * @description 按Application的Id，查找项目申请的全部参与团队。
 */
export function findApplicationTeamByAppId(appId: number, params?: { appId: number }) {
  return request<RListApplicationTeamVO>(`/application/findApplicationTeamByAppId/${appId}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找项目申请
 * @description 按Application的Id，精确查找数据记录。
 */
export function findAppById(appId: number, params?: { appId: number }) {
  return request<RApplicationVO>(`/application/findAppById/${appId}`, {
    method: 'get',
    params,
  });
}

/**
 * 分页显示完成审核的项目申请
 * @description 分页显示状态为“审核通过/审核未通过”的全部项目申请。
 */
export function findAllVerifyApplicationByStateIn(
  pageNum: number,
  pageSize: number,
  params?: { pageNum: number; pageSize: number }
) {
  return request<REntityVOPage>(`/application/findAllVerifyApplicationByStateIn/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 分页显示全部项目申请
 * @description 分页显示状态为“待审批”的全部项目申请。
 */
export function findAllVerifyApplication(
  pageNum: number,
  pageSize: number,
  params?: { pageNum: number; pageSize: number }
) {
  return request<REntityVOPage>(`/application/findAllVerifyApplication/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找项目申请
 * @description 按Application的Id，精确查找数据记录。
 */
export function findAllApplicationById(appId: Array<number>, params?: { appId: Array<number> }) {
  return request<RListApplicationVO>(`/application/findAllApplicationById/${appId}`, {
    method: 'get',
    params,
  });
}

/**
 * 分页显示全部项目申请
 * @description 分页显示全部项目申请。
 */
export function findAllApplication(pageNum: number, pageSize: number, params?: { pageNum: number; pageSize: number }) {
  return request<REntityVOPage>(`/application/findAllApplication/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除项目
 * @description 按Application的Id，删除一个或多个项目的记录。
 */
export function deleteApplicationById(applicationId: Array<number>, params?: { applicationId: Array<number> }) {
  return request<R>(`/application/deleteApplicationById/${applicationId}`, {
    method: 'get',
    params,
  });
}

/**
 * 提交项目申请
 * @description 将状态为“未提交”或“审核未通过”的项目提交审核。
 */
export function applyApplication(
  applicationId: number,
  userId: number,
  params?: { applicationId: number; userId: number }
) {
  return request<RApplicationVO>(`/application/applyApplication/${applicationId}/${userId}`, {
    method: 'get',
    params,
  });
}
