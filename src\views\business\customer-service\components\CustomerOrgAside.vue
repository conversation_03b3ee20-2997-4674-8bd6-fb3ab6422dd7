<template>
  <div class="flex h-full w-[400px] flex-col border-r border-border">
    <el-tabs v-model="tabName" class="tabs">
      <el-tab-pane label="全部" name="全部" />
      <el-tab-pane label="待审批" name="待审批" />
      <el-tab-pane label="已审批" name="已审批" />
    </el-tabs>

    <el-scrollbar class="h-0 flex-1">
      <div class="overflow-hidden px-5 pt-5">
        <el-input v-model="search" placeholder="请输入关键字">
          <template #prepend>
            <el-select v-model="prepend" style="width: 100px">
              <el-option v-for="(item, index) in prependOptions" :key="index" :label="item.value" :value="item.value" />
            </el-select>
          </template>
          <template #append>
            <el-button :icon="Search" @click="onSearch" />
          </template>
        </el-input>

        <ul class="list mt-5 text-sm">
          <li
            v-for="(item, index) in list"
            :key="index"
            class="mb-3 cursor-pointer rounded border border-border p-4 hover:shadow-md"
            :class="active == index ? 'active' : ''"
            @click="selectItem(item, index)"
          >
            <div class="flex items-center justify-between">
              <span class="text-base font-bold">{{ item.title }}</span>
              <div :class="statusClass[item.status]" class="h-6 px-3 leading-6">
                <span>{{ statusText[item.status] }}</span>
              </div>
            </div>

            <div class="mt-2">
              <span class="text-[#aeb1b2]">机构名称</span>
              <span class="ml-2 text-[#595e5f]">{{ item.fullNameCn }}</span>
            </div>
            <div class="mt-2">
              <span class="text-[#aeb1b2]">机构性质</span>
              <span class="ml-2 text-[#595e5f]">{{ item.character }}</span>
            </div>
            <div class="mt-2">
              <span class="text-[#aeb1b2]">法人姓名</span>
              <span class="ml-2 text-[#595e5f]">{{ item.corporateName }}</span>
            </div>
            <div class="mt-2">
              <span class="text-[#aeb1b2]">{{ item.startDate }}</span>
            </div>
          </li>
        </ul>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup>
  import { Search } from '@element-plus/icons-vue';
  const props = defineProps({ id: { type: [String, Number] } });

  const tabName = ref('待审批');
  const search = ref('');
  const prepend = ref('机构名称');
  const prependOptions = [
    {
      label: '机构名称',
      value: '机构名称',
    },
    {
      label: '机构性质',
      value: '机构性质',
    },
    {
      label: '法人姓名',
      value: '法人姓名',
    },
  ];
  const onSearch = () => {
    console.log(prepend.value);
    console.log(search.value);
  };

  const statusText = {
    0: '待审批',
    1: '通过',
    2: '驳回',
  };
  const statusClass = {
    0: 'status-todo',
    1: 'status-pass',
    2: 'status-reject',
  };
  let allList = ref([
    {
      id: 1,
      title: '新机构注册申请',
      status: 0,
      fullNameCn: '桂林电子科技大学',
      abbreviationCn: '桂电',
      fullNameEn: 'Guilin University Of Electronic Technology',
      abbreviationEn: 'GUOET',
      character: '学校',
      usci: '1245000049867145XC',
      corporateName: '张三',
      corporateIdNumber: '450324999999999999',
      intro:
        '桂林电子科技大学简介 桂林电子科技大学是全国四所电子科技大学之一，是工业和信息化部与广西壮族自治区共建高校，广西壮族自治区重点建设高校。 学校始建于1960年，1980 年经国务院批准成立桂林电子工业学院，2006年更名为桂林电子科技大学。学校先后隶属于第四机械工业部、电子工业部、机械电子工业部、中国电子工业总公司、信息产业部。',
      startDate: '2022-08-11 10:00:00',
    },
    {
      id: 2,
      title: '新机构注册申请',
      status: 1,
      fullNameCn: '桂林电子科技大学',
      abbreviationCn: '桂电',
      fullNameEn: 'Guilin University Of Electronic Technology',
      abbreviationEn: 'GUOET',
      character: '学校',
      usci: '1245000049867145XC',
      corporateName: '张三',
      corporateIdNumber: '450324999999999999',
      intro: 'xxx',
      startDate: '2022-08-11 10:00:00',
    },
    {
      id: 3,
      title: '新机构注册申请',
      status: 2,
      fullNameCn: '桂林电子科技大学',
      abbreviationCn: '桂电',
      fullNameEn: 'Guilin University Of Electronic Technology',
      abbreviationEn: 'GUOET',
      character: '学校',
      usci: '1245000049867145XC',
      corporateName: '张三',
      corporateIdNumber: '450324999999999999',
      intro: 'xxxx',
      startDate: '2022-08-11 10:00:00',
    },
    {
      id: 4,
      title: '新机构注册申请',
      status: 0,
      fullNameCn: '桂林电子科技大学',
      abbreviationCn: '桂电',
      fullNameEn: 'Guilin University Of Electronic Technology',
      abbreviationEn: 'GUOET',
      character: '学校',
      usci: '1245000049867145XC',
      corporateName: '张三',
      corporateIdNumber: '450324999999999999',
      intro: 'xxxx',
      startDate: '2022-08-11 10:00:00',
    },
  ]);
  const list = computed(() => {
    let result = [];
    switch (tabName.value) {
      case '全部':
        result = allList.value;
        break;
      case '待审批':
        result = allList.value.filter((item) => item.status === 0);
        break;
      case '已审批':
        result = allList.value.filter((item) => item.status === 1 || item.status === 2);
        break;
    }
    return result;
  });
  let active = ref(0);
  const emit = defineEmits(['select']);
  let selectItem = (item, index) => {
    active.value = index;
    emit('select', item);
  };
  let selectValue = allList.value[0];
  if (props.id) {
    allList.value.forEach((item, index) => {
      if (item.id == props.id) {
        selectValue = item;
        active.value = index;
      }
    });
  }
  selectItem(selectValue, active.value);
</script>

<style scoped lang="scss">
  .tabs {
    --el-border-color-light: #e1e3e6;
    :deep(.el-tabs__header) {
      margin: 0;
    }
  }

  :deep(.el-tabs--top .el-tabs__item.is-top:nth-child(2)) {
    padding-left: 20px;
  }

  .list {
    .active {
      border-color: $color-primary;
    }

    .status-todo {
      color: #3a73e6;
      background-color: #ebf1fd;
    }

    .status-pass {
      color: #29b586;
      background-color: #e8f7f2;
    }

    .status-reject {
      color: #e64848;
      background-color: #fdecec;
    }
  }
</style>
