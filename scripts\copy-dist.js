import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { loadEnv } from 'vite';

// 获取当前文件的目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, '..');
const env = loadEnv('production', rootDir);

const sourceDistPath = path.join(rootDir, 'dist');
const targetDistPath = env.VITE_TARGET_DIST_PATH;

console.log(`正在将 dist 文件夹从 ${sourceDistPath} 复制到 ${targetDistPath}...`);

// 检查源目录是否存在
if (!fs.existsSync(sourceDistPath)) {
  console.error('错误: 源 dist 文件夹不存在!');
  process.exit(1);
}

// 检查目标目录是否存在
if (!fs.existsSync(targetDistPath)) {
  console.error(`错误: 目标路径 ${targetDistPath} 不存在!`);
  process.exit(1);
}

// 目标dist路径
const targetDistFullPath = path.join(targetDistPath, 'dist');

// 如果目标dist文件夹存在，则删除它
if (fs.existsSync(targetDistFullPath)) {
  console.log('删除已存在的目标 dist 文件夹...');
  try {
    // 递归删除目录
    fs.rmSync(targetDistFullPath, { recursive: true, force: true });
    console.log('目标 dist 文件夹已删除');
  } catch (err) {
    console.error('删除目标 dist 文件夹时出错:', err);
    process.exit(1);
  }
}

// 复制函数 - 递归复制目录及其内容
function copyFolderSync(from, to) {
  // 创建目标文件夹
  fs.mkdirSync(to, { recursive: true });

  // 读取源文件夹内容
  const items = fs.readdirSync(from);

  // 遍历每个文件/文件夹
  for (const item of items) {
    const srcPath = path.join(from, item);
    const destPath = path.join(to, item);
    const stat = fs.statSync(srcPath);

    // 如果是文件夹，则递归复制
    if (stat.isDirectory()) {
      copyFolderSync(srcPath, destPath);
    }
    // 如果是文件，则直接复制
    else {
      fs.copyFileSync(srcPath, destPath);
    }
  }
}

// 执行复制操作
try {
  console.log('开始复制 dist 文件夹...');
  copyFolderSync(sourceDistPath, targetDistFullPath);
  console.log(`复制完成! dist 文件夹已复制到 ${targetDistFullPath}`);
  console.log(`上一级目录为: ${path.dirname(targetDistFullPath)}`);
} catch (err) {
  console.error('复制过程中出错:', err);
  process.exit(1);
}
