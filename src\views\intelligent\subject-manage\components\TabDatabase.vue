<template>
  <div class="h-full p-5">
    <div class="flex h-full flex-col rounded-md bg-w pt-5">
      <div class="flex justify-between px-10">
        <div>
          <el-button type="primary" @click="onAdd"> 新增 </el-button>
          <el-button @click="onUpdate"> 立即更新 </el-button>
          <el-button @click="onStop"> 中止 </el-button>
        </div>
      </div>

      <div class="mt-3 h-0 w-full flex-1 px-10">
        <el-table
          height="100%"
          :data="tableData"
          style="width: 100%"
          class="c-table-header"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="name" label="数据库名称" />
          <el-table-column prop="desc" label="数据库说明" min-width="100px" />
          <el-table-column prop="frequency" label="更新频率" />
          <el-table-column label="建库语句">
            <template #default="{ row }">
              <el-button link type="primary">
                {{ row.statement }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80">
            <template #default="{ row }">
              <el-popconfirm title="确定删除此项？" @confirm="onDel(row)">
                <template #reference>
                  <el-button link type="primary"> 删除 </el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="pagination-bottom">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :page-size="pagination.pageSize"
          :total="tableData.length"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>

  <TabDatabaseDrawer v-model="showDrawer" @success="onSuccess" />
</template>

<script setup>
  import TabDatabaseDrawer from './TabDatabaseDrawer.vue';

  const showDrawer = ref(false);
  const onAdd = () => {
    showDrawer.value = true;
  };
  const onSuccess = () => {};
  const onUpdate = () => {};
  const onStop = () => {};

  //表格
  const tableData = ref([
    {
      id: 1,
      name: 'XXXXXX医院库1',
      desc: '说明说明说明说明说明',
      frequency: '每周',
      statement: 'XX医院库建库语句.doc',
    },
  ]);
  const checkList = ref([]);
  const handleSelectionChange = (val) => {
    checkList.value = val;
  };
  const onDel = (row) => {};

  //页码
  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  const handleCurrentChange = (e) => {
    pagination.page = e;
  };
  const handleSizeChange = (e) => {
    pagination.pageSize = e;
  };
</script>
