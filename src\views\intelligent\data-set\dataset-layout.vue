<template>
  <div class="flex h-full">
    <div class="aside bg-w">
      <asidemenu />
    </div>

    <div class="w-0 flex-1">
      <router-view />
    </div>
  </div>
</template>

<script setup>
  import asidemenu from '@/components/aside.vue';
  import { useRoute } from 'vue-router';
  let route = useRoute();

  let activeId = ref('');
  let menuList = ref([
    {
      id: '1',
      title: '数据集管理',
      svgname: 'icon-guanliyuan_guanliyuanrizhi',
      pathname: 'DatasetManage',
    },
    // {
    //   id: '2',
    //   title: '数据源管理',
    //   svgname: 'icon-shujuku1',
    //   pathname: 'DatabaseImport',
    // },
    {
      id: '2',
      title: '数据集疾病类型管理',
      svgname: 'icon-shujuku1',
      pathname: 'DatasetTypeMappingManage',
    },
  ]);

  watch(
    () => route.name,
    (val, old) => {
      menuList.value.forEach((e) => {
        if (e.pathname == val) {
          activeId.value = e.id;
        } else if (e.children) {
          let childrenItem = e.children.find((c) => c.pathname == val);
          activeId.value = childrenItem?.id;
        }
      });
    },
    {
      immediate: true,
    }
  );
  provide('datalist', menuList);
  provide('openid', activeId);
</script>

<style scoped lang="scss">
  .aside {
    width: 256px;
    height: 100%;
    box-shadow: 2px 0 12px 0 rgb(0 0 0 / 10%);
    z-index: 10;
  }
</style>
