<template>
  <div v-loading="loading" class="bg-bac h-full">
    <el-scrollbar height="100%">
      <div class="bg-w m-5 rounded-lg p-4">
        <el-descriptions>
          <template #title>
            <div class="flex cursor-pointer flex-wrap items-center" @click="router.back()">
              <el-icon size="20px"><Back /></el-icon>
              <div class="ml-2 break-words">{{ detailData.databaseName }}</div>
            </div>
          </template>
          <el-descriptions-item label="数据库类型">{{ detailData.databaseType }}</el-descriptions-item>
          <el-descriptions-item label="数据库驱动">{{ detailData.driverClassName }}</el-descriptions-item>
          <el-descriptions-item label="数据库地址">
            {{ detailData.address }}
          </el-descriptions-item>
          <el-descriptions-item label="端口号">
            {{ detailData?.port }}
          </el-descriptions-item>
          <el-descriptions-item label="数据库别名">{{ detailData?.aliasName || '' }}</el-descriptions-item>
          <el-descriptions-item label="数据库编码规则">{{ detailData?.characterEncoding || '' }}</el-descriptions-item>
          <el-descriptions-item label="测试连接字符串">{{
            detailData?.zeroDateTimeBehavior || ''
          }}</el-descriptions-item>
          <el-descriptions-item label="是否使用时区转换兼容规则">{{
            boolText(detailData?.useJDBCCompliantTimezoneShift)
          }}</el-descriptions-item>
          <el-descriptions-item label="是否使用本地时区">{{
            boolText(detailData?.useLegacyDatetimeCode)
          }}</el-descriptions-item>
          <el-descriptions-item label="useSSL">{{ boolText(detailData?.useSSL) }}</el-descriptions-item>
          <el-descriptions-item label="mysql时区">{{ detailData?.serverTimezone }}</el-descriptions-item>
          <el-descriptions-item label="生效开始时间">{{ detailData?.startDate }}</el-descriptions-item>
          <el-descriptions-item label="生效结束时间">{{ detailData?.endDate }}</el-descriptions-item>
          <el-descriptions-item label="备注">{{ detailData?.note }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="bg-w m-5 mb-0 rounded-md p-4">
        <div class="mb-4 flex gap-2">
          <el-select v-model="valueType" style="width: 100px" placeholder="字段类型" clearable @change="fetchData()">
            <el-option v-for="item in valueTypes" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>

          <el-select
            v-model="gendered"
            style="width: 160px"
            placeholder="是否按性别区分"
            clearable
            @change="fetchData()"
          >
            <el-option v-for="item in gendereds" :key="item.label" :label="item.label" :value="item.value"> </el-option>
          </el-select>

          <el-input
            v-model="searchVal"
            placeholder="请输入关键字搜索"
            style="width: 300px"
            clearable
            @clear="fetchData()"
            @keyup.enter="fetchData()"
          >
            <template #append>
              <el-button :icon="Search" @click="fetchData()" />
            </template>
          </el-input>
        </div>
        <el-table :data="tableData" style="width: 100%" class="c-table-header">
          <el-table-column prop="id" label="字段ID" width="100">
            <template #default="{ row }">
              <el-button link type="primary" @click="gotoFiled(row)">
                {{ row.id }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="字段名称" />
          <el-table-column prop="valueType" label="字段类型" />
          <el-table-column prop="baseInfo" label="基本信息" />
          <el-table-column label="是否按性别区分">
            <template #default="{ row }">
              <span>{{ row.gendered ? '是' : '否' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="remark" label="备注" min-width="200" />
          <el-table-column prop="id" label="更新时间" width="180">
            <template #default="{ row }">
              {{ row.rltTime.updateTime }}
            </template>
          </el-table-column>
          <el-table-column v-if="props.desensitization" label="是否脱敏" width="100">
            <template #default="{ row }">
              <el-switch
                v-model="row.needScrubbing"
                active-color="#13ce66"
                inactive-color="#ff4949"
                @change="updateNeedScrubbing(row)"
              ></el-switch>
            </template>
          </el-table-column>
          <el-table-column v-else label="操作" width="100">
            <template #default="{ row }">
              <el-button link type="primary" @click="gotoFiled(row)">查看详情</el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="mt-5 flex justify-center">
          <el-pagination
            background
            layout="total, prev, pager, next, jumper"
            :page-size="pagination.pageSize"
            :total="total"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
      <div class="h-5"></div>
    </el-scrollbar>
  </div>
</template>

<!-- 数据集数据字段详情 -->
<script setup lang="ts">
  import { findDBVOByDbId, findMedicalFieldsByFileInforIdAndDynamicConditions, newOrUpdateMedicalFieldVO } from '@/api';
  import { useRouter } from 'vue-router';
  import { Search } from '@element-plus/icons-vue';
  import { boolText } from '@/utils/format';
  import { MyDb } from './index.vue';

  const router = useRouter();
  interface Props {
    id: string;
    desensitization?: boolean;
  }
  const props = defineProps<Props>();
  const tableData = ref<MedicalFieldVO[]>([]);
  const loading = ref(false);
  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  const total = ref(0);
  const handleCurrentChange = (e) => {
    pagination.page = e;
    fetchData(e);
  };
  const detailData = ref<MyDb>({} as any);
  const searchVal = ref('');
  const valueType = ref('');
  const valueTypes = ref([
    { value: '文本', label: '文本' },
    { value: '整数', label: '整数' },
    { value: '实数', label: '实数' },
    { value: '日期时间', label: '日期时间' },
    { value: '组合', label: '组合' },
    { value: '单分类', label: '单分类' },
    { value: '多分类', label: '多分类' },
  ]);
  const gendered = ref<boolean | ''>('');
  const gendereds = ref([
    { value: true, label: '是' },
    { value: false, label: '否' },
  ]);

  async function fetchDetail() {
    try {
      loading.value = true;
      const { data } = await findDBVOByDbId(+props.id);
      detailData.value = data!;
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  async function fetchData(pageNum = 1) {
    try {
      loading.value = true;
      const { data } = await findMedicalFieldsByFileInforIdAndDynamicConditions(+props.id, {
        searchInput: searchVal.value,
        pageNum,
        pageSize: pagination.pageSize,
      });
      total.value = data?.totalElement || 0;
      pagination.page = pageNum;
      tableData.value = data?.content || [];
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  const updateNeedScrubbing = async (row: MedicalFieldVO) => {
    try {
      loading.value = true;
      await newOrUpdateMedicalFieldVO(row);
      fetchData(pagination.page);
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  };

  const gotoFiled = (row: any) => {
    router.push({
      name: 'DataSetFieldDetail',
      query: { fieldId: row.id },
    });
  };

  onBeforeMount(() => {
    fetchDetail();
    fetchData();
  });
</script>
