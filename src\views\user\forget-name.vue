<template>
  <div class="page">
    <h2 class="title">忘记账号</h2>

    <div class="box">
      <div v-if="!isValid" class="box-inner">
        <div class="box-tip">您可以通过验证账号绑定的邮箱找回账号</div>
        <el-form ref="formRef" size="large" :model="form" class="form" :rules="rules">
          <el-form-item prop="email">
            <el-input v-model="form.email" placeholder="请输入邮箱" maxlength="50" />
          </el-form-item>
          <el-form-item prop="emailCode">
            <div class="code-container">
              <el-input v-model="form.emailCode" class="code-input" placeholder="请输入邮箱验证码" maxlength="10" />
              <el-button plain @click="getCode"> 获取验证码 </el-button>
            </div>
          </el-form-item>
          <el-form-item>
            <el-button class="btn" color="#007f99" @click="onSubmit"> 下一步 </el-button>
          </el-form-item>
        </el-form>
        <div class="link" @click="gologin">
          <a>返回登录</a>
        </div>
      </div>

      <div v-else class="username-container">
        <img class="username-img" src="@/assets/img/data-resource/forgot-username.png" alt="" />
        <div class="tip">
          您的用户名为：<span class="username">{{ username }}</span>
        </div>
        <div class="link" @click="gologin">
          <a>前往登录</a>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { useRouter } from 'vue-router';
  import { ElMessage } from 'element-plus';
  const router = useRouter();

  let form = reactive({
    email: '',
    emailCode: '',
  });
  const isValid = ref(false);
  const username = ref('');

  const getCode = () => {
    console.log('获取验证码');
  };
  let gologin = () => {
    router.replace({ name: 'Login' });
  };

  const rules = {
    email: [
      { required: true, message: '请输入邮箱', trigger: 'blur' },
      {
        type: 'email',
        message: '请输入有效的邮箱地址',
        trigger: ['blur', 'change'],
      },
    ],
    emailCode: [{ required: true, message: '请输入邮箱验证码', trigger: 'blur' }],
  };

  let formRef = ref('');
  function onSubmit() {
    formRef.value.validate((valid) => {
      if (valid) {
        isValid.value = true;
        username.value = '张三';
      }
    });
  }
</script>

<style scoped lang="scss">
  .page {
    padding-top: 100px;
    padding-bottom: 20px;
    font-size: 14px;

    .title {
      text-align: center;
      font-size: 30px;
      font-weight: 700;
      line-height: 43px;
    }
  }

  .box {
    width: 1200px;
    border-radius: 8px;
    background: #ffffff;
    margin: 0 auto;
    margin-top: 55px;
    padding-top: 60px;
    padding-bottom: 34px;
    display: flex;
    flex-direction: column;
    align-items: center;

    .box-inner {
      width: 480px;
      text-align: center;
    }

    .box-tip {
      color: $color-tip-text;
      text-align: left;
      margin-bottom: 28px;
    }

    .form {
      .el-form-item {
        margin-bottom: 28px;
      }
    }

    .btn {
      margin-top: 20px;
      width: 100%;
    }

    .link {
      margin-top: 19px;
      color: $color-primary;
    }

    .code-container {
      width: 100%;
      display: flex;
      justify-content: space-between;

      .code-input {
        width: 362px;
      }
    }
  }

  .password-tip {
    color: $color-tip-text;
    font-size: 12px;

    .password-ul {
      margin-top: 8px;
      padding-left: 20px;
    }
  }

  .username-container {
    text-align: center;
    color: $color-tip-text;
    .username-img {
      width: 380px;
      height: 160px;
      margin-bottom: 40px;
    }

    .username {
      color: $color-main-text;
    }
  }
</style>
