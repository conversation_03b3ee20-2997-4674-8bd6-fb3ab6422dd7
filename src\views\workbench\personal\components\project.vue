<template>
  <div class="flex flex-col justify-start rounded-lg bg-w pb-10 pl-6 pr-10 pt-4">
    <div class="flex justify-between">
      <h4 class="text-lg font-bold">项目管理</h4>
      <div class="cursor-pointer text-xs" @click="onMore">
        <span class="mr-1 text-tip">查看更多</span>
        <el-icon :size="10">
          <ArrowRightBold color="#939899" :size="20" />
        </el-icon>
      </div>
    </div>

    <div class="mt-7 table">
      <el-table :data="tableData" class="c-table-header" height="100%">
        <el-table-column label="ID" prop="id" />
        <el-table-column label="项目名称" min-width="200px">
          <template #default="{ row }">
            <div class="flex items-center">
              <span>{{ row.title }}</span>
              <el-popover
                v-if="['审核未通过'].includes(row.state)"
                effect="light"
                placement="bottom-start"
                trigger="click"
                @show="fetchOpinion(row)"
              >
                <template #reference>
                  <div class="audit-state" :style="`color: #E64545;`">
                    审核意见
                    <el-icon><CaretBottom /></el-icon>
                  </div>
                </template>
                <template #default>
                  <div v-loading="opinionLoading" class="text-xs">
                    <div class="text-tip">审核意见：</div>
                    {{ opinion }}
                  </div>
                </template>
              </el-popover>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="申请时间">
          <template #default="{ row }">
            <span>{{ dayjs(row.createTime).format('YYYY-MM-DD') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="结束时间">
          <template #default="{ row }">
            <EndTimeDisplay :create-time="row.createTime" :duration="row.duration" />
          </template>
        </el-table-column>
        <el-table-column label="状态">
          <template #default="{ row }">
            <span class="status" :class="statusClass[row.state]">{{ row.state }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="240">
          <template #default="{ row }">
            <el-button link type="primary" @click="onView(row)"> 编辑 </el-button>
            <el-popconfirm title="确定提交申请？" @confirm="onSubmit(row)">
              <template #reference>
                <el-button link type="primary"> 提交申请 </el-button>
              </template>
            </el-popconfirm>
            <el-popconfirm title="确定删除此项？" @confirm="onDel(row)">
              <template #reference>
                <el-button link type="primary"> 删除 </el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { applyApplication, deleteApplicationById, findAllApplication, findVerifyResult_1 } from '@/api';
  import { ElMessage } from 'element-plus';
  import { useRouter } from 'vue-router';
  import { useUsers } from '@/store/index';
  import dayjs from 'dayjs';
  import EndTimeDisplay from '../../components/EndTimeDisplay.vue';

  const store = useUsers();
  const router = useRouter();
  const tableData = ref([]);
  const loading = ref(false);
  const statusClass = {
    待审批: 'status-yellow',
    审核通过: 'status-green',
    审核未通过: 'status-gray',
    3: 'status-blue',
  };

  //查看编辑
  const onView = (row) => {
    router.push({ name: 'PersonalProjectEdit', query: { id: row.id } });
  };

  async function fetchData() {
    try {
      loading.value = true;
      const { data } = await findAllApplication(1, 3);
      tableData.value = data?.content || ([] as any);
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  //提交申请
  const onSubmit = async (row) => {
    try {
      loading.value = true;
      await applyApplication(row.id, store.user.id);
      ElMessage({ type: 'success', message: '提交成功' });
      fetchData();
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  };

  //更多
  const onMore = () => {
    router.push({ name: 'PersonalProjectManage' });
  };

  const onDel = async (row) => {
    try {
      loading.value = true;
      await deleteApplicationById(row.id);
      ElMessage({ type: 'success', message: '删除成功' });
      fetchData();
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  };

  const opinion = ref('');
  const opinionLoading = ref(false);
  const fetchOpinion = async (row) => {
    try {
      opinionLoading.value = true;
      opinion.value = '';
      const { data } = await findVerifyResult_1(row.id);
      opinion.value = data?.description ?? '暂无';
    } catch (error) {
      console.log(error);
    } finally {
      opinionLoading.value = false;
    }
  };

  onBeforeMount(() => {
    fetchData();
  });
</script>

<style lang="scss" scoped>
  .status-text::before {
    content: '';
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 6px;
    margin-right: 6px;
    position: relative;
    top: -2px;
  }

  .status-to-start::before {
    background: #e6a117;
  }

  .status-under-way::before {
    background: #24b383;
  }

  .status-finished::before {
    background: #939899;
  }

  .audit-state {
    text-align: center;
    border-width: 1px;
    border-style: solid;
    border-radius: 12px;
    margin-left: 12px;
    font-size: 12px;
    height: 20px;
    line-height: 20px;
    padding: 0 8px;
    cursor: pointer;
  }
</style>
