<template>
  <div class="pl-10 pr-10 pt-4 text-sm">
    <h4 class="text-xl font-bold">数据平台权限审核</h4>

    <div class="mb-5 mt-6 flex justify-between border-b-[1px] border-solid border-[#e1e3e6] pb-5">
      <span>系统通知</span>
      <el-switch v-model="value1" inline-prompt active-text="开启" active-color="#007f99" inactive-text="关闭" />
    </div>

    <div class="v mb-5 flex justify-between border-b-[1px] border-solid border-[#e1e3e6] pb-5">
      <span>新闻动态</span>
      <el-switch v-model="value2" inline-prompt active-text="开启" active-color="#007f99" inactive-text="关闭" />
    </div>

    <div class="v mb-5 flex justify-between border-b-[1px] border-solid border-[#e1e3e6] pb-5">
      <span>系统通知系统通知系统通知</span>
      <el-switch v-model="value3" inline-prompt active-text="开启" active-color="#007f99" inactive-text="关闭" />
    </div>

    <div class="v mb-5 flex justify-between border-b-[1px] border-solid border-[#e1e3e6] pb-5">
      <span>新闻动态新闻动态</span>
      <el-switch v-model="value4" inline-prompt active-text="开启" active-color="#007f99" inactive-text="关闭" />
    </div>
  </div>
</template>

<script setup>
  /* 订阅 */
  let value1 = ref(true);
  let value2 = ref(false);
  let value3 = ref(true);
  let value4 = ref(false);
</script>
