<template>
  <div v-loading="loading" class="flex h-full flex-col gap-5 overflow-hidden p-5">
    <ul class="bg-w flex h-[156px] items-center justify-center rounded">
      <li
        v-for="item in overviewData"
        :key="item.id"
        class="border-border flex flex-1 justify-center border-r last-of-type:border-none"
      >
        <img class="mr-6 h-[60px] w-[60px]" :src="item.img" alt="" />
        <div>
          <span class="mr-3 text-[28px]">{{ item.number }}</span>
          <span>{{ item.unit }}</span>
          <div class="text-tip text-sm">
            {{ item.detail }}
          </div>
        </div>
      </li>
    </ul>

    <div class="grid h-0 flex-1 grid-cols-3 gap-5">
      <div class="bg-w h-full rounded py-3 pr-10 pl-6">
        <Chart v-if="pie" :option="pie" />
      </div>
      <div class="bg-w h-full rounded py-3 pr-10 pl-6">
        <Chart v-if="pie2" :option="pie2" />
      </div>
      <div class="bg-w h-full rounded py-3 pr-10 pl-6">
        <Chart v-if="barChartOptions" :option="barChartOptions" />
      </div>
    </div>

    <!-- <div class="w-460 ml-5 h-[656px] w-[460px] rounded bg-w p-10">
        <div v-for="item in asideMenu" :key="item.id" @click="handleAsideClick(item)">
          <div
            v-if="item.id === 4"
            class="aside-item-bg bg-op-50 flex h-[164px] w-[380px] cursor-pointer rounded-lg pl-10 pr-2 pt-10"
          >
            <div>
              <div class="text-xl font-bold text-m">数据集管理</div>
              <div class="mt-[29px] text-tip">数据集上传、维护</div>
            </div>
            <img src="@/assets/img/data-resource/data-set.png" alt="数据集" class="ml-auto" />
          </div>
          <img v-else :src="item.img" class="mb-[42px] h-[164px] w-[380px] cursor-pointer" />
        </div>
      </div> -->

    <div class="bg-w rounded pt-[14px] pr-10 pb-10 pl-6">
      <div class="flex justify-between text-lg">
        专题库
        <div class="text-tip flex cursor-pointer items-center text-sm" @click="handleMore">
          <span class="mr-1">查看更多</span>
          <el-icon><ArrowRight /></el-icon>
        </div>
      </div>

      <div class="mt-10 flex items-center">
        <div v-if="isPrevDisabled" class="mr-10 h-10 w-10" />
        <img v-else :src="imgb7" alt="" class="mr-10 h-10 w-10 cursor-pointer" @click="handlePrevSlide" />
        <div class="w-0 flex-1 overflow-hidden">
          <ul
            ref="listRef"
            class="flex gap-10 transition"
            :style="{ transform: 'translateX(' + maxSlideOffset + 'px)' }"
          >
            <li
              v-for="item in subjectList"
              :key="item.id"
              class="flex h-[180px] w-[240px] cursor-pointer flex-col items-center justify-center rounded-lg border-[#26A5BF] bg-[#F5F7FA] hover:border hover:bg-[#eef8fa]"
              @click="handleSubjectClick(item)"
            >
              <img :src="item.img" alt="" class="h-[44px] w-[51px]" />
              <div class="mt-[22px] text-lg">
                {{ item.detail }}
              </div>
            </li>
          </ul>
        </div>
        <div v-if="isNextDisabled" class="ml-10 h-10 w-10" />
        <img v-else :src="imgb8" alt="" class="ml-10 h-10 w-10 cursor-pointer" @click="handleNextSlide" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  // 导入依赖
  import { useRouter } from 'vue-router';
  import { cloneDeep, keys, map, sortBy } from 'lodash-es';
  import { fieldTypeText } from '@/utils/format';
  import {
    findByBictionaryCode,
    findFileInforByAnnotationId,
    findPlatStatistic,
    findTotalAmountByDictionaryCodeDisease,
    findTotalAmountByDictionaryCodeOrg,
  } from '@/api/index';
  import Chart from '@/components/Chart.vue';

  // 导入图片资源
  import img1 from '@/assets/img/number.png';
  import img2 from '@/assets/img/storage.png';
  import img3 from '@/assets/img/field.png';
  import imgb3 from '@/assets/img/bottom3.png';
  import imgb4 from '@/assets/img/bottom4.png';
  import imgb7 from '@/assets/img/left.png';
  import imgb8 from '@/assets/img/right.png';
  import aside1 from '@/assets/img/aside1.png';
  import aside3 from '@/assets/img/aside3.png';
  import { ElMessage } from 'element-plus';

  // 路由实例
  const router = useRouter();

  // 加载状态
  const loading = ref(false);

  // 数据概览配置
  const overviewData = reactive([
    {
      id: 1,
      img: img1,
      number: 0,
      unit: '种',
      detail: '疾病种类',
    },
    {
      id: 2,
      img: img2,
      number: 0,
      unit: '例',
      detail: '病例数量',
    },
    {
      id: 3,
      img: img3,
      number: 0,
      unit: '条',
      detail: '字段数量',
    },
  ]);

  // 侧边栏配置
  const asideMenu = reactive([
    {
      id: 1,
      img: aside1,
      pathname: 'IntelligentRegister',
    },
    {
      id: 3,
      img: aside3,
      pathname: 'DesensitizationManage',
    },
    {
      id: 4,
      pathname: 'DatasetManage',
    },
  ]);

  // 饼图配置
  let option = {
    title: {
      text: '',
    },
    tooltip: {
      trigger: 'item',
    },
    series: {
      type: 'pie',
      radius: '50%',
      data: [] as { value: number; name: string }[],
      label: {
        color: '#939899',
        formatter: '{b} \n\r{@[]}({d}%)',
      },
    },
  };
  const pie = ref<any>({});
  const pie2 = ref<any>({});
  //文件数量饼图
  function setFileChart(data: any[]) {
    let newOption = cloneDeep(option);
    newOption.title!.text = '文件数量';
    newOption.series.data = data.map((item) => {
      item.name = item.title;
      item.value = item.amount;
      return item;
    });
    pie.value = newOption;
  }
  //疾病类型饼图
  function setIllnessChart(data: any[]) {
    let newOption = cloneDeep(option);
    newOption.title!.text = '疾病类型';
    newOption.series.data = data.map((item) => {
      item.name = item.title;
      item.value = item.amount;
      return item;
    });
    pie2.value = newOption;
  }
  async function fetchChartData() {
    const [res1, res2] = await Promise.all([
      findTotalAmountByDictionaryCodeOrg(),
      findTotalAmountByDictionaryCodeDisease(),
    ]);
    setFileChart(res1.data!);
    setIllnessChart(res2.data!);
    overviewData[0].number = res2.data?.length || 0;
  }

  // 柱状图配置
  const barChartOptions = ref({
    title: {
      text: '数据更新状态',
      left: 'left',
      top: 0,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    grid: {
      top: '15%',
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: [] as string[],
      axisTick: {
        show: false,
      },
      axisLine: {
        lineStyle: {
          color: '#ccc',
        },
      },
      axisLabel: {
        color: '#565b5c',
        interval: 0,
        rotate: 30,
      },
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        color: '#939899',
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
        },
      },
    },
    series: {
      name: '更新数量',
      type: 'bar',
      barWidth: '50%',
      data: [] as number[],
      color: '#1e9bb6',
      itemStyle: {
        borderRadius: [4, 4, 0, 0],
      },
      label: {
        show: true,
        position: 'top',
      },
    },
  });

  // 获取概览数据
  async function fetchOverview() {
    try {
      loading.value = true;
      const { data } = await findPlatStatistic();
      overviewData[1].number = data?.caseCount || 0;
      overviewData[2].number = data?.medicalFieldCount || 0;
      if (data?.updateDistribution) {
        const sortedKeys = sortBy(keys(data.updateDistribution), (key) => Number(key));
        barChartOptions.value.xAxis.data = sortedKeys;
        barChartOptions.value.series.data = sortedKeys.map((key) => Number(data!.updateDistribution![key]));
      } else {
        barChartOptions.value.xAxis.data = [];
        barChartOptions.value.series.data = [];
      }
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  // 侧边栏点击事件
  const handleAsideClick = (e) => {
    router.push({ name: e.pathname });
  };

  // 专题库配置
  const subjectList = reactive([
    {
      id: 3,
      img: imgb4,
      detail: '抑郁症数据库',
      value: '抑郁症',
    },
    {
      id: 4,
      img: imgb3,
      detail: '痴呆数据库',
      value: '自然人-痴呆',
    },
  ]);

  // 专题库相关配置和方法
  const handleMore = () => {};
  const slideItemWidth = 280;
  const slideOffset = ref(0);
  const slideListRef = ref();
  const maxSlideOffset = ref(0);

  // 滑动按钮状态
  const isPrevDisabled = computed(() => {
    return slideOffset.value >= 0;
  });
  const isNextDisabled = computed(() => {
    return slideOffset.value <= maxSlideOffset.value;
  });

  // 滑动方法
  const handlePrevSlide = () => {
    slideOffset.value += slideItemWidth;
  };
  const handleNextSlide = () => {
    slideOffset.value += -slideItemWidth;
  };

  // 专题库点击事件
  const requestLoading = ref(false);
  const handleSubjectClick = async (item) => {
    if (item.id === 4) {
      router.push({ name: 'ResourceTopic', query: { tag: item.value } });
      // if (requestLoading.value) return;
      // requestLoading.value = true;
      // try {
      //   const { data } = await findByBictionaryCode(1, 999, { dictionaryCode: 'TYPE_DISEASE' } as any);
      //   const findItem = data?.content?.find((e) => e.title === '自然人-痴呆');
      //   const res = await findFileInforByAnnotationId({
      //     annotationIDList: [findItem!.id!],
      //     pageNum: 1,
      //     pageSize: 1,
      //   });
      //   router.push({ name: 'DataSetField', params: { id: res.data!.content![0].id } });
      // } finally {
      //   requestLoading.value = false;
      // }
    } else {
      ElMessage.warning(item.detail + '不存在');
    }
  };

  // 计算最大滑动距离
  const calculateMaxSlideOffset = () => {
    maxSlideOffset.value = slideListRef.value ? -(slideListRef.value.scrollWidth - slideListRef.value.clientWidth) : 0;
  };

  // 生命周期钩子
  onMounted(() => {
    fetchChartData();
    fetchOverview();
    calculateMaxSlideOffset();
    window.addEventListener('resize', calculateMaxSlideOffset);
  });

  onUnmounted(() => {
    window.removeEventListener('resize', calculateMaxSlideOffset);
  });
</script>

<style lang="scss" scoped>
  .aside-item-bg {
    background: linear-gradient(140deg, rgba(0, 90, 179, 0.2) 0%, rgba(0, 128, 255, 0.1) 100%);
  }
</style>
