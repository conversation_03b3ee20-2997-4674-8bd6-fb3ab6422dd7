/*
 * @Description:工作台-个人用户
 */

export default {
  path: '/personal',
  redirect: 'personal/index',
  component: () => import('@/views/logged-layout/index.vue'),
  children: [
    //首页
    {
      path: 'index',
      name: 'Personal',
      component: () => import('@/views/workbench/personal/index.vue'),
    },
    {
      path: 'menu',
      component: () => import('@/views/workbench/personal/person-menu.vue'),
      children: [
        //项目管理
        {
          path: 'project-manage',
          name: 'PersonalProjectManage',
          component: () => import('@/views/workbench/personal/project/project-manage.vue'),
        },
        //项目申请
        {
          path: 'project-application',
          name: 'PersonalProjectApplication',
          component: () => import('@/views/workbench/personal/project/project-application/project-application.vue'),
        },
        //项目编辑
        {
          path: 'project-edit',
          name: 'PersonalProjectEdit',
          component: () => import('@/views/workbench/personal/project/project-application/project-edit.vue'),
          props: (route) => ({ id: route.query.id }),
        },
        //项目订单数据选择
        {
          path: 'project-order-select',
          name: 'PersonalProjectOrderSelect',
          component: () => import('@/views/workbench/personal/project/order-data/data-layout.vue'),
          props: (route) => ({ orderId: route.query.orderId, code: route.query.code }),
        },
      ],
    },
  ],
};
