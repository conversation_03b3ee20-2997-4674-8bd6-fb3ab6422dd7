<template>
  <el-collapse v-model="activeNames" accordion @change="onChange">
    <el-collapse-item v-for="(item, index) in order" :key="item.id" :name="index">
      <template #title>
        <div class="flex w-full items-center justify-between pr-2">
          <div class="flex items-center">
            <el-icon v-if="activeNames !== index" color="#B4B6B8">
              <CaretRight />
            </el-icon>
            <el-icon v-else color="#007f99">
              <CaretBottom />
            </el-icon>
            <span class="ml-2 font-bold">订单编号 {{ item.code }}</span>
            <el-link v-if="!readonly" type="primary" class="ml-10" :underline="false" @click.stop="onAddData(item)">
              添加数据
            </el-link>
          </div>
          <el-popconfirm v-if="!readonly" title="确定删除？" @confirm="onDelOrder(item.id)">
            <template #reference>
              <div class="flex cursor-pointer items-center text-sm text-regular" @click.stop="() => {}">
                <el-icon><Delete /></el-icon>
                <span class="ml-2">删除订单</span>
              </div>
            </template>
          </el-popconfirm>
        </div>
      </template>

      <div class="desc bg-baf p-3 pb-0">
        <el-descriptions :column="2">
          <el-descriptions-item label="创建日期"> {{ item.rltTime?.updateTime }} </el-descriptions-item>
          <el-descriptions-item label="状态"> {{ item.state || '未批准' }} </el-descriptions-item>
          <!-- <el-descriptions-item label="快照提取ID"> Suzhou </el-descriptions-item>
          <el-descriptions-item label="快照中的项目数"> 808 </el-descriptions-item>
          <el-descriptions-item label="快照最后更新时间"> 2023年12月29日 </el-descriptions-item> -->
          <!-- <el-descriptions-item label="订单包含">
            {{ statistic.dataSetCount || 0 }}个数据集，{{ statistic.medicalFieldCount || 0 }}个字段（文本类型{{
              statistic.txtFieldCount || 0
            }}个，整数类型{{ statistic.longFieldCount || 0 }}个，实数类型{{
              statistic.floatFieldCount || 0
            }}个，日期类型{{ statistic.dateTimeFieldCount || 0 }}个，分类类别{{
              statistic.categoricalFieldCount || 0
            }}个）
          </el-descriptions-item> -->
        </el-descriptions>
      </div>

      <div class="mt-4">
        <!-- <el-collapse v-model="currentCollapse" accordion>
          <el-collapse-item name="1">
            <template #title>
              <div class="flex w-full items-center justify-between pr-2">
                <div class="flex items-center">
                  <el-icon v-if="currentCollapse !== '1'" color="#B4B6B8">
                    <CaretRight />
                  </el-icon>
                  <el-icon v-else color="#B4B6B8">
                    <CaretBottom />
                  </el-icon>
                  <span class="ml-2">脑疾病</span>
                </div>
                <el-popconfirm v-if="!readonly" title="确定清空？" @confirm="onClear">
                  <template #reference>
                    <div
                      v-show="currentCollapse === '1'"
                      class="flex cursor-pointer items-center text-sm text-regular"
                      @click.stop="() => {}"
                    >
                      <i class="iconfont icon-clear text-regular" style="font-size: 14px" />
                      <span class="ml-2">清空所选</span>
                    </div>
                  </template>
                </el-popconfirm>
              </div>
            </template>
            <el-table class="c-table c-table-header" :data="tableData" style="width: 100%">
              <el-table-column prop="fieldId" label="字段ID" />
              <el-table-column prop="name" label="名称" />
              <el-table-column v-if="!readonly" label="操作">
                <template #default="{ row }">
                  <el-popconfirm title="确定删除此项？" @confirm="onDelField(row)">
                    <template #reference>
                      <el-button link type="primary"> 删除 </el-button>
                    </template>
                  </el-popconfirm>
                </template>
              </el-table-column>
            </el-table>
          </el-collapse-item>
        </el-collapse> -->
        <el-table v-loading="loading" class="c-table c-table-header" :data="tableData" style="width: 100%">
          <el-table-column type="index" label="序号" width="80" />
          <el-table-column prop="tableName" label="量表英文名称" />
          <el-table-column prop="tableChineseName" label="量表中文名称" />
          <el-table-column v-if="!readonly" label="操作" width="100">
            <template #default="{ row }">
              <el-popconfirm title="确定删除此项？" @confirm="onDelField(row)">
                <template #reference>
                  <el-button link type="primary"> 删除 </el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-collapse-item>
  </el-collapse>
</template>

<script setup lang="ts">
  /* 数据字段 */
  import { deleteOrderById, deleteOrderItemById, findOrderItemVOByOrderId } from '@/api/index';
  import { useDataBrowse } from '@/store/data-browse';
  import { ElMessage } from 'element-plus';
  import { isNumber } from 'lodash-es';
  import { useRouter } from 'vue-router';

  const router = useRouter();
  const store = useDataBrowse();
  interface Props {
    order: OrderVO[];
    readonly?: boolean;
  }
  const props = withDefaults(defineProps<Props>(), {
    order: () => {
      return [];
    },
  });
  const activeNames = ref(-1);
  const emit = defineEmits(['delete']);
  const loading = ref(false);
  const tableData = ref<OrderItemVO[]>([]);
  // const statistic = ref<OrderStatistic>({});

  async function fetchDetail() {
    try {
      loading.value = true;
      const [{ data }] = await Promise.all([
        findOrderItemVOByOrderId(props.order[activeNames.value].id!),
        // getOrderStatistic(props.order[activeNames.value].id!),
      ]);
      tableData.value = data || [];
      // statistic.value = data2 || {};
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  //删除订单
  const onDelOrder = async (id: number | undefined) => {
    if (!id) return;
    // orderTable.value.splice(index, 1);
    try {
      await deleteOrderById([id]);
      activeNames.value = -1;
      emit('delete');
    } catch (error) {
      console.log('🚀 ~ onDelOrder ~ error:', error);
    }
  };

  const onChange = (activeNames: number) => {
    if (isNumber(activeNames)) {
      fetchDetail();
    }
  };

  //添加数据
  const onAddData = (item: OrderVO) => {
    store.setData({ isSelectMode: true });
    router.push({ name: 'PersonalProjectOrderSelect', query: { orderId: item.id, code: item.code } });
  };
  const currentCollapse = ref('');
  //清空所选
  const onClear = () => {};
  //删除字段
  const onDelField = async (row: OrderItemVO) => {
    await deleteOrderItemById([row.orderItemId!]);
    fetchDetail();
    ElMessage({ type: 'success', message: '删除成功' });
  };
</script>

<style lang="scss" scoped>
  .desc {
    --el-fill-color-blank: #f7f9fc;

    :deep(.el-descriptions__label) {
      color: $color-tip-text;
    }

    :deep(.el-descriptions__content) {
      color: $color-main-text;
    }
  }

  .el-collapse {
    --el-border-color-lighter: transparent;
    :deep(.el-collapse-item__arrow) {
      display: none;
    }
  }

  .c-table {
    --el-border-color-lighter: #ebeef5;
  }

  :deep(.el-table .el-table__cell) {
    padding: 8px 0;
  }
</style>
