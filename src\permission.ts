import router from './router/index';
import { useUsers, useRouteStore } from '@/store/index';
import { getRoutesByRoleCode } from '@/utils/business';

// 白名单
const whiteList = ['/user', '/protocol', '/data-resource'];

router.beforeEach(async (to) => {
  const userStore = useUsers();
  const routeStore = useRouteStore();
  const hasToken = userStore.isLogin;
  if (hasToken) {
    //判断token是否存在 存在即为已经登录
    if (to.path !== '/user/login') {
      if (routeStore.init) {
        // 获取了动态路由 init一定true,就无需再次请求 直接放行
        return true;
      } else {
        // init为false,一定没有获取动态路由,就跳转到获取动态路由的方法
        const accessRoutes = getRoutesByRoleCode(userStore.user.roleCode); //解析路由,存储路由
        // 动态挂载路由
        accessRoutes.forEach((route) => {
          router.addRoute(route);
        });
        routeStore.init = true; //init改为true,路由初始化完成
        return { ...to, replace: true }; // hack方法 确保addRoute已完成
      }
    } else {
      return '/';
    }
  } else {
    // 白名单，直接放行
    if (to.path === '/') return true;
    if (whiteList.some((item) => to.path.startsWith(item))) return true;
    // 非白名单，去登录
    return '/user/login';
  }
});
