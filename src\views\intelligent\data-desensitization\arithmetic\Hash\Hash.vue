<template>
  <div class="flex h-full flex-col px-5 pb-5">
    <p class="flex items-center py-3 text-tip">
      <el-icon><InfoFilled /></el-icon>
      <span class="ml-2">
        将字符串类型字段用Hash值代替。在关系型数据库中，当该字段长度小于Hash长度时，会将目标库中该字段的长度与Hash值长度设置相同，保证Hash值完整
      </span>
    </p>

    <div class="flex h-0 flex-1 flex-col rounded bg-w pt-5">
      <div class="mt-3 h-0 w-full flex-1 px-10">
        <el-table
          height="100%"
          :data="tableData"
          style="width: 100%"
          class="c-table-header"
          @selection-change="handleSelectionChange"
        >
          <el-table-column prop="name" label="算法名" align="center" />
          <el-table-column prop="type" label="类型" align="center" />
          <el-table-column label="操作" width="120">
            <template #default="{ row }">
              <el-button link type="primary" @click="onTest(row)"> 测试 </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="pagination-bottom">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :page-size="pagination.pageSize"
          :total="tableData.length"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
  //表格
  const tableData = ref([
    {
      id: '1',
      name: 'SHA256',
      type: '内置',
    },
    {
      id: '2',
      name: 'SHA512',
      type: '内置',
    },
  ]);
  const checkList = ref([]);
  const handleSelectionChange = (val) => {
    checkList.value = val;
  };

  //页码
  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  const handleCurrentChange = (e) => {
    pagination.page = e;
  };
  const handleSizeChange = (e) => {
    pagination.pageSize = e;
  };

  const showDrawer = ref(false);
  const readonly = ref(false);
  let drawerData = ref({});
  const onView = (row) => {
    drawerData.value = row;
    readonly.value = true;
    showDrawer.value = true;
  };
  const onAdd = () => {
    drawerData.value = {};
    readonly.value = false;
    showDrawer.value = true;
  };
  const onDel = (row) => {};
  const onEdit = (row) => {
    drawerData.value = row;
    readonly.value = false;
    showDrawer.value = true;
  };
  const onTest = (row) => {};
  const onSuccess = () => {};
</script>

<style lang="scss" scoped>
  .tabs {
    :deep(.el-tabs__header) {
      margin: 0;
    }
  }
</style>
