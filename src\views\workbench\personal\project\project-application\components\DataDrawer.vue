<template>
  <el-drawer v-model="drawer" class="relative" @close="onCancel">
    <template #header>
      <h4 class="text-m">订单编号 {{ order }}</h4>
    </template>

    <div class="bg-baf p-4">
      <el-descriptions direction="vertical" :column="2">
        <el-descriptions-item label="创建日期"> 2022-07-15 </el-descriptions-item>
        <el-descriptions-item label="状态">
          <span class="status" :class="getStatusClass(data.status)">未获批准</span>
        </el-descriptions-item>
        <el-descriptions-item label="快照提取ID"> 38183 </el-descriptions-item>
        <el-descriptions-item label="快照中的项目数"> 808 </el-descriptions-item>
        <el-descriptions-item label="快照最后更新时间" :span="2"> 2022-08-15 </el-descriptions-item>
        <el-descriptions-item label="订单包含">
          789个标准字段（789个表格、0个遗传SNP、0个数据集）和19个批量字段（19个批量、0个HES记录）
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <el-collapse v-model="currentCollapse" accordion>
      <el-collapse-item name="1">
        <template #title>
          <div class="flex w-full items-center justify-between pr-2">
            <div class="flex items-center">
              <el-icon v-if="currentCollapse !== '1'" color="#B4B6B8">
                <CaretRight />
              </el-icon>
              <el-icon v-else color="#B4B6B8">
                <CaretBottom />
              </el-icon>
              <span class="ml-2">传染性疾病</span>
            </div>
          </div>
        </template>
        <el-table class="c-table c-table-header" :data="tableData" style="width: 100%">
          <el-table-column prop="fieldId" label="字段ID" />
          <el-table-column prop="name" label="名称" />
        </el-table>
      </el-collapse-item>
    </el-collapse>
  </el-drawer>
</template>

<script setup>
  /*上传成果*/
  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
      required: true,
    },
    order: {
      type: String,
      required: true,
    },
  });
  const drawer = ref(false);
  const data = ref({ status: 0 });
  const tableData = ref([{ fieldId: '23050', name: '单纯抱疹病毒1的HSV-1血清阳性' }]);
  for (let i = 0; i < 20; i++) {
    tableData.value.push({
      fieldId: '2305' + (i + 1),
      name: `单纯抱疹病毒1的HSV-${i + 2}血清阳性`,
    });
  }

  const getStatusClass = (status) => {
    let className = '';
    switch (status) {
      case 0:
        className = 'status--warn';
        break;
      case 1:
        className = 'status--sucess';
        break;
      case 2:
        className = 'status--done';
        break;
    }
    return className;
  };
  const currentCollapse = ref('');

  const emit = defineEmits(['update:modelValue', 'success']);
  watchEffect(() => {
    drawer.value = props.modelValue;
  });
  const onCancel = () => {
    emit('update:modelValue', false);
  };
</script>

<style lang="scss" scoped>
  :deep(.el-descriptions) {
    .el-descriptions__body {
      background-color: transparent;
    }
    .el-descriptions__label {
      color: $color-tip-text;
    }
  }
  :deep(.el-table .el-table__cell) {
    padding: 8px 0;
  }
  .c-table {
    --el-border-color-lighter: #ebeef5;
  }
  .el-collapse {
    --el-border-color-lighter: transparent;
    :deep(.el-collapse-item__arrow) {
      display: none;
    }
  }

  .status::before {
    content: '';
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 6px;
    margin-right: 6px;
    position: relative;
    top: -2px;
  }
  .status--warn::before {
    background: #e6a117;
  }
  .status--sucess::before {
    background: #24b383;
  }
  .status--done::before {
    background: #939899;
  }
</style>
