<template>
  <div class="flex h-full flex-col overflow-hidden">
    <div class="bg-w pl-5">
      <h2 class="flex h-[60px] items-center text-xl font-bold">脱敏算法管理</h2>
      <el-tabs v-model="activeName" class="tabs">
        <el-tab-pane v-for="(item, index) in tabs" :key="index" :label="item.label" :name="item.name" />
      </el-tabs>
    </div>

    <div class="h-0 flex-1">
      <CharacterMask v-if="activeName === '字符遮盖脱敏'" />
      <KeywordReplacement v-if="activeName === '关键字替换'" />
      <RoundOff v-if="activeName === '取整脱敏'" />
      <AlphabeticShift v-if="activeName === '字符移位'" />
      <Encrypt v-if="activeName === '加密脱敏'" />
      <Hash v-if="activeName === 'Hash脱敏'" />
      <SQLCustom v-if="activeName === 'SQL自定义脱敏'" />
    </div>
  </div>
</template>

<script setup>
  import CharacterMask from './CharacterMask/CharacterMask.vue';
  import KeywordReplacement from './KeywordReplacement/KeywordReplacement.vue';
  import RoundOff from './RoundOff/RoundOff.vue';
  import AlphabeticShift from './AlphabeticShift/AlphabeticShift.vue';
  import Encrypt from './Encrypt/Encrypt.vue';
  import Hash from './Hash/Hash.vue';
  import SQLCustom from './SQLCustom/SQLCustom.vue';

  const activeName = ref('字符遮盖脱敏');
  const tabs = [
    {
      name: '字符遮盖脱敏',
      label: '字符遮盖脱敏',
    },
    {
      name: '关键字替换',
      label: '关键字替换',
    },
    {
      name: '取整脱敏',
      label: '取整脱敏',
    },
    {
      name: '字符移位',
      label: '字符移位',
    },
    {
      name: '加密脱敏',
      label: '加密脱敏',
    },
    {
      name: 'Hash脱敏',
      label: 'Hash脱敏',
    },
    {
      name: 'SQL自定义脱敏',
      label: 'SQL自定义脱敏',
    },
  ];
</script>

<style lang="scss" scoped>
  .tabs {
    :deep(.el-tabs__header) {
      margin: 0;
    }
  }
</style>
