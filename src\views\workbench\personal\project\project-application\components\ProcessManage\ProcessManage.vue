<template>
  <div class="h-full p-5">
    <div class="flex h-full rounded bg-w">
      <div class="h-full w-[240px]">
        <el-menu
          :default-active="activeIndex"
          active-text-color="#007f99"
          class="custom-menu h-full w-full"
          @select="onChangeMenu"
        >
          <el-menu-item index="1">
            <span>项目报告</span>
          </el-menu-item>
          <el-menu-item index="2">
            <span>发表刊物</span>
          </el-menu-item>
          <el-menu-item index="3">
            <span>会议报告</span>
          </el-menu-item>
          <el-menu-item index="4">
            <span>项目成果</span>
          </el-menu-item>
        </el-menu>
      </div>

      <div class="h-full w-0 flex-1">
        <ProcessProject v-if="activeIndex === '1'" />
        <ProcessPeriodical v-if="activeIndex === '2'" />
        <ProcessMeeting v-if="activeIndex === '3'" />
        <ProcessAchievement v-if="activeIndex === '4'" />
      </div>
    </div>
  </div>
</template>

<script setup>
  /* 过程管理 */
  import ProcessProject from './ProcessProject.vue';
  import ProcessPeriodical from './ProcessPeriodical.vue';
  import ProcessMeeting from './ProcessMeeting.vue';
  import ProcessAchievement from './ProcessAchievement.vue';
  const props = defineProps({
    state: {
      type: String,
      default: '',
    },
  });

  const activeIndex = ref('1');
  const onChangeMenu = (key, keyPath) => {
    activeIndex.value = key;
  };
</script>

<style lang="scss" scoped>
  .custom-menu {
    padding-top: 20px;
    --el-menu-item-height: 40px;

    .is-active {
      background-image: url('@/assets/img/workbench/menu-selected.png');
      border-right: 2px solid $color-primary;
    }

    .el-menu-item {
      margin-bottom: 8px;
    }
  }
</style>
