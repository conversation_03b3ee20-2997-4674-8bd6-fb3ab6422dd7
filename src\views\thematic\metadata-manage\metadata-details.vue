<template>
  <div class="flex h-full flex-col">
    <h2 class="flex h-[60px] cursor-pointer items-center bg-w pl-5 text-xl font-semibold" @click="onBack">
      <el-icon class="mr-2" color="#939899">
        <ArrowLeft />
      </el-icon>
      元数据详情
    </h2>

    <div class="flex h-0 flex-1 p-5">
      <DetailsLeft />
      <DetailsRight />
    </div>
  </div>
</template>

<script setup>
  import DetailsRight from './components/DetailsRight.vue';
  import DetailsLeft from './components/DetailsLeft.vue';
  import { useRouter } from 'vue-router';
  let router = useRouter();
  const onBack = () => {
    router.back();
  };
</script>

<style lang="scss" scoped>
  :deep(.c-steps) {
    .el-step__line {
      background-color: #e1e3e6;
    }
    .el-step__icon {
      height: 20px;
    }
  }
</style>
