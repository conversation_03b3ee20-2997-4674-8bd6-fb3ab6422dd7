<template>
  <div class="flex h-full p-5">
    <CustomerAside />
    <div class="flex w-0 flex-1 flex-col">
      <div class="h-[110px] rounded bg-w">
        <el-tabs v-model="activeName" class="service-tabs" stretch>
          <el-tab-pane name="用户">
            <template #label>
              <div class="flex items-center justify-center">
                <div class="flex h-[56px] w-[56px] items-center justify-center rounded bg-[#f0f2f5]">
                  <el-icon color="#409EFF" :size="24">
                    <User />
                  </el-icon>
                </div>
                <div class="ml-5">
                  <div class="text-left text-m">
                    <!-- <span class="text-2xl font-bold">5</span>
                    <span class="ml-1 text-sm">条</span> -->
                    <span class="text-2xl">新用户注册申请</span>
                  </div>
                  <!-- <div class="text-sm text-tip">新用户注册申请</div> -->
                </div>
              </div>
              <div class="absolute right-0 h-[80px] w-[1px] bg-border" />
            </template>
          </el-tab-pane>
          <el-tab-pane name="机构">
            <template #label>
              <div class="flex items-center justify-center">
                <div class="flex h-[56px] w-[56px] items-center justify-center rounded bg-[#e5f2f4]">
                  <img class="h-[24px] w-[24px]" src="@/assets/img/business/org-register.png" alt="" />
                </div>
                <div class="ml-5">
                  <div class="text-left text-m">
                    <span class="text-2xl">新机构注册申请</span>
                    <!-- <span class="text-2xl font-bold">12</span>
                    <span class="ml-1 text-sm">条</span> -->
                  </div>
                  <!-- <div class="text-sm text-tip">新机构注册申请</div> -->
                </div>
              </div>
              <div class="absolute right-0 h-[80px] w-[1px] bg-border" />
            </template>
          </el-tab-pane>
          <el-tab-pane name="项目">
            <template #label>
              <div class="flex items-center justify-center">
                <div class="flex h-[56px] w-[56px] items-center justify-center rounded bg-[#eef3fc]">
                  <img class="h-[24px] w-[24px]" src="@/assets/img/business/project-register.png" alt="" />
                </div>
                <div class="ml-5">
                  <div class="text-left text-m">
                    <span class="text-2xl">项目申请</span>
                    <!-- <span class="text-2xl font-bold">12</span>
                    <span class="ml-1 text-sm">条</span> -->
                  </div>
                  <!-- <div class="text-sm text-tip">项目申请</div> -->
                </div>
              </div>
            </template>
          </el-tab-pane>
        </el-tabs>
      </div>

      <CustomerUser v-if="activeName === '用户'" />
      <CustomerOrg2 v-if="activeName === '机构'" />
      <CustomerProject v-if="activeName === '项目'" />
      <!-- <div v-if="activeName === '项目'" class="flex justify-center pt-10">开发中</div> -->
    </div>
  </div>
</template>

<script setup>
  import CustomerUser from './components/CustomerUser.vue';
  import CustomerOrg2 from './components/CustomerOrg2.vue';
  import CustomerProject from './components/CustomerProject.vue';
  import CustomerAside from './components/CustomerAside.vue';

  const activeName = ref('用户');
</script>

<style lang="scss" scoped>
  :deep(.service-tabs) {
    .el-tabs__item {
      height: 110px;
      padding: 0;
    }
    .el-tabs__active-bar {
      height: 4px;
    }
  }
</style>
