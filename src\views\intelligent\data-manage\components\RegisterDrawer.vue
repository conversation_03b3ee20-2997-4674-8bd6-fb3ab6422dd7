<template>
  <el-drawer v-model="drawer" size="60%" @close="onCancel">
    <template #header>
      <h4 class="text-m">{{ action }}目录</h4>
    </template>

    <el-form
      ref="formRef"
      v-loading="loading"
      :model="form"
      :rules="rules"
      label-position="top"
      :hide-required-asterisk="readonly"
      :class="{ readonly: readonly }"
    >
      <div class="mb-4 flex w-full items-center">
        <div class="h-[13px] w-[3px] bg-p" />
        <span class="ml-2">目录分类</span>
      </div>
      <div class="text-sm" :class="{ 'text-tip': readonly }">
        目录分类：<span class="text-regular" :class="{ 'text-m': readonly }">{{ data.parentName }}</span>
      </div>

      <div class="mb-4 mt-8 flex w-full items-center">
        <div class="h-[13px] w-[3px] bg-p" />
        <span class="ml-2">目录信息</span>
      </div>
      <el-form-item label="目录名称" prop="title">
        <div v-if="readonly">
          {{ form.title }}
        </div>
        <el-input v-else v-model="form.title" placeholder="请输入目录名称" maxlength="50" />
      </el-form-item>
      <el-form-item label="目录分类" prop="type">
        <div v-if="readonly">
          {{ form.type }}
        </div>
        <el-select v-else v-model="form.type" placeholder="请选择目录分类" style="width: 100%">
          <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="目录摘要" prop="description">
        <div v-if="readonly">
          {{ form.description }}
        </div>
        <el-input
          v-else
          v-model="form.description"
          placeholder="请输入目录摘要"
          maxlength="300"
          type="textarea"
          :rows="4"
          show-word-limit
        />
      </el-form-item>
      <el-form-item label="备注" prop="notes">
        <div v-if="readonly">
          {{ form.notes }}
        </div>
        <el-input
          v-else
          v-model="form.notes"
          placeholder="请输入备注"
          maxlength="500"
          type="textarea"
          :rows="4"
          show-word-limit
        />
      </el-form-item>
      <!-- <el-form-item label="目录状态" prop="state">
        <span v-if="readonly" class="status" :class="statusClass[form.state]">{{ statusText[form.state] }}</span>
        <el-input v-else placeholder="请输入目录状态" v-model="form.state" maxlength="50" />
      </el-form-item> -->
    </el-form>

    <template v-if="tableData.length">
      <div class="mb-4 mt-10 flex w-full items-center">
        <div class="h-[13px] w-[3px] bg-p" />
        <span class="ml-2">字段信息</span>
      </div>
      <el-table :data="tableData" style="width: 100%" class="c-table-header mt-4">
        <el-table-column type="index" width="60" label="序号" />
        <el-table-column prop="name" label="字段名称" min-width="100px" />
        <el-table-column prop="annotation" label="字段注释" />
        <el-table-column prop="isPrimary" label="是否主键" />
        <el-table-column prop="isAllowNull" label="是否允许为空" />
        <el-table-column prop="type" label="数据类型" />
        <el-table-column prop="size" label="数据长度" />
        <el-table-column prop="accuracy" label="数据精度" />
        <el-table-column prop="decimalPlace" label="数据小数位" />
        <el-table-column prop="defaultValue" label="数据默认值">
          <template #default="{ row }">
            <span>{{ ['', null, undefined].includes(row.defaultValue) ? '-' : row.defaultValue }}</span>
          </template>
        </el-table-column>
      </el-table>
    </template>

    <template v-if="!readonly" #footer>
      <el-button @click="onCancel"> 取消 </el-button>
      <!-- <el-button v-if="action !== '添加'" @click="onAudit">送审</el-button> -->
      <el-button type="primary" @click="onSave"> 保存 </el-button>
    </template>
  </el-drawer>
</template>

<script setup>
  import { newOrUpdateCatalogue, getCatalogue } from '@/api/index';
  import { ElMessage } from 'element-plus';

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
      required: true,
    },
    data: {
      type: Object,
      required: true,
    },
    readonly: {
      type: Boolean,
      required: false,
    },
  });

  const loading = ref(false);
  const drawer = ref(false);
  const typeOptions = ref([
    {
      label: '基础分类',
      value: '基础分类',
    },
    {
      label: '普通分类',
      value: '普通分类',
    },
  ]);
  let form = reactive({
    title: '',
    type: '',
    description: '',
    notes: '',
    // state: '',
  });
  const rules = reactive({
    title: [{ required: true, message: '请输入目录名称', trigger: 'blur' }],
    type: [{ required: true, message: '请选择目录分类', trigger: 'blur' }],
    description: [{ required: true, message: '请输入目录摘要', trigger: 'blur' }],
  });
  const statusText = {
    1: '待送审',
    2: '已送审',
    3: '审核未通过',
    4: '已发布',
  };
  const statusClass = {
    1: 'status-gray',
    2: 'status-blue',
    3: 'status-red',
    4: 'status-green',
  };
  watchEffect(() => {
    drawer.value = props.modelValue;
    if (drawer.value && props.data.id) {
      fetchData();
    }
  });
  const action = computed(() => {
    return props.data?.id ? (props.readonly ? '查看' : '编辑') : '添加';
  });

  const tableData = ref([
    // {
    //   name: 'ID',
    //   annotation: '姓名',
    //   isPrimary: 'Y',
    //   isAllowNull: 'N',
    //   type: 'int',
    //   size: 20,
    //   accuracy: 10,
    //   decimalPlace: 0,
    //   defaultValue: '-',
    // },
  ]);

  const emit = defineEmits(['update:modelValue', 'success']);
  const formRef = ref();
  const onCancel = () => {
    emit('update:modelValue', false);
    formRef.value.resetFields();
  };
  const onAudit = () => {
    emit('update:modelValue', false);
    formRef.value.validate((valid) => {
      if (valid) {
        onCancel();
        emit('success');
      }
    });
  };
  const saveLoading = ref(false);
  const onSave = () => {
    formRef.value.validate(async (valid) => {
      if (valid) {
        try {
          saveLoading.value = true;
          form.id = props.data.id;
          form.parentId = props.data.parentId;
          await newOrUpdateCatalogue(form);
          ElMessage({ type: 'success', message: '保存成功' });
          onCancel();
          emit('success');
        } catch (error) {
          console.log(error);
        } finally {
          saveLoading.value = false;
        }
      }
    });
  };

  async function fetchData() {
    try {
      loading.value = true;
      const { data } = await getCatalogue(props.data.id);
      nextTick(() => {
        Object.assign(form, data);
      });
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }
</script>

<style lang="scss">
  .status::before {
    content: '';
    display: inline-block;
    border-radius: 999px;
    width: 6px;
    height: 6px;
    margin-right: 6px;
    position: relative;
    top: -2px;
  }
  .status-green::before {
    background: var(--el-color-success);
  }
  .status-gray::before {
    background: $color-tip-text;
  }
  .status-blue::before {
    background: $color-blue;
  }
  .status-red::before {
    background: var(--el-color-danger);
  }
</style>

<style>
  .readonly .el-form-item__label {
    color: #939899;
  }
</style>
