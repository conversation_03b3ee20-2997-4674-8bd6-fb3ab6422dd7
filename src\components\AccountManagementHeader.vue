<template>
  <h2 class="bg-p text-w flex h-[60px] items-center justify-between px-5">
    <div class="text-xl font-semibold">{{ title }}</div>
    <div class="flex gap-4 text-sm">
      <a class="text-w!" @click="goToIndex">返回主页</a>
      <el-dropdown class="dropdown-select" trigger="click">
        <div class="dropdown-text">
          <el-icon class="dropdown-icon--user" :size="20">
            <User />
          </el-icon>
          <div class="dropdown-username">
            {{ username }}
          </div>
          <el-icon :size="14">
            <CaretBottom />
          </el-icon>
        </div>
        <template #dropdown>
          <TopbarDropdown />
        </template>
      </el-dropdown>
    </div>
  </h2>
</template>

<script lang="ts" setup>
  import { computed } from 'vue';
  import { useRouter } from 'vue-router';
  import { useUsers } from '@/store/index';
  import TopbarDropdown from '@/components/TopbarDropdown.vue';
  import { User, CaretBottom } from '@element-plus/icons-vue';

  interface Props {
    title: string;
  }
  const props = defineProps<Props>();

  // 获取路由实例
  const router = useRouter();

  // 获取用户状态管理实例
  const store = useUsers();

  // 计算属性，获取当前用户的用户名
  const username = computed(() => {
    return store.user.username;
  });

  // 定义返回主页的方法
  const goToIndex = () => {
    router.replace({ name: 'Index' });
  };
</script>

<style lang="scss" scoped>
  .dropdown-select {
    color: #fff;
    .dropdown-text {
      user-select: none;
      display: flex;
      align-items: center;
    }

    .dropdown-icon--user {
      border: 1px solid #fff;
      border-radius: 100%;
    }

    .dropdown-username {
      margin-left: 8px;
      margin-right: 7px;
    }
  }
</style>
