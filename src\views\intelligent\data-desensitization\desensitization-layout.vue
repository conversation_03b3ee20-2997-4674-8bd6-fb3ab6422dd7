<template>
  <div class="flex h-full">
    <div class="aside bg-w">
      <asidemenu />
    </div>

    <div class="w-0 flex-1">
      <router-view />
    </div>
  </div>
</template>

<script setup>
  import asidemenu from '@/components/aside.vue';
  import { useRoute } from 'vue-router';
  let route = useRoute();

  let activeId = ref('');
  let menuList = ref([
    // {
    //   id: '1',
    //   title: '脱敏算法管理',
    //   svgname: 'icon-zhuye',
    //   children: [
    //     {
    //       id: '11',
    //       title: '脱敏算法管理',
    //       pathname: 'DesensitizationArithmetic',
    //     },
    //     {
    //       id: '12',
    //       title: '脱敏算法模板',
    //       pathname: 'ArithmeticTemplate',
    //     },
    //   ],
    // },
    // {
    //   id: '2',
    //   title: '脱敏任务配置',
    //   svgname: 'icon-guanliyuan_guanliyuanrizhi',
    //   pathname: 'DesensitizationTask',
    // },
    {
      id: '1',
      title: '脱敏管理',
      svgname: 'icon-guanliyuan_guanliyuanrizhi',
      pathname: 'DesensitizationManage',
    },
  ]);

  watch(
    () => route.name,
    (val, old) => {
      menuList.value.forEach((e) => {
        if (e.pathname == val) {
          activeId.value = e.id;
        } else if (e.children) {
          let childrenItem = e.children.find((c) => c.pathname == val);
          activeId.value = childrenItem?.id;
        }
      });
    },
    {
      immediate: true,
    }
  );
  provide('datalist', menuList);
  provide('openid', activeId);
</script>

<style scoped lang="scss">
  .aside {
    width: 256px;
    height: 100%;
    box-shadow: 2px 0 12px 0 rgb(0 0 0 / 10%);
    z-index: 10;
  }
</style>
