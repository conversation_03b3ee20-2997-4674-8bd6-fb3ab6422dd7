<template>
  <el-dialog v-model="show" title="出版文献" width="80%" class="dialog-height" @close="onCancel">
    <div class="mb-4">
      <el-button type="primary" @click="onAdd"> 新增 </el-button>
      <el-button :disabled="checkList.length <= 0" :loading="batchLoading" @click="onBatchDel"> 批量删除 </el-button>
    </div>

    <el-table :data="tableData" style="width: 100%" class="c-table-header" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" />
      <el-table-column prop="title" label="刊物标题" min-width="100px" />
      <el-table-column prop="description" label="刊物说明" />
      <el-table-column label="操作" width="110">
        <template #default="{ row }">
          <el-button link type="primary" @click="onEdit(row)"> 编辑 </el-button>
          <el-popconfirm title="确定删除此项？" @confirm="onDel(row)">
            <template #reference>
              <el-button link type="primary"> 删除 </el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>

  <el-dialog v-model="showAdd" title="刊物" width="500px" @close="onAddClose">
    <el-form ref="formRef" :model="addForm" :rules="rules" label-width="80px">
      <el-form-item label="刊物标题" prop="title">
        <el-input v-model="addForm.title" placeholder="请输入刊物标题" />
      </el-form-item>
      <el-form-item label="刊物说明" prop="description">
        <el-input v-model="addForm.description" placeholder="请输入刊物说明" />
      </el-form-item>
    </el-form>

    <template #footer>
      <span>
        <el-button @click="onAddClose">取消</el-button>
        <el-button type="primary" :loading="addLoading" @click="onAddConfirm">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
  import { addPublication, findPublicationByMedicalFieldId, deletePublication } from '@/api/index';
  import { ElMessageBox, ElMessage } from 'element-plus';
  import { nextTick } from 'vue';

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
    id: {
      type: [String, Number],
    },
  });
  const show = ref(false);
  watchEffect(() => {
    show.value = props.modelValue;
    if (show.value) {
      fetchData();
    }
  });

  const tableData = ref([]);
  const checkList = ref([]);
  const handleSelectionChange = (val) => {
    checkList.value = val;
  };
  const batchLoading = ref(false);
  const onBatchDel = () => {
    ElMessageBox.confirm('确定删除所有选中项？', '操作提示', { type: 'warning' })
      .then(async () => {
        try {
          batchLoading.value = true;
          await Promise.all(checkList.value.map((row) => deletePublication(props.id, row.id)));
          ElMessage({ type: 'success', message: '删除成功' });
          fetchData();
        } catch (error) {
          console.log(error);
        } finally {
          batchLoading.value = false;
        }
      })
      .catch(() => {});
  };
  const onDel = async (row) => {
    try {
      await deletePublication(props.id, row.id);
      ElMessage({ type: 'success', message: '删除成功' });
      fetchData();
    } catch (error) {
      console.log(error);
    }
  };

  const formRef = ref();
  const addForm = reactive({
    id: 0,
    title: '',
    description: '',
  });
  const rules = ref({
    title: [{ required: true, message: '不能为空' }],
  });
  const showAdd = ref(false);
  const onAdd = () => {
    addForm.id = 0;
    showAdd.value = true;
  };
  const addLoading = ref(false);
  const onAddConfirm = () => {
    formRef.value.validate(async (valid) => {
      try {
        if (valid) {
          addLoading.value = true;
          await addPublication(props.id, addForm);
          showAdd.value = false;
          ElMessage({ type: 'success', message: '操作成功' });
          fetchData();
        }
      } catch (error) {
        console.log(error);
      } finally {
        addLoading.value = false;
      }
    });
  };
  const onAddClose = () => {
    showAdd.value = false;
    formRef.value.resetFields();
  };

  const onEdit = (row) => {
    showAdd.value = true;
    nextTick(() => {
      Object.assign(addForm, row);
    });
  };

  const emit = defineEmits(['update:modelValue', 'success']);
  const onCancel = () => {
    emit('update:modelValue', false);
  };

  async function fetchData() {
    try {
      const { data } = await findPublicationByMedicalFieldId(props.id);
      tableData.value = data;
    } catch (error) {
      return Promise.reject(error);
    }
  }
</script>

<style lang="scss">
  .dialog-height {
    height: 75vh;
    overflow: auto;
    display: flex;
    flex-direction: column;

    .el-dialog__body {
      height: 0;
      flex: 1;
      display: flex;
      flex-direction: column;
    }
  }
</style>

<style lang="scss" scoped>
  .c-table-header {
    flex: 1;
    height: 0;
  }
</style>
