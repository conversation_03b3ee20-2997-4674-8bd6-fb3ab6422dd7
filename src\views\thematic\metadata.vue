<template>
  <div class="flex h-full">
    <div class="aside bg-w">
      <asidemenu />
    </div>

    <div class="w-0 flex-1">
      <router-view />
    </div>
  </div>
</template>

<script setup>
  import asidemenu from '@/components/aside.vue';
  import { useRoute } from 'vue-router';
  let route = useRoute();

  let activeId = ref('');
  let menuList = ref([
    {
      id: '1',
      title: '数据源',
      pathname: 'ThematicDataSource',
      svgname: 'icon-shujuku',
    },
    {
      id: '2',
      title: '数据表',
      pathname: 'ThematicDataTable',
      svgname: 'icon-ziyuan35',
    },
    // {
    //   id: '3',
    //   title: '元数据',
    //   pathname: 'ThematicData',
    //   svgname: 'icon-guanliyuan_guanliyuanrizhi',
    // },
    // {
    //   id: '4',
    //   title: '元数据',
    //   pathname: 'ThematicMedicalManage',
    //   svgname: 'icon-shujuku1',
    // },
  ]);

  watch(
    () => route.name,
    (val, old) => {
      const path = route.path;
      if (path.includes('data-source')) {
        activeId.value = '1';
      }
      if (path.includes('data-table') || path.includes('data-field')) {
        activeId.value = '2';
      }
      if (path.includes('data-origin')) {
        activeId.value = '3';
      }
      if (path.includes('medical-manage')) {
        activeId.value = '4';
      }
    },
    {
      immediate: true,
    }
  );
  provide('datalist', menuList);
  provide('openid', activeId);
</script>

<style scoped lang="scss">
  .aside {
    width: 256px;
    height: 100%;
    box-shadow: 2px 0 12px 0 rgb(0 0 0 / 10%);
    z-index: 10;
  }
</style>
