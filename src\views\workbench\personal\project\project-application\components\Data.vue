<template>
  <div class="h-full p-5">
    <div class="flex h-full flex-col rounded bg-w pr-10 pt-[18px]">
      <h4 class="pl-7">数据下载</h4>

      <div class="mt-4 h-0 flex-1 pl-10">
        <el-table :data="tableData" style="width: 100%" height="100%" class="c-table-header">
          <el-table-column prop="dataId" label="ID" />
          <el-table-column prop="order" label="数据订单编号" />
          <el-table-column prop="desc" label="描述" min-width="100px" />
          <el-table-column prop="size" label="大小" />
          <el-table-column prop="date" label="发布日期" />
          <el-table-column label="操作">
            <template #default="{ row }">
              <el-button link type="primary" @click="onView(row)"> 查看 </el-button>
              <el-button link type="primary" @click="onDownload(row)"> 下载 </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>

  <DataDrawer v-model="drawer" :order="order" />
</template>

<script setup>
  /* 数据 */
  import DataDrawer from './DataDrawer.vue';
  const props = defineProps({
    state: {
      type: String,
      default: '',
    },
  });
  const tableData = ref([
    {
      dataId: '49957',
      order: '4001485',
      desc: '初始数据',
      size: '718MB',
      date: '2022-07-15',
    },
  ]);
  const drawer = ref(false);
  const order = ref('');
  const onView = (row) => {
    order.value = row.order;
    drawer.value = true;
  };
  const onDownload = (row) => {};
</script>
