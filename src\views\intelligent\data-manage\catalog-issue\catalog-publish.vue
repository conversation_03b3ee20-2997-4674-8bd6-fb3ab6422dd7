<template>
  <div class="flex h-full w-full flex-col overflow-hidden">
    <h2 class="flex h-[60px] cursor-pointer items-center bg-w pl-5 text-xl font-semibold" @click="onBack">
      <el-icon class="mr-2" color="#939899">
        <ArrowLeft />
      </el-icon>
      发布
    </h2>

    <div class="m-5 flex flex-1 justify-center rounded bg-w pt-10">
      <div class="w-[600px]">
        <el-form ref="formRef" label-position="left" label-width="120px" :model="formLabelAlign" :rules="rules">
          <el-form-item label="信息资源分类">
            <div class="absolute -left-72 flex items-center justify-start">
              <div class="mr-2 h-[12px] w-[2.5px] bg-p" />
              <span class="text-base font-semibold">目录分类</span>
            </div>
            <el-input v-model="formLabelAlign.classify" disabled />
          </el-form-item>
          <el-form-item label="信息资源代码">
            <div class="absolute -left-72 flex items-center justify-start">
              <div class="mr-2 h-[12px] w-[2.5px] bg-p" />
              <span class="text-base font-semibold">基本信息</span>
            </div>
            <el-input v-model="formLabelAlign.code" disabled />
          </el-form-item>
          <el-form-item label="信息资源名称">
            <el-input v-model="formLabelAlign.name" disabled />
          </el-form-item>
          <el-form-item label="信息资源摘要">
            <el-input v-model="formLabelAlign.abstract" type="textarea" :rows="4" disabled />
          </el-form-item>
          <el-form-item class="mb-10" label="更新周期">
            <el-input v-model="formLabelAlign.period" disabled />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSave"> 发布 </el-button>
            <el-button @click="onBack"> 取消 </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { useRouter } from 'vue-router';
  let router = useRouter();

  const props = defineProps({
    id: { type: String },
  });
  const onBack = () => {
    router.back();
  };

  let formLabelAlign = reactive({
    classify: '',
    code: '',
    name: '',
    abstract: '',
    period: '',
  });
  const onSave = () => {
    router.replace({ name: 'IntelligentIssue' });
  };
</script>
