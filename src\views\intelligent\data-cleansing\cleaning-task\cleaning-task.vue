<template>
  <div class="flex h-full flex-col overflow-hidden">
    <h2 class="flex h-[60px] items-center bg-w pl-5 text-xl font-semibold">清洗任务配置</h2>

    <div class="m-5 flex h-0 flex-1 flex-col rounded-md bg-w pt-5">
      <div class="flex justify-between px-10">
        <div>
          <el-button type="primary" @click="onAdd"> 新建任务 </el-button>
          <el-popconfirm width="180" title="确定删除选中数据？" @confirm="onDel">
            <template #reference>
              <el-button :disabled="checkList.length <= 0"> 删除 </el-button>
            </template>
          </el-popconfirm>
        </div>
        <el-input v-model="search" placeholder="请输入关键字" style="width: 500px">
          <template #prepend>
            <el-select v-model="searchPre" style="width: 150px">
              <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </template>
          <template #append>
            <el-button :icon="Search" @click="onSearch" />
          </template>
        </el-input>
      </div>

      <div class="mt-3 h-0 w-full flex-1 px-10">
        <el-table
          height="100%"
          :data="tableData"
          style="width: 100%"
          class="c-table-header"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column label="启用/禁用任务" width="120">
            <template #default="{ row }">
              <el-switch v-model="row.enable" />
            </template>
          </el-table-column>
          <el-table-column label="任务实例ID">
            <template #default="{ row }">
              <el-button link type="primary" @click="onViewInstance(row)">
                {{ row.instanceId }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="任务名称" min-width="100px" />
          <el-table-column prop="sourceTarget" label="数据源/数据目标" />
          <el-table-column label="状态">
            <template #default="{ row }">
              <span class="status" :class="statusClass[row.state]">{{ statusText[row.state] }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="period" label="清洗周期" />
          <el-table-column label="操作" width="150">
            <template #default="{ row }">
              <el-button link type="primary" @click="onRun(row)"> 立即运行 </el-button>
              <el-button link type="primary" @click="onEdit(row)"> 编辑 </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="pagination-bottom">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :page-size="pagination.pageSize"
          :total="tableData.length"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
  import { Search } from '@element-plus/icons-vue';
  import { useRouter } from 'vue-router';
  const router = useRouter();

  const searchPre = ref('任务名称');
  const search = ref('');
  const options = [
    {
      value: '任务名称',
      label: '任务名称',
    },
    {
      value: '任务实例ID',
      label: '任务实例ID',
    },
    {
      value: '数据源/数据目标',
      label: '数据源/数据目标',
    },
  ];
  const onSearch = () => {};

  //表格
  const statusText = {
    1: '运行中',
    2: '停止运行',
  };
  const statusClass = {
    1: 'status-green',
    2: 'status-gray',
  };
  const tableData = ref([
    {
      id: 1,
      enable: false,
      instanceId: 'JDY214463256333',
      name: '清洗任务-审核表采集',
      sourceTarget: 'sgdhdgh/jshdkjkk',
      state: 1,
      period: '手动执行',
    },
  ]);
  const checkList = ref([]);
  const handleSelectionChange = (val) => {
    checkList.value = val;
  };
  let drawerData = ref({});
  const onAdd = () => {
    router.push({ name: 'CleansingConfig' });
  };
  const onDel = () => {
    console.log(checkList.value);
  };
  const onViewInstance = () => {
    console.log('onViewInstance');
  };
  const onEdit = (row) => {
    drawerData.value = row;
  };
  const onRun = (row) => {};
  const onSuccess = () => {};

  //页码
  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  const handleCurrentChange = (e) => {
    pagination.page = e;
  };
  const handleSizeChange = (e) => {
    pagination.pageSize = e;
  };
</script>
