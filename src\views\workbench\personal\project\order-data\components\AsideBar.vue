<template>
  <div class="aside text-sm">
    <el-scrollbar height="100%" class="scrollbar">
      <div class="aside-header">
        <div class="flex items-center">
          <el-icon class="mr-2 cursor-pointer" color="#939899" size="20px" @click="router.back()">
            <ArrowLeft />
          </el-icon>
          <div class="header-title">选择数据</div>
        </div>
        <div class="mt-2 flex items-center gap-2">
          <el-input
            v-model="datasetName"
            placeholder="请输入数据集名称"
            :prefix-icon="Search"
            @keyup.enter="fetchData"
          />
          <el-button type="primary" @click="fetchData">搜索</el-button>
        </div>
      </div>

      <div v-loading="loading" class="min-h-[200px] overflow-hidden">
        <el-radio-group v-model="datasetId" class="my-radio w-full">
          <el-radio v-for="item in tableData" :key="item.id" :value="item.id" class="w-full">
            <div class="w-0 flex-1 truncate">{{ item.datasetNameCn }}</div>
            <div v-if="item.selectedNum > 0">({{ item.selectedNum }})</div>
          </el-radio>
        </el-radio-group>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
  /* 选择数据侧边栏 */
  import { findFileInforByAnnotationId } from '@/api';
  import { useRouter } from 'vue-router';
  import { Search } from '@element-plus/icons-vue';
  import { useDataBrowse } from '@/store/data-browse';

  type FileInfoVOEx = FileInfoVO & {
    selectedNum: number;
  };

  const emit = defineEmits<{ change: [{ id: number; name: string }] }>();
  const store = useDataBrowse();
  const router = useRouter();
  const datasetId = ref<number>(0);
  const tableData = ref<Array<FileInfoVOEx>>([]);
  const datasetName = ref('');
  const loading = ref(false);

  async function fetchData() {
    try {
      loading.value = true;

      // 获取数据集列表
      const { data } = await findFileInforByAnnotationId({
        pageNum: 1,
        pageSize: 999,
        annotationIDList: [],
        otherFilter: datasetName.value,
      });

      // 处理返回数据
      tableData.value =
        data?.content?.map((item) => ({
          ...item,
          selectedNum: store.getFieldLength(item.id!.toString()),
        })) || [];

      // 默认选中第一个数据集
      if (tableData.value.length) {
        datasetId.value = tableData.value[0].id || 0;
      }
    } catch (error) {
      console.error('获取数据集失败:', error);
    } finally {
      loading.value = false;
    }
  }

  // 使用深度监听，确保字段变化时更新选中数量
  watch(
    () => store.dataBrowse.fileds,
    () => {
      tableData.value = tableData.value.map((item) => ({
        ...item,
        selectedNum: store.getFieldLength(item.id!.toString()),
      }));
    },
    { deep: true }
  );

  watch(datasetId, (newVal) => {
    const name = tableData.value.find((e) => e.id === newVal)?.datasetNameCn || '';
    store.setData({ id: newVal });
    emit('change', { id: newVal, name });
  });

  fetchData();
</script>

<style lang="scss" scoped>
  .aside {
    height: 100%;
    width: 320px;
  }

  .scrollbar {
    padding-left: 20px;
    padding-right: 20px;
  }

  :deep(.el-scrollbar__view) {
    height: 100%;
  }

  .aside-header {
    padding-top: 20px;
    border-bottom: 1px solid #e0e0e0;
    padding-bottom: 15px;
    margin-bottom: 12px;

    .header-title {
      font-size: 18px;
      line-height: 26px;
      font-weight: 700;
    }
  }

  :deep(.my-radio) {
    .el-radio {
      margin-right: 0;
    }
    .el-radio__label {
      width: 0;
      flex: 1;
      display: flex;
    }
  }
</style>
