/*
 * @Description:用户中心
 */
import { useUsers } from '@/store/user-info';

export default {
  path: '/workbench',
  component: () => import('@/views/logged-layout/index.vue'),
  children: [
    //消息中心
    {
      path: 'msg',
      name: 'WorkbenchMsg',
      component: () => import('@/views/logged-layout/msg-center/index.vue'),
    },
    //购物车
    {
      path: 'cart',
      name: 'WorkbenchCart',
      component: () => import('@/views/logged-layout/shopping-cart/index.vue'),
    },
    //用户中心
    {
      path: 'user',
      component: () => import('@/views/logged-layout/user-center/index.vue'),
      beforeEnter: (to, from) => {
        useUsers().setUserCenterPageUp(from.name);
      },
      children: [
        {
          path: 'base-info',
          name: 'WorkbenchBaseInfo',
          component: () => import('@/views/logged-layout/user-center/base-info/base-info.vue'),
        },
        {
          path: 'user-material',
          name: 'WorkbenchMaterial',
          component: () => import('@/views/logged-layout/user-center/user-material.vue'),
        },
        {
          path: 'access-security',
          name: 'WorkbenchAccessSecurity',
          component: () => import('@/views/logged-layout/user-center/access-security.vue'),
        },
        {
          path: 'data-platform',
          name: 'WorkbenchDataPlatform',
          component: () => import('@/views/logged-layout/user-center/data-platform.vue'),
        },
        {
          path: 'subscription',
          name: 'WorkbenchSubscription',
          component: () => import('@/views/logged-layout/user-center/subscription.vue'),
        },
        {
          path: 'my-team',
          name: 'WorkbenchMyTeam',
          component: () => import('@/views/logged-layout/user-center/my-team/my-team.vue'),
        },
        {
          path: 'data-manage',
          name: 'WorkbenchDataManage',
          component: () => import('@/views/workbench/org/data-manage/index.vue'),
        },
      ],
    },
    //数据详情
    {
      path: 'data-detail',
      name: 'OrgDataDetail',
      component: () => import('@/views/workbench/org/data-manage/detail.vue'),
      props: (route) => ({ id: route.query.id }),
    },
    //新闻详情
    {
      path: 'network-detail',
      name: 'NetworkDetail',
      component: () => import('@/views/workbench/network-detail/index.vue'),
      props: (route) => ({ id: route.query.id }),
    },
  ],
};
