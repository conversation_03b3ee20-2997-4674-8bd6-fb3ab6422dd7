<template>
  <div class="bg-p pb-5">
    <div class="text-w mx-auto w-[1280px]">
      <h2 class="flex cursor-pointer items-center text-[34px] font-bold" @click="onBack">
        <el-icon class="mr-2">
          <ArrowLeft />
        </el-icon>
        医疗信息库
      </h2>
      <el-descriptions direction="horizontal" :column="3" class="mt-5">
        <el-descriptions-item label="管理单位"> 桂林医学院附属医院 </el-descriptions-item>
        <el-descriptions-item label="接入时间"> 2022-03-12 12:00:00 </el-descriptions-item>
        <el-descriptions-item label="更新频率"> 每日03:00 </el-descriptions-item>
        <el-descriptions-item label="管理员"> 张三 </el-descriptions-item>
        <el-descriptions-item label="更新时间"> 2022-03-12 12:00:00 </el-descriptions-item>
      </el-descriptions>
    </div>
  </div>

  <div class="mx-auto mt-10 w-[1280px] overflow-hidden">
    <div class="bg-w rounded px-10 pt-[18px] pb-10">
      <h3 class="text-lg">统计信息</h3>
      <ul class="flex h-[156px] items-center justify-center">
        <li
          v-for="item in data"
          :key="item.id"
          class="border-border flex flex-1 justify-center border-r last-of-type:border-none"
        >
          <img class="mr-6 h-[60px] w-[60px]" :src="item.img" alt="" />
          <div>
            <span class="mr-3 text-[28px]">{{ item.number }}</span>
            <span>{{ item.unit }}</span>
            <div class="text-tip text-sm">
              {{ item.detail }}
            </div>
          </div>
        </li>
      </ul>
      <div class="flex h-[400px] flex-1">
        <div class="border-border bg-w mr-5 flex-1 rounded border-r py-3">
          <Chart :option="options1" />
        </div>
        <div class="bg-w flex-1 rounded py-3">
          <Chart :option="options2" />
        </div>
      </div>
    </div>

    <div class="bg-w my-5 rounded px-10 pt-[18px] pb-10">
      <h3 class="text-lg">数据字段详情</h3>
      <el-table :data="tableData" style="width: 100%" class="c-table-header mt-4">
        <el-table-column type="expand">
          <template #default="props">
            <div class="bg-baf px-10 py-5">
              <el-descriptions direction="horizontal" :column="4">
                <el-descriptions-item label="参与者"> 324900 </el-descriptions-item>
                <el-descriptions-item label="值类型"> 分类（单个） </el-descriptions-item>
                <el-descriptions-item label="性别"> 两性 </el-descriptions-item>
                <el-descriptions-item label="首发"> 2012.02 </el-descriptions-item>
                <el-descriptions-item label="项目数"> 318546 </el-descriptions-item>
                <el-descriptions-item label="种类"> 数据 </el-descriptions-item>
                <el-descriptions-item label="实例"> 单数 </el-descriptions-item>
                <el-descriptions-item label="版本"> 2012.02 </el-descriptions-item>
                <el-descriptions-item label="稳定性"> 完全 </el-descriptions-item>
                <el-descriptions-item label="层级"> 基本 </el-descriptions-item>
                <el-descriptions-item label="数组"> 无 </el-descriptions-item>
                <el-descriptions-item label="成本层"> d1 o1 s1 </el-descriptions-item>
              </el-descriptions>
              <InfoTabs />
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="字段名称" />
        <el-table-column prop="type" label="字段类型" />
        <el-table-column prop="desc" label="字段说明" min-width="100px" />
        <el-table-column prop="about" label="相关标准" />
      </el-table>
    </div>
  </div>
</template>

<script setup>
  import img1 from '@/assets/img/number.png';
  import img2 from '@/assets/img/storage.png';
  import img3 from '@/assets/img/field.png';
  import Chart from '@/components/Chart.vue';
  import InfoTabs from './components/InfoTabs.vue';
  import { useRouter } from 'vue-router';
  const router = useRouter();

  const onBack = () => {
    router.back();
  };
  const data = reactive([
    {
      id: 1,
      img: img1,
      number: 125446,
      unit: '条',
      detail: '数据总条目',
    },
    {
      id: 2,
      img: img2,
      number: 3,
      unit: 'TB',
      detail: '数据存储总量',
    },
    {
      id: 3,
      img: img3,
      number: 620,
      unit: '条',
      detail: '数据字段总数',
    },
  ]);
  const typeData = [
    { value: 78, name: '产品A' },
    { value: 75, name: '产品B' },
    { value: 80, name: '产品C' },
    { value: 44, name: '产品D' },
    { value: 30, name: '产品E' },
  ];
  const options1 = {
    title: [
      {
        text: '数据类型分布',
      },
      {
        text: `{name|总数}\n{val|18,004}`,
        top: 'center',
        left: 'center',
        textStyle: {
          rich: {
            name: {
              fontSize: 12,
              color: '#666666',
              padding: [10, 0],
            },
            val: {
              fontSize: 22,
              color: '#333333',
            },
          },
        },
      },
    ],
    tooltip: {
      trigger: 'item',
    },
    legend: {
      top: 'bottom',
      left: 'center',
      icon: 'circle',
      // formatter(name) {
      //   const item = typeData.find((item) => item.name === name);
      //   return `${item.name} ${item.value}（${((item.value / typeTotal) * 100).toFixed(2) + '%'}）`;
      // },
    },
    series: [
      {
        type: 'pie',
        radius: ['55%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 3,
          color(colors) {
            let colorList = ['#5c89e6', '#2eb7e6', '#13a0bf', '#29ccb0', '#9f7ede'];
            return colorList[colors.dataIndex];
          },
        },
        label: {
          //下面三条语句设置了让文字显示在标线上
          formatter: '{b}\n\n{c}（{d}%）',
          padding: [0, -40],
          alignTo: 'labelLine',
        },
        labelLine: {
          length: 10, //第一段表示线
          length2: 50, // 第二段标示线
        },
        data: typeData,
      },
    ],
  };
  const options2 = {
    title: {
      text: '数据更新状态',
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        data: ['04.15', '04.16', '04.17', '04.18', '04.19', '04.20', '04.21', '04.22'],
        axisTick: {
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: '#ccc',
          },
        },
        axisLabel: {
          color: '#565b5c',
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        axisLabel: {
          color: '#939899',
        },
      },
    ],
    series: [
      {
        type: 'bar',
        barWidth: '50%',
        data: [2800, 4500, 9000, 3000, 6300, 4500, 1700, 3700],
        color: '#1e9bb6',
      },
    ],
  };

  const tableData = ref([
    {
      name: 'rfMRI 的回波时间',
      type: '整形',
      desc: '文字描述+pdf文件下载',
      about: 'GX12389',
    },
  ]);
</script>

<style lang="scss" scoped>
  :deep(.el-descriptions) {
    .el-descriptions__body {
      background-color: transparent;
    }
    .el-descriptions__label {
      color: #fff;
      opacity: 0.7;
    }
    .el-descriptions__content {
      color: #fff;
    }
  }

  .c-table-header {
    :deep(.el-descriptions) {
      --el-table-tr-bg-color: #f7f9fc;
      .el-descriptions__label {
        color: $color-tip-text;
      }
      .el-descriptions__content {
        color: $color-main-text;
      }
    }

    :deep(.el-table__expanded-cell) {
      padding: 0;
    }
  }
</style>
