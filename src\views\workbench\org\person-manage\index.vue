<template>
  <div class="flex h-full flex-col overflow-hidden">
    <h2 class="flex h-[60px] items-center bg-w pl-5 text-xl font-bold">人员管理</h2>

    <div class="m-5 flex h-0 flex-1 flex-col bg-w px-10 pt-5">
      <div>
        <el-button type="primary" @click="onAdd"> 添加人员 </el-button>
      </div>

      <div class="mt-4 h-0 flex-1">
        <el-table :data="tableData" style="width: 100%" height="100%" class="c-table-header">
          <el-table-column prop="name" label="姓名" />
          <el-table-column prop="department" label="部门" min-width="100px" />
          <el-table-column prop="userId" label="ID" />
          <el-table-column prop="certificatetype" label="证件类型" width="100px" />
          <el-table-column prop="certificatecode" label="证件号码" />
          <el-table-column prop="phonenumber" label="联系方式" width="120px" />
          <el-table-column prop="position" label="职位" width="100px" />
          <el-table-column prop="professional" label="职称" width="100px" />
          <el-table-column label="操作" width="100px">
            <template #default="{ row }">
              <el-button link type="primary" @click="onEdit(row)"> 编辑 </el-button>
              <el-popconfirm title="确定删除此项？" @confirm="onDel(row)">
                <template #reference>
                  <el-button link type="primary"> 删除 </el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>

  <PersonDrawer v-model="showDrawer" :data="data" @success="onSuccess" />
</template>

<script setup>
  import PersonDrawer from './components/PersonDrawer.vue';
  // import { getorAllperson } from '@/api/organization/personmanage/index';

  const tableData = ref([
    {
      userId: '21245577',
      name: '李桂芬',
      sex: '女',
      professional: '高级工程师',
      education: '本科',
      certificatetype: '身份证',
      certificatecode: '450833198807085672',
      phonenumber: '15677881087',
      department: 'XXXXX部门 / 技术部 / 开发组',
      position: '开发工程师',
    },
  ]);
  let showDrawer = ref(false);
  let data = reactive({});
  const onAdd = () => {
    data = {};
    showDrawer.value = true;
  };
  const onEdit = (item) => {
    data = item;
    showDrawer.value = true;
  };
  const onDel = () => {};
  const onSuccess = () => {};

  // getorAllperson('2')
  //   .then((res) => {
  //     console.log(res);
  //     if (res.status == '200') {
  //       res.data.data.forEach((ele, index) => {
  //         ele.customer.userid = ele.customer.id;
  //         delete ele.customer.id;
  //         cartItems.value[index] = { ...ele, ...ele.customer };
  //       });
  //     }
  //     persontableloading.value = false;
  //   })
  //   .catch((err) => {
  //     persontableloading.value = false;
  //   });
</script>

<style lang="scss" scoped>
  .title {
    font-size: 20px;
    font-weight: 800;
    margin: 1.5vh;
    z-index: 2;
  }
  .person {
    background-color: #e6e8eb;
    height: 90vh;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .content {
    background-color: white;
    width: 98%;
    height: 95%;
  }
  .add {
    cursor: pointer;
    height: 5vh;
    background-color: #007f99;
    width: 12vh;
    line-height: 5vh;
    display: flex;
    justify-content: center;
    align-content: center;
    margin: 2vh;
    color: white;
    border-radius: 3px;
  }
  .view {
    display: flex;
    justify-content: center;
    align-items: center;
    align-content: center;
  }
  .unpaid-box {
    width: 97%;
    background-color: #fff;
    height: 75vh;
  }
  table {
    width: 100%;
    margin: 0.7vh auto;
    border-collapse: collapse;
    background-color: #fff;
    thead tr th {
      font-weight: 700;
      background-color: #f0f2f5;
      padding: 1.3vh 0;
      text-align: center;
      color: #242727;
    }
    tbody tr {
      text-align: center;
      color: #7c8081;
      font-size: 12px;
      border-bottom: 1px solid #e8eaed;
      height: 5vh;
      button {
        border: none;
        background-color: #fff;
        color: #478cff;
        cursor: pointer;
      }
    }
  }

  .close {
    font-size: 25px;
    cursor: pointer;
  }

  :deep(.el-drawer__header) {
    margin-bottom: 0 !important;
  }
  .personMessage {
    margin-top: -1vh;
    border-top: 1px solid rgb(204, 204, 204);
    .subtitle {
      margin: 1vh;
    }
    .el-select {
      width: 100%;
    }
  }

  .detail {
    margin-left: 2vh;
    font-size: 14px;
    #title {
      color: rgb(147, 147, 147);
    }
    #value {
      padding-bottom: 1vh;
    }
  }

  .manageMessage {
    margin: 1vh;
    .text {
      font-size: 14px;
      color: grey;
    }
    .button {
      border-top: 1px solid rgb(195, 195, 195);
      margin-top: 6vh;
      padding-top: 3vh;
      :deep(.el-button .el-button--primary #save) {
        background-color: #007f99 !important;
      }
    }
  }
</style>
