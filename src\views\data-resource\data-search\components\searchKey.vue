<template>
  <!-- <ul class="center-content">
    <li v-for="(item, index) in list" :key="index" class="card">
      <h3 class="title">
        {{ item.name }}
      </h3>
      <p class="content">
        {{ item.content }}
      </p>
    </li>
  </ul> -->
  <div class="p-4 text-center">功能建设中</div>
</template>

<script setup lang="ts">
  const list = ref([
    {
      name: '数据字段',
      content:
        'Lorem ipsum dolor sit amet, consectetur adipiscing elit . Aenean euismod bibendum laoreet . Proin gravida dolor sit amet lacus accumsan et viverra justo commodo. Proin sodales pulvinar sic tempor. Sociis natoque penatibus et magnis dis parturient montes , nascetur ridiculus mus. Nam fermentum, nulla luctus pharetra vulputate, felis tellus mollis orci, sed rhoncus pronin sapien nunc accuan eget.',
    },
    {
      name: '数据编码',
      content:
        'Lorem ipsum dolor sit amet, consectetur adipiscing elit. <PERSON><PERSON>an euismod bibendum laoreet. Proin gravida dolor sit amet lacus accumsan et viverra justo commodo. Proin sodales pulvinar sic tempor. Sociis natoque penatibus et magnis dis parturient montes , nascetur ridiculus mus. Nam fermentum, nulla luctus pharetra vulputate, felis tellus mollis orci, sed rhoncus pronin sapien nunc accuan eget.',
    },
    {
      name: '数据分类',
      content:
        'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean euismod bibendum laoreet. Proin gravida dolor sit amet lacus accumsan et viverra justo commodo. Proin sodales pulvinar sic tempor. Socis natoque penatibus et magnis dis parturient montes , nascetur ridiculus mus. Nam fermentum, nulla luctus pharetra vulputate, felis tellus mollis orci, sed rhoncus pronin sapien nunc accuan eget.',
    },
    {
      name: '数据资源',
      content:
        'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean euismod bibendum laoreet. Proin gravida dolor sit amet lacus accumsan et viverra justo commodo. Proin sodales pulvinar sic tempor. Socis natoque penatibus et magnis dis parturient montes , nascetur ridiculus mus. Nam fermentum, nulla luctus pharetra vulputate, felis tellus mollis orci, sed rhoncus pronin sapien nunc accuan eget.',
    },
    //底下那一坨
  ]);
</script>

<style scoped lang="scss">
  .center-content {
    padding-bottom: 40px;
  }

  .card {
    padding: 18px 40px;
    background: #ffffff;
    border-radius: 3px;
    margin-top: 40px;

    .title {
      font-size: 18px;
    }

    .content {
      margin-top: 19px;
      color: #565b5c;
    }
  }
</style>
