//用户业务管理员页面
export default {
  path: '/business',
  redirect: '/business/index/scroll',
  component: () => import('@/views/logged-layout/index.vue'),
  children: [
    //数据资源管理
    {
      path: 'data-resource',
      name: 'BusinessResource',
      component: () => import('@/views/business/data-resource/data-resource.vue'),
    },
    //用户业务管理
    {
      path: 'customer-service',
      name: 'BusinessCustomer',
      component: () => import('@/views/business/customer-service/customer-service.vue'),
    },
    //专题库管理
    {
      path: 'thematic-library',
      name: 'BusinessThematic',
      component: () => import('@/views/business/thematic-library/thematic-library.vue'),
    },
    {
      path: 'manage',
      component: () => import('@/views/business/manage/manage.vue'),
      children: [
        //机构管理
        {
          path: 'org-manage',
          name: 'BusinessOrgManage',
          component: () => import('@/views/business/manage/org-manage/org-manage.vue'),
        },
        {
          path: 'personal-manage',
          name: 'BusinessPersonalManage',
          component: () => import('@/views/business/manage/personal-manage/personal-manage.vue'),
        },
        {
          path: 'project-manage',
          name: 'BusinessProjectManage',
          component: () => import('@/views/business/manage/project-manage/project-manage.vue'),
        },
      ],
    },
  ],
};
