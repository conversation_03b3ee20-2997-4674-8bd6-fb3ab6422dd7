import fs from 'fs';
import path from 'path';
import { promisify } from 'util';
import prettier from 'prettier';

const readdir = promisify(fs.readdir);
const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);

// 递归扫描目录，获取所有 .ts 文件
async function scanDirectory(dir) {
  const entries = await readdir(dir, { withFileTypes: true });
  let files = [];
  for (const entry of entries) {
    const fullPath = path.join(dir, entry.name);
    if (entry.isDirectory()) {
      files = files.concat(await scanDirectory(fullPath));
    } else if (path.extname(entry.name) === '.ts') {
      files.push(fullPath);
    }
  }
  return files;
}

// 从文件内容中提取 API 路径和函数名
function extractApiInfo(fileContent) {
  const regex = /export function (\w+)\([^)]*\) {\s*return request<[^>]*>\(`([^`]*)`/g;
  const matches = [];
  let match;
  while ((match = regex.exec(fileContent)) !== null) {
    const functionName = match[1];
    let apiPath = match[2];
    apiPath = apiPath.replace(/\$/g, '');
    matches.push({ apiPath, functionName });
  }
  return matches;
}

// 主函数
async function main() {
  const modulesDir = path.join(process.cwd(), '/src/api/modules');
  const tsFiles = await scanDirectory(modulesDir);
  const mapping = {};

  for (const file of tsFiles) {
    const fileContent = await readFile(file, 'utf8');
    const apiInfos = extractApiInfo(fileContent);
    for (const info of apiInfos) {
      const { apiPath, functionName } = info;
      mapping[apiPath] = functionName;
    }
  }

  const tsContent = `export default ${JSON.stringify(mapping, null, 2)};`;

  const prettierConfig = {
    parser: 'typescript',
    semi: true,
    tabWidth: 2,
    singleQuote: true,
    printWidth: 120,
    trailingComma: 'es5',
    vueIndentScriptAndStyle: true,
    endOfLine: 'crlf',
    bracketSpacing: true,
  };

  try {
    const formattedContent = await prettier.format(tsContent, prettierConfig);
    await writeFile(path.join(process.cwd(), '/src/api/api_mapping.js'), formattedContent);
    console.log('映射表已成功保存到 api_mapping.ts 文件。');
  } catch (error) {
    console.error('保存映射表时出现错误:', error);
  }
}

main().catch((error) => {
  console.error('执行过程中出现错误:', error);
});
