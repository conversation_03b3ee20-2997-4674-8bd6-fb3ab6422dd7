<template>
  <el-drawer v-model="drawer" class="relative" @close="onCancel">
    <template #header>
      <h4 class="text-m">人员操作</h4>
    </template>

    <el-form ref="formRef" label-position="top" :model="form" :rules="rules">
      <div class="mb-4 flex w-full items-center">
        <div class="h-[13px] w-[3px] bg-p" />
        <span class="ml-2">个人信息</span>
      </div>
      <el-form-item label="姓名" prop="name">
        <el-input v-model="form.name" placeholder="请输入姓名" />
      </el-form-item>
      <el-form-item label="性别" prop="sex">
        <el-select v-model="form.sex" class="w-full" placeholder="请选择性别">
          <el-option v-for="item in genderOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="证件类型" prop="certificatetype">
        <el-select v-model="form.certificatetype" class="w-full" placeholder="请选择证件类型">
          <el-option v-for="item in idTypeOptions" :key="item" :label="item" :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item label="证件号码" prop="certificatecode">
        <el-input v-model="form.certificatecode" placeholder="请输入证件号码" />
      </el-form-item>
      <el-form-item label="学历">
        <el-select v-model="form.education" class="w-full" placeholder="请选择学历">
          <el-option v-for="item in educationOptions" :key="item" :label="item" :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item label="职称">
        <el-select v-model="form.professional" class="w-full" placeholder="请选择职称">
          <el-option v-for="item in rankOptions" :key="item" :label="item" :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item label="手机号" prop="phonenumber">
        <el-input v-model="form.phonenumber" placeholder="请输入手机号" />
      </el-form-item>
      <div class="mb-4 flex w-full items-center">
        <div class="h-[13px] w-[3px] bg-p" />
        <span class="ml-2">机构信息</span>
      </div>
      <el-form-item label="部门">
        <el-input v-model="form.department" placeholder="请输入部门" />
      </el-form-item>
      <el-form-item label="职位">
        <el-input v-model="form.position" placeholder="请输入职位" />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="onCancel"> 取消 </el-button>
      <el-button type="primary" @click="onSave"> 保存 </el-button>
    </template>
  </el-drawer>
</template>

<script setup>
  /*添加人员*/
  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
      required: true,
    },
    data: {
      type: [Object, null],
      required: true,
    },
  });
  const genderOptions = [
    { label: '男', value: 1 },
    { label: '女', value: 0 },
  ];
  const idTypeOptions = ['身份证'];
  const educationOptions = ['本科', '专科', '硕士', '博士'];
  const rankOptions = ['初级工程师', '中级工程师', '高级工程师'];
  let form = reactive({
    name: '',
    sex: '',
    professional: '',
    education: '',
    certificatetype: '',
    certificatecode: '',
    phonenumber: '',
    department: '',
    position: '',
  });
  const rules = reactive({
    name: [{ required: true, message: '请输入姓名' }],
    sex: [{ required: true, message: '请选择性别' }],
    certificatetype: [{ required: true, message: '请选择证件类型' }],
    certificatecode: [{ required: true, message: '请输入证件号码' }],
    phonenumber: [{ required: true, message: '请输入手机号码' }],
  });

  const drawer = ref(false);
  watchEffect(() => {
    drawer.value = props.modelValue;
  });
  watch(
    () => props.data,
    (value) => {
      for (const key in form) {
        form[key] = value[key];
      }
    }
  );

  const emit = defineEmits(['update:modelValue', 'success']);
  const formRef = ref();
  const onCancel = () => {
    emit('update:modelValue', false);
    formRef.value.resetFields();
  };
  const onSave = () => {
    formRef.value.validate((valid) => {
      if (valid) {
        onCancel();
        emit('success');
      }
    });
  };
</script>
