<template>
  <div class="pl-10 pr-10 pt-4">
    <h4 class="text-xl font-bold">数据平台权限审核</h4>
    <el-table :data="tableData" class="mt-4" header-row-class-name="table-header" cell-class-name="table-cell">
      <el-table-column prop="platform" label="平台" />
      <el-table-column prop="scope" label="范围" />
      <el-table-column label="状态">
        <template #default="scope">
          <div class="status-text">
            {{ scope.row.state }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="Date" label="有效期" width="200px" />
      <el-table-column label="操作">
        <template #default="scope">
          <el-popconfirm title="确定撤销此项？" @confirm="onRevocation(scope.row)">
            <template #reference>
              <a class="mr-5 text-blue">撤销</a>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
  /* 数据平台权限审核 */
  const tableData = [
    {
      platform: '智能分析平台',
      scope: '申请数据',
      state: '允许访问',
      Date: '2022-03-12 12：00：00',
    },
    {
      platform: '智能分析平台',
      scope: '用户信息',
      state: '允许访问',
      Date: '2022-03-12 12：00：00',
    },
    {
      platform: '智能分析平台',
      scope: '申请数据',
      state: '允许访问',
      Date: '2022-03-12 12：00：00',
    },
    {
      platform: '智能分析平台',
      scope: '申请数据',
      state: '允许访问',
      Date: '2022-03-12 12：00：00',
    },
    {
      platform: '智能分析平台',
      scope: '申请数据',
      state: '允许访问',
      Date: '2022-03-12 12：00：00',
    },
  ];

  const onRevocation = (row) => {
    console.log(row.platform);
  };
</script>

<style lang="scss" scoped>
  :deep(.table-header) {
    --el-table-header-bg-color: #f0f2f5;
    color: $color-regular-text;
  }

  :deep(.table-cell) {
    color: $color-regular-text;
  }

  .status-text::before {
    content: '';
    display: inline-block;
    background: #3ec59a;
    width: 6px;
    height: 6px;
    border-radius: 6px;
    margin-right: 6px;
    position: relative;
    top: -2px;
  }
</style>
