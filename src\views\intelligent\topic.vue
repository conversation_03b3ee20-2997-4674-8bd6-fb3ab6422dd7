<template>
  <div v-loading="tableLoading" class="bg-bac h-full overflow-hidden p-5">
    <div v-loading="tableLoading" class="bg-w flex h-full flex-col overflow-hidden rounded-md p-4">
      <div class="flex">
        <el-input
          v-model="fieldName"
          placeholder="请输入关键字搜索"
          style="width: 300px"
          clearable
          @clear="fetchTable()"
          @keyup.enter="fetchTable()"
        >
          <template #append>
            <el-button :icon="Search" @click="fetchTable()" />
          </template>
        </el-input>
      </div>

      <el-table ref="tableRef" :data="tableData" style="width: 100%" class="c-table-header mt-4 h-0 flex-1">
        <el-table-column prop="datasetNameCn" label="数据集名称(中文)" show-overflow-tooltip />
        <el-table-column prop="datasetName" label="数据集名称(英文)" show-overflow-tooltip />
        <el-table-column prop="projectCode" label="课题编码缩写" />
        <el-table-column prop="description" label="数据集说明" show-overflow-tooltip />
        <el-table-column prop="diseaseTypeAnnotation" label="疾病类型" />
        <el-table-column prop="createDate" label="更新日期" width="170px" />
        <el-table-column prop="dataManager" label="数据负责人" width="120px" />
        <el-table-column prop="affiliatedUnit" label="所属单位" />
        <el-table-column label="操作" width="100">
          <template #default="{ row }">
            <el-button link type="primary" @click="onViewDetail(row)">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-bottom">
        <el-pagination
          background
          layout="total, prev, pager, next, jumper"
          :page-size="pagination.pageSize"
          :total="total"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<!-- 数据资源跳转的专题库页面 -->
<script setup lang="ts">
  import { findByBictionaryCode, findFileInforByAnnotationId } from '@/api/index';
  import { Search } from '@element-plus/icons-vue';
  import { useRouter } from 'vue-router';
  const router = useRouter();

  interface Props {
    tag: string;
  }
  const props = defineProps<Props>();

  //表格
  const ids = ref<number[]>([]);
  const tableLoading = ref(false);
  const tableData = ref<FileInfoVO[]>([]);
  const fieldName = ref('');
  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  const total = ref(0);
  const handleCurrentChange = (e) => {
    pagination.page = e;
    fetchTable(e);
  };
  async function fetchTable(pageNum = 1) {
    try {
      tableLoading.value = true;
      let params: AnnotationIDListDTO = {
        annotationIDList: ids.value,
        pageNum,
        pageSize: pagination.pageSize,
        otherFilter: fieldName.value,
      };
      const { data } = await findFileInforByAnnotationId(params);
      total.value = data!.totalElement!;
      tableData.value = data?.content || [];
    } catch (error) {
      console.log(error);
    } finally {
      tableLoading.value = false;
    }
  }

  async function fetchTypes() {
    try {
      const { data } = await findByBictionaryCode(1, 999, { dictionaryCode: 'TYPE_DISEASE' } as any);
      const item = data?.content?.find((e) => e.title === props.tag);
      if (item) {
        ids.value = [item.id!];
        fetchTable();
      }
    } catch (error) {
      console.log(error);
    }
  }

  //查看详情
  const onViewDetail = (row) => {
    router.push({ name: 'DataSetField', params: { id: row.id } });
  };

  onBeforeMount(() => {
    fetchTypes();
  });
</script>
