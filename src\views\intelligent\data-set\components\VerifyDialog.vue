<template>
  <el-dialog v-model="modelValue" title="数据校验" width="80%" top="4vh">
    <div class="mb-4 flex flex-wrap items-center justify-center break-all bg-baf px-4 py-2 text-base">
      <span>数据校验结果：</span>
      <span :class="isPassCheck ? 'text-success' : 'text-danger'">{{ isPassCheck ? '校验通过' : '校验不通过' }}</span>
      <div v-if="!isPassCheck">
        <span> ，有 </span>
        <span class="font-bold text-danger"> {{ failedCount }} </span>
        条数据未通过校验，请修改后再导入，
        <el-link type="danger" style="font-size: 16px" :underline="false">点击查看错误数据</el-link>
      </div>
    </div>

    <el-table v-loading="loading" :data="tableData" style="width: 100%" class="c-table-header">
      <el-table-column prop="id" label="字段ID" width="100">
        <template #default="{ row }">
          {{ row.id }}
        </template>
      </el-table-column>
      <el-table-column prop="name" label="字段名称" />
      <el-table-column prop="valueType" label="字段类型" />
      <el-table-column prop="baseInfo" label="基本信息" />
      <el-table-column label="是否按性别区分">
        <template #default="{ row }">
          <span>{{ row.gendered ? '是' : '否' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" min-width="200" />
      <el-table-column prop="id" label="更新时间" width="180">
        <template #default="{ row }">
          {{ row.rltTime.updateTime }}
        </template>
      </el-table-column>
    </el-table>

    <div class="mt-5 flex justify-center">
      <el-pagination
        background
        layout="total, prev, pager, next, jumper"
        :page-size="pagination.pageSize"
        :total="total"
        @current-change="handleCurrentChange"
      />
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="onCancel">取消导入</el-button>
        <el-button :disabled="!isPassCheck" type="primary" @click="onSave">确定导入</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { findMedicalFieldsByFileInforIdAndDynamicConditions } from '@/api';
  import { ElMessage } from 'element-plus';

  const modelValue = defineModel<boolean>({ required: true });
  interface Props {
    id: number;
  }
  const props = defineProps<Props>();

  const failedCount = ref(0);
  const isPassCheck = computed(() => !failedCount.value);
  const loading = ref(false);
  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  const total = ref(0);
  const handleCurrentChange = (e) => {
    pagination.page = e;
    fetchData(e);
  };
  const tableData = ref<MedicalFieldVO[]>([]);
  async function fetchData(pageNum = 1) {
    try {
      loading.value = true;
      const { data } = await findMedicalFieldsByFileInforIdAndDynamicConditions(+props.id, {
        pageNum,
        pageSize: pagination.pageSize,
      });
      total.value = data?.totalElement || 0;
      pagination.page = pageNum;
      tableData.value = data?.content || [];
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  const emit = defineEmits<{ success: [] }>();
  const onCancel = () => {
    modelValue.value = false;
  };
  const onSave = () => {
    onCancel();
    emit('success');
  };

  watch(modelValue, (value) => {
    if (value) {
      fetchData();
    }
  });
</script>
