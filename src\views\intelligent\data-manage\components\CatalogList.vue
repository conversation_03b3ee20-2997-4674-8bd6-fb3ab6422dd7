<template>
  <el-scrollbar height="100%">
    <div class="p-5">
      <div class="mb-3 flex w-full items-center">
        <span class="text-sm font-bold">目录列表</span>
        <el-icon class="ml-1 cursor-pointer" size="16px" color="#666" @click="onRefresh">
          <Refresh />
        </el-icon>
      </div>
      <el-tree
        v-if="showTree"
        ref="treeRef"
        v-loading="loading"
        accordion
        check-on-click-node
        highlight-current
        node-key="id"
        lazy
        :props="treeProps"
        :load="loadNode"
        :current-node-key="currentId"
        @current-change="changeTree"
      />
    </div>
  </el-scrollbar>
</template>

<script setup>
  /* 目录列表 */
  import { getTopBasicCatalogues, getChildCatalogue } from '@/api/index';
  import { nextTick } from 'vue';
  const props = defineProps({ modelValue: { type: String } });
  const emit = defineEmits(['update:modelValue', 'get-name']);

  const showTree = ref(true);
  const loading = ref(false);
  const treeRef = ref();
  const currentId = ref('');
  const name = ref('');
  const changeTree = (data, node) => {
    currentId.value = data.id;
    name.value = data.title;
  };

  watch(currentId, (value) => {
    emit('update:modelValue', value);
    emit('get-name', name.value);
  });

  const treeProps = {
    label: 'title',
    isLeaf: (data, node) => {
      return data.childrenCount <= 0;
    },
  };
  const loadNode = async (node, resolve) => {
    if (node.level === 0) {
      const data = await fetchTop();
      if (data?.length) {
        currentId.value = data[0].id;
        name.value = data[0].title;
      }
      resolve(data);
    } else {
      const data = await fetchChild(node.data.id);
      resolve(data);
    }
  };

  const onRefresh = () => {
    showTree.value = false;
    nextTick(() => {
      showTree.value = true;
    });
  };

  async function fetchTop() {
    try {
      loading.value = true;
      const { data } = await getTopBasicCatalogues();
      return data;
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }
  async function fetchChild(id) {
    try {
      const { data } = await getChildCatalogue(id);
      return data;
    } catch (error) {
      console.log(error);
    }
  }
</script>

<style lang="scss" scoped>
  .tree-node-selected {
    background-color: #ebf4f7;
    color: #4492aa;
  }
</style>
