/*
 * @Description:数据浏览
 */
import { defineStore } from 'pinia';
import { reactive } from 'vue';

type StoreField = {
  id: string;
  data: number[];
};

export const useDataBrowse = defineStore(
  'dataBrowse',
  () => {
    const dataBrowse = reactive({
      id: '', //数据浏览左侧目录id
      fileds: [] as StoreField[], //订单选中的字段数组
    });

    const setData = (data) => {
      for (const key in data) {
        if (Object.hasOwnProperty.call(data, key)) {
          dataBrowse[key] = data[key];
        }
      }
    };

    const setFileds = (id: string, value: number[]) => {
      const item = dataBrowse.fileds.find((item) => item.id === id);
      if (item) {
        item.data = value;
      } else {
        dataBrowse.fileds.push({
          id,
          data: value,
        });
      }
    };

    const getFieldLength = (id: string) => {
      const item = dataBrowse.fileds.find((item) => item.id === id);
      return item?.data.length || 0;
    };

    return {
      dataBrowse,
      setData,
      setFileds,
      getFieldLength,
    };
  },
  {
    persist: false,
  }
);
