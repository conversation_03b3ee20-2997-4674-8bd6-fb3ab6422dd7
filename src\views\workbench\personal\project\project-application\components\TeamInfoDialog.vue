<template>
  <el-dialog v-model="show" title="选择团队" width="80%" destroy-on-close @close="onCancel">
    <template v-if="tableData.length > 0">
      <div class="text-tip mb-2">
        <span>没找到想要的数据？</span>
        <el-button link type="primary" @click="goTeam"> 去创建团队 </el-button>
      </div>

      <el-table
        ref="tableRef"
        :data="tableData"
        style="width: 100%"
        class="c-table-header"
        :header-cell-class-name="cellClass"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" :selectable="checkSelectable" />
        <el-table-column prop="name" label="团队名称" min-width="100px" />
        <el-table-column show-overflow-tooltip prop="address" label="通讯地址" />
        <el-table-column show-overflow-tooltip prop="description" label="团队描述" />
        <!-- <el-table-column prop="org" label="研究机构" />
        <el-table-column prop="leadResearcher" label="首席研究员" />
        <el-table-column label="合作者">
          <template #default="{ row }">
            <div v-for="(item, i) in row.cooperator" :key="i">
              <span>{{ item.name }}</span>
              <span v-if="row.deputy == i">(代表)</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="orgName" label="机构官方名称" />
        <el-table-column prop="orgContact" label="机构联系人" /> -->
      </el-table>
    </template>

    <div v-else class="text-tip text-center">
      <span>暂无团队数据，</span>
      <el-button link type="primary" @click="goTeam"> 去创建团队 </el-button>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="onCancel">取消</el-button>
        <el-button type="primary" :disabled="!checkList.length" :loading="saveLoading" @click="onSave">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { findAllTeam_02, newOrUpdateApplicationTeam } from '@/api';
  import { ElMessage, ElTable } from 'element-plus';
  import { useRouter } from 'vue-router';
  const router = useRouter();
  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
      required: true,
    },
    orgId: String,
    addedTeams: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
  });
  const show = ref(false);
  watchEffect(() => {
    show.value = props.modelValue;
  });

  const tableRef = ref<InstanceType<typeof ElTable>>();
  const loading = ref(false);
  const tableData = ref<any[]>([
    // {
    //   name: '我的团队1',
    //   org: 'XXXXXXX大学',
    //   leadResearcher: '<EMAIL>',
    //   cooperator: [{ name: '<EMAIL>' }, { name: '<EMAIL>' }, { name: '<EMAIL>' }],
    //   deputy: 0,
    //   orgName: 'XXXXXXXXXX机构',
    //   orgContact: '<EMAIL>',
    // },
  ]);
  //页码
  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  const total = ref(0);
  const handleCurrentChange = (e) => {
    pagination.page = e;
    fetchData(e);
  };
  const handleSizeChange = (e) => {
    pagination.pageSize = e;
    fetchData();
  };
  const cellClass = ({ columnIndex }: { columnIndex: number }) => {
    if (columnIndex === 0) {
      return 'disabled-check';
    }
    return '';
  };

  // 检查团队是否可以被选择（未被添加的团队才能选择）
  const checkSelectable = (row: any) => {
    return !props.addedTeams.some((team) => team.applicationTeamId?.teamId === row.id);
  };

  async function fetchData(pageNum = 1) {
    try {
      loading.value = true;
      const { data } = await findAllTeam_02({ pageNum, pageSize: pagination.pageSize });
      total.value = data?.totalElement ?? 0;
      pagination.page = pageNum;
      tableData.value = data?.content ?? [];
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  const checkList = ref<any[]>([]);
  const handleSelectionChange = (selection: any[]) => {
    if (selection.length > 1) {
      let del_row = selection.shift();
      tableRef.value?.toggleRowSelection(del_row, false);
    }
    checkList.value = selection;
  };
  const goTeam = () => {
    router.push({ name: 'WorkbenchMyTeam' });
  };

  const emit = defineEmits(['update:modelValue', 'success']);
  const onCancel = () => {
    emit('update:modelValue', false);
  };

  const saveLoading = ref(false);
  const onSave = async () => {
    try {
      saveLoading.value = true;
      await newOrUpdateApplicationTeam({
        applicationTeamId: { applicationId: Number(props.orgId), teamId: checkList.value[0].id },
      });
      ElMessage.success('添加成功');
      onCancel();
      emit('success', checkList.value);
    } catch (error) {
      console.log(error);
    } finally {
      saveLoading.value = false;
    }
  };

  onBeforeMount(() => {
    fetchData();
  });
</script>

<style lang="scss" scoped>
  // 深度选择器 去掉全选按钮
  :deep(.disabled-check .cell .el-checkbox__inner) {
    display: none;
  }
  :deep(.disabled-check .cell::before) {
    content: '';
    text-align: center;
    line-height: 37px;
  }
</style>
