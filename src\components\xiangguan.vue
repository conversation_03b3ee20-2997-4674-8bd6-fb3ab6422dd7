<template>
  <div class="flex h-full min-h-fit w-full flex-col justify-start rounded-lg bg-w p-[3%]">
    <div class="flex h-fit w-full justify-between">
      <span class="font-bold">相关新闻</span>
      <div>
        <span class="text-xs text-[#939899]">查看更多</span>
        <el-icon :size="10">
          <ArrowRightBold color="#939899" :size="20" />
        </el-icon>
      </div>
    </div>
    <div class="w-full p-[1%]">
      <ul>
        <li v-for="item in datali" :key="item.id" class="mt-3 flex w-full justify-between">
          <span class="mr-3 text-[xx-small] 2xl:text-sm">{{ item.text }}</span>
          <span class="text-[xx-small] text-[#939899] 2xl:text-sm">{{ item.date }}</span>
        </li>
      </ul>
    </div>
  </div>
</template>
<script setup>
  let datali = [
    {
      id: 1,
      text: '6 篇 Science 齐发 ， 首次破译完整的人类基因组 ， 百人科学家团队揭开背后的神秘面纱',
      date: '2022-03-12',
    },
    {
      id: 2,
      text: '全国肿瘤防治宣传周丨癌症防治早早行动 ， 癌症防治核心要点早知道 ！',
      date: '2022-03-12',
    },
    {
      id: 3,
      text: '老年人新冠疫苗接种科普问答',
      date: '2022-03-12',
    },
    {
      id: 4,
      text: '《 细胞 》 ： 环状 RNA 技术应对新冠突变 ， 魏文胜团队发表新型疫苗结果',
      date: '2022-03-12',
    },
    {
      id: 5,
      text: '关于发布推荐性卫生行业标准 《 妇幼保健机构医用设备配备标准 》 的通告',
      date: '2022-03-12',
    },
  ];
</script>
<style scoped>
  li {
    /* list-style-position:outside; */
    list-style: disc !important;
  }
  ul {
    list-style: disc !important;
    /* list-style-position:outside; */
  }
</style>
