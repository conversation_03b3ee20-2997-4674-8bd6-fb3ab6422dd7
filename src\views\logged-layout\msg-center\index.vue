<template>
  <div class="flex h-full flex-col pb-5">
    <h2 class="mb-5 flex h-[60px] cursor-pointer items-center bg-w pl-5 text-xl font-bold" @click="onBack">
      <el-icon class="mr-2" color="#939899">
        <ArrowLeft />
      </el-icon>
      消息中心
    </h2>

    <div class="mx-5 flex h-0 flex-1 flex-col rounded-md bg-w pt-5">
      <div class="flex items-center justify-between px-10">
        <!-- <el-button type="primary" @click="onSendMsg"> 消息发送 </el-button> -->

        <div>
          <el-button-group>
            <el-button plain @click="showDiffer('all')"> 全部 </el-button>
            <el-button plain @click="showDiffer(true)"> 已读 </el-button>
            <el-button plain @click="showDiffer(false)"> 未读 </el-button>
          </el-button-group>

          <el-input v-model="search" placeholder="请输入关键字" style="width: 480px" :suffix-icon="Search" class="ml-6">
            <template #prepend>
              <el-select v-model="value" style="width: 100px">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </template>
          </el-input>
        </div>
      </div>

      <div class="mt-3 h-0 w-full flex-1 px-10">
        <el-table
          :data="currentTableData(filterTableData)"
          :header-cell-style="{
            height: '48px',
            'background-color': '#F0F2F5',
            color: '#303333',
          }"
          :cell-style="changeCellStyle"
          height="100%"
        >
          <el-table-column fixed type="expand">
            <template #default="props">
              <div class="break-all bg-baf px-[60px] py-7 text-m">
                尊敬的用户，本款产品已升级至最新版本（1.14&1.15版本），此次升级新增功能包括：1.
                WAF实例配置升级，用户可以对新增的每个子域名进行独立配置和自定义规则配置；2.
                增加了IP黑/白名单的配置项；3. 增加子域名自动导入功能（BLB-WAF）；4.
                调整显示项目，优化使用体验；感谢您对产品的关注与支持，如有建议请与我们保持联系！XXXXXX团队2022年4月20日
              </div>
            </template>
          </el-table-column>
          <el-table-column label="主题" prop="topic" />
          <el-table-column label="发送人" prop="sender" />
          <el-table-column label="关联" prop="relevancy" />
          <el-table-column label="日期" prop="date" />
          <el-table-column label="操作" width="200">
            <template #default="{ row }">
              <el-button link type="primary" @click="onReply(row)"> 回复 </el-button>
              <el-button link type="primary" @click="onViewLog(row)"> 查看记录 </el-button>
              <el-popconfirm title="确定删除此项？" @confirm="onDel(row)">
                <template #reference>
                  <el-button link type="primary"> 删除 </el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="pagination-bottom">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :page-size="pagination.pageSize"
          :total="newTableData.length"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
  /* 消息中心 */
  import { Search } from '@element-plus/icons-vue';
  import { useRouter } from 'vue-router';
  const router = useRouter();

  onMounted(() => {
    showDiffer('all');
  });

  const tableData = [
    {
      topic: '4563223568心理疾病研究',
      sender: '张三',
      relevancy: '22134145525',
      date: '2022-03-12 12:00:00',
      state: 'California',
      city: 'San Francisco',
      address: '3650 21st St, San Francisco',
      zip: 'CA 94114',
      status: false,
    },
    {
      topic: '4563223568脑疾病研究',
      sender: 'Tom',
      relevancy: '25446552233',
      date: '2022-03-12 12:00:00',
      state: 'California',
      city: 'San Francisco',
      address: '3650 21st St, San Francisco',
      zip: 'CA 94114',
      status: true,
    },

    {
      topic: '4563223568脑疾病研究',
      sender: 'Tom',
      relevancy: '25446552233',
      date: '2022-03-12 12:00:00',
      state: 'California',
      city: 'San Francisco',
      address: '3650 21st St, San Francisco',
      zip: 'CA 94114',
      status: false,
    },
    {
      topic: '4563223568脑疾病研究',
      sender: 'Tom',
      relevancy: '25446552233',
      date: '2022-03-12 12:00:00',
      state: 'California',
      city: 'San Francisco',
      address: '3650 21st St, San Francisco',
      zip: 'CA 94114',
      status: true,
    },

    {
      topic: '4563223568脑疾病研究',
      sender: 'Tom',
      relevancy: '25446552233',
      date: '2022-03-12 12:00:00',
      state: 'California',
      city: 'San Francisco',
      address: '3650 21st St, San Francisco',
      zip: 'CA 94114',
      status: false,
    },
    {
      topic: '4563223568脑疾病研究',
      sender: 'Tom',
      relevancy: '25446552233',
      date: '2022-03-12 12:00:00',
      state: 'California',
      city: 'San Francisco',
      address: '3650 21st St, San Francisco',
      zip: 'CA 94114',
      status: true,
    },

    {
      topic: '4563223568脑疾病研究',
      sender: 'Tom',
      relevancy: '25446552233',
      date: '2022-03-12 12:00:00',
      state: 'California',
      city: 'San Francisco',
      address: '3650 21st St, San Francisco',
      zip: 'CA 94114',
      status: false,
    },
    {
      topic: '4563223568脑疾病研究',
      sender: 'Tom',
      relevancy: '25446552233',
      date: '2022-03-12 12:00:00',
      state: 'California',
      city: 'San Francisco',
      address: '3650 21st St, San Francisco',
      zip: 'CA 94114',
      status: true,
    },

    {
      topic: '4563223568脑疾病研究',
      sender: 'Tom',
      relevancy: '25446552233',
      date: '2022-03-12 12:00:00',
      state: 'California',
      city: 'San Francisco',
      address: '3650 21st St, San Francisco',
      zip: 'CA 94114',
      status: false,
    },
    {
      topic: '4563223568脑疾病研究',
      sender: 'Tom',
      relevancy: '25446552233',
      date: '2022-03-12 12:00:00',
      state: 'California',
      city: 'San Francisco',
      address: '3650 21st St, San Francisco',
      zip: 'CA 94114',
      status: true,
    },

    {
      topic: '4563223568脑疾病研究',
      sender: 'Tom',
      relevancy: '25446552233',
      date: '2022-03-12 12:00:00',
      state: 'California',
      city: 'San Francisco',
      address: '3650 21st St, San Francisco',
      zip: 'CA 94114',
      status: false,
    },
    {
      topic: '4563223568脑疾病研究',
      sender: 'Tom',
      relevancy: '25446552233',
      date: '2022-03-12 12:00:00',
      state: 'California',
      city: 'San Francisco',
      address: '3650 21st St, San Francisco',
      zip: 'CA 94114',
      status: true,
    },

    {
      topic: '4563223568脑疾病研究',
      sender: 'Tom',
      relevancy: '25446552233',
      date: '2022-03-12 12:00:00',
      state: 'California',
      city: 'San Francisco',
      address: '3650 21st St, San Francisco',
      zip: 'CA 94114',
      status: false,
    },
    {
      topic: '4563223568脑疾病研究',
      sender: 'Tom',
      relevancy: '25446552233',
      date: '2022-03-12 12:00:00',
      state: 'California',
      city: 'San Francisco',
      address: '3650 21st St, San Francisco',
      zip: 'CA 94114',
      status: true,
    },

    {
      topic: '4563223568脑疾病研究',
      sender: 'Tom',
      relevancy: '25446552233',
      date: '2022-03-12 12:00:00',
      state: 'California',
      city: 'San Francisco',
      address: '3650 21st St, San Francisco',
      zip: 'CA 94114',
      status: false,
    },
    {
      topic: '4563223568脑疾病研究',
      sender: 'Tom',
      relevancy: '25446552233',
      date: '2022-03-12 12:00:00',
      state: 'California',
      city: 'San Francisco',
      address: '3650 21st St, San Francisco',
      zip: 'CA 94114',
      status: true,
    },

    {
      topic: '4563223568脑疾病研究',
      sender: 'Tom',
      relevancy: '25446552233',
      date: '2022-03-12 12:00:00',
      state: 'California',
      city: 'San Francisco',
      address: '3650 21st St, San Francisco',
      zip: 'CA 94114',
      status: true,
    },
    {
      topic: '4563223568脑疾病研究',
      sender: 'Tom',
      relevancy: '25446552233',
      date: '2022-03-12 12:00:00',
      state: 'California',
      city: 'San Francisco',
      address: '3650 21st St, San Francisco',
      zip: 'CA 94114',
      status: false,
    },

    {
      topic: '4563223568脑疾病研究',
      sender: 'Tom',
      relevancy: '25446552233',
      date: '2022-03-12 12:00:00',
      state: 'California',
      city: 'San Francisco',
      address: '3650 21st St, San Francisco',
      zip: 'CA 94114',
      status: true,
    },
    {
      topic: '4563223568脑疾病研究',
      sender: 'Tom',
      relevancy: '25446552233',
      date: '2022-03-12 12:00:00',
      state: 'California',
      city: 'San Francisco',
      address: '3650 21st St, San Francisco',
      zip: 'CA 94114',
      status: true,
    },

    {
      topic: '4563223568脑疾病研究',
      sender: 'Tom',
      relevancy: '25446552233',
      date: '2022-03-12 12:00:00',
      state: 'California',
      city: 'San Francisco',
      address: '3650 21st St, San Francisco',
      zip: 'CA 94114',
      status: false,
    },
    {
      topic: '4563223568脑疾病研究',
      sender: 'Tom',
      relevancy: '25446552233',
      date: '2022-03-12 12:00:00',
      state: 'California',
      city: 'San Francisco',
      address: '3650 21st St, San Francisco',
      zip: 'CA 94114',
      status: false,
    },

    {
      topic: '4563223568脑疾病研究',
      sender: 'Tom',
      relevancy: '25446552233',
      date: '2022-03-12 12:00:00',
      state: 'California',
      city: 'San Francisco',
      address: '3650 21st St, San Francisco',
      zip: 'CA 94114',
      status: true,
    },
    {
      topic: '4563223568脑疾病研究',
      sender: 'Tom',
      relevancy: '25446552233',
      date: '2022-03-12 12:00:00',
      state: 'California',
      city: 'San Francisco',
      address: '3650 21st St, San Francisco',
      zip: 'CA 94114',
      status: true,
    },

    {
      topic: '4563223568脑疾病研究',
      sender: 'Tom',
      relevancy: '25446552233',
      date: '2022-03-12 12:00:00',
      state: 'California',
      city: 'San Francisco',
      address: '3650 21st St, San Francisco',
      zip: 'CA 94114',
      status: false,
    },
    {
      topic: '4563223568脑疾病研究',
      sender: 'Tom',
      relevancy: '25446552233',
      date: '2022-03-12 12:00:00',
      state: 'California',
      city: 'San Francisco',
      address: '3650 21st St, San Francisco',
      zip: 'CA 94114',
      status: true,
    },

    {
      topic: '4563223568脑疾病研究',
      sender: 'Tom',
      relevancy: '25446552233',
      date: '2022-03-12 12:00:00',
      state: 'California',
      city: 'San Francisco',
      address: '3650 21st St, San Francisco',
      zip: 'CA 94114',
      status: true,
    },
    {
      topic: '4563223568脑疾病研究',
      sender: 'Tom',
      relevancy: '25446552233',
      date: '2022-03-12 12:00:00',
      state: 'California',
      city: 'San Francisco',
      address: '3650 21st St, San Francisco',
      zip: 'CA 94114',
      status: true,
    },

    {
      topic: '4563223568脑疾病研究',
      sender: 'Tom',
      relevancy: '25446552233',
      date: '2022-03-12 12:00:00',
      state: 'California',
      city: 'San Francisco',
      address: '3650 21st St, San Francisco',
      zip: 'CA 94114',
      status: true,
    },
    {
      topic: '4563223568脑疾病研究',
      sender: 'Tom',
      relevancy: '25446552233',
      date: '2022-03-12 12:00:00',
      state: 'California',
      city: 'San Francisco',
      address: '3650 21st St, San Francisco',
      zip: 'CA 94114',
      status: true,
    },

    {
      topic: '4563223568脑疾病研究',
      sender: 'Tom',
      relevancy: '25446552233',
      date: '2022-03-12 12:00:00',
      state: 'California',
      city: 'San Francisco',
      address: '3650 21st St, San Francisco',
      zip: 'CA 94114',
      status: true,
    },
    {
      topic: '4563223568脑疾病研究',
      sender: 'Tom',
      relevancy: '25446552233',
      date: '2022-03-12 12:00:00',
      state: 'California',
      city: 'San Francisco',
      address: '3650 21st St, San Francisco',
      zip: 'CA 94114',
      status: true,
    },

    {
      topic: '4563223568脑疾病研究',
      sender: 'Tom',
      relevancy: '25446552233',
      date: '2022-03-12 12:00:00',
      state: 'California',
      city: 'San Francisco',
      address: '3650 21st St, San Francisco',
      zip: 'CA 94114',
      status: true,
    },
    {
      topic: '4563223568脑疾病研究',
      sender: 'Tom',
      relevancy: '25446552233',
      date: '2022-03-12 12:00:00',
      state: 'California',
      city: 'San Francisco',
      address: '3650 21st St, San Francisco',
      zip: 'CA 94114',
      status: false,
    },

    {
      topic: '4563223568脑疾病研究',
      sender: 'Tom',
      relevancy: '25446552233',
      date: '2022-03-12 12:00:00',
      state: 'California',
      city: 'San Francisco',
      address: '3650 21st St, San Francisco',
      zip: 'CA 94114',
      status: true,
    },
    {
      topic: '4563223568脑疾病研究',
      sender: 'Tom',
      relevancy: '25446552233',
      date: '2022-03-12 12:00:00',
      state: 'California',
      city: 'San Francisco',
      address: '3650 21st St, San Francisco',
      zip: 'CA 94114',
      status: false,
    },

    {
      topic: '4563223568脑疾病研究',
      sender: 'Tom',
      relevancy: '25446552233',
      date: '2022-03-12 12:00:00',
      state: 'California',
      city: 'San Francisco',
      address: '3650 21st St, San Francisco',
      zip: 'CA 94114',
      status: true,
    },
  ];

  const search = ref('');
  const value = ref('topic');
  const options = [
    {
      value: 'topic',
      label: '主题',
    },
    {
      value: 'sender',
      label: '发送人',
    },
    {
      value: 'relevancy',
      label: '关联',
    },
    {
      value: 'date',
      label: '日期',
    },
  ];

  const newTableData = ref([]);
  const showDiffer = (choice) => {
    if (choice == 'all') newTableData.value = tableData;
    else {
      newTableData.value = tableData.filter((el) => {
        return el.status == choice;
      });
    }
  };
  const filterTableData = computed(() =>
    newTableData.value.filter(
      (data) => !search.value || data[value.value].toLowerCase().includes(search.value.toLowerCase())
    )
  );

  function changeCellStyle(row) {
    const styleObject = {};
    if (row.row.status) styleObject.color = '#939899';
    else styleObject.color = '#303333';
    return styleObject;
  }

  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  //前端限制分页（currentData为当前展示页表格）
  const currentTableData = (currentData) => {
    return currentData.filter(
      (item, index) =>
        index < pagination.page * pagination.pageSize && index >= pagination.pageSize * (pagination.page - 1)
    );
  };
  //改变页码
  const handleCurrentChange = (e) => {
    pagination.page = e;
  };
  //改变页数限制
  const handleSizeChange = (e) => {
    pagination.pageSize = e;
  };

  //消息发送
  const onSendMsg = () => {};

  //回复
  const onReply = (row) => {};
  //查看记录
  const onViewLog = (row) => {};
  //删除
  const onDel = (row) => {};
  const onBack = () => {
    router.back();
  };
</script>
