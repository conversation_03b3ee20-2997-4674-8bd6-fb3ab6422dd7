<template>
  <div class="flex h-full flex-col overflow-hidden">
    <div class="box-shadow bg-w pb-5">
      <h2 class="flex h-[60px] w-fit cursor-pointer items-center pl-5 text-xl font-semibold" @click="onBackHome">
        <el-icon class="mr-2" color="#939899">
          <ArrowLeft />
        </el-icon>
        <span>任务配置</span>
      </h2>

      <el-steps :active="active" finish-status="success" class="c-steps" align-center>
        <el-step v-for="(item, index) in steps" :key="index" :title="item" />
      </el-steps>
    </div>

    <div class="h-0 flex-1">
      <StepBase v-if="active === 0" @success="onSuccess" />
      <StepRule v-if="active === 1" @success="onSuccess" />
      <StepCleaning v-if="active === 2" @success="onSuccess" />
      <StepRelationship v-if="active === 3" @success="onSuccess" />
      <StepStrategy v-if="active === 4" @success="onSuccess" />
    </div>
  </div>
</template>

<script setup>
  import StepBase from './components/StepBase.vue';
  import StepRule from './components/StepRule.vue';
  import StepCleaning from './components/StepCleaning.vue';
  import StepRelationship from './components/StepRelationship.vue';
  import StepStrategy from './components/StepStrategy.vue';
  import { useRouter } from 'vue-router';
  const router = useRouter();
  const onBackHome = () => {
    router.back();
  };

  const active = ref(0);
  const steps = ['基本信息', '配置规则', '清洗规则配置', '配置映射关系', '配置调度策略'];
  const onSuccess = (step) => {
    active.value += step;
  };
</script>

<style lang="scss" scoped>
  :deep(.c-steps) {
    --el-color-success: #007f99;
    padding-left: 200px;
    padding-right: 200px;
    .el-step__line {
      background-color: #c8cbcd;
      height: 1px;
    }
    .el-step__icon.is-text {
      border-width: 1px;
    }
    .el-step__head.is-process {
      .el-step__icon.is-text {
        background: $color-primary;
        color: #fff;
        border-color: $color-primary;
      }
    }
  }
</style>
