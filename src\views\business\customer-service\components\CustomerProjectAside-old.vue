<template>
  <div class="flex h-full w-[400px] flex-col border-r border-border">
    <el-tabs v-model="tabName" class="tabs">
      <el-tab-pane label="全部" name="全部" />
      <el-tab-pane label="待审批" name="待审批" />
      <el-tab-pane label="已审批" name="已审批" />
    </el-tabs>

    <el-scrollbar class="h-0 flex-1">
      <div class="overflow-hidden px-5 pt-5">
        <el-input v-model="search" placeholder="请输入关键字">
          <template #prepend>
            <el-select v-model="prepend" style="width: 100px">
              <el-option v-for="(item, index) in prependOptions" :key="index" :label="item.value" :value="item.value" />
            </el-select>
          </template>
          <template #append>
            <el-button :icon="Search" @click="onSearch" />
          </template>
        </el-input>

        <ul class="list mt-5 text-sm">
          <li
            v-for="(item, index) in list"
            :key="index"
            class="mb-3 cursor-pointer rounded border border-border p-4 hover:shadow-md"
            :class="active == index ? 'active' : ''"
            @click="selectItem(item, index)"
          >
            <div class="flex items-center justify-between">
              <span class="text-base font-bold">{{ item.title }}</span>
              <div :class="statusClass[item.status]" class="h-6 px-3 leading-6">
                <span>{{ statusText[item.status] }}</span>
              </div>
            </div>

            <div class="mt-2">
              <span class="text-[#aeb1b2]">申请ID</span>
              <span class="ml-2 text-[#595e5f]">{{ item.applyId }}</span>
            </div>
            <div class="mt-2">
              <span class="text-[#aeb1b2]">项目名称</span>
              <span class="ml-2 text-[#595e5f]">{{ item.name }}</span>
            </div>
            <div class="mt-2">
              <span class="text-[#aeb1b2]">申请人</span>
              <span class="ml-2 text-[#595e5f]">{{ item.applicant }}</span>
            </div>
            <div class="mt-2">
              <span class="text-[#aeb1b2]">{{ item.startDate }}</span>
            </div>
          </li>
        </ul>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup>
  import { Search } from '@element-plus/icons-vue';
  const props = defineProps({ id: { type: [String, Number], default: '' } });

  const tabName = ref('待审批');
  const search = ref('');
  const prepend = ref('申请ID');
  const prependOptions = [
    {
      label: '申请ID',
      value: '申请ID',
    },
    {
      label: '项目名称',
      value: '项目名称',
    },
    {
      label: '申请人',
      value: '申请人',
    },
  ];
  const onSearch = () => {
    console.log(prepend.value);
    console.log(search.value);
  };

  const statusText = {
    0: '待审批',
    1: '通过',
    2: '驳回',
  };
  const statusClass = {
    0: 'status-todo',
    1: 'status-pass',
    2: 'status-reject',
  };
  let allList = ref([
    {
      id: '1',
      title: '项目申请',
      status: 0,
      applyId: '45563',
      name: '脑疾病研究',
      applicant: '张三',
      startDate: '2024-1-17 16:25:51',
      target:
        '国家脑疾病临床大数据平台是我国首个面向脑疾病临床数据的大规模平台，提供了数据存储、获取、展示、分析等一系列功能，方便研究人员进行研究，促进脑疾病研究发展。下设数据资源库、工作台、智能分析平台，分别为***功能。',
      background:
        '国家脑疾病临床大数据平台是我国首个面向脑疾病临床数据的大规模平台，提供了数据存储、获取、展示、分析等一系列功能，方便研究人员进行研究，促进脑疾病研究发展。下设数据资源库、工作台、智能分析平台，分别为***功能。国家脑疾病临床大数据平台是我国首个面向脑疾病临床数据的大规模平台，提供了数据存储、获取、展示、分析等一系列功能。',
      method:
        '国家脑疾病临床大数据平台是我国首个面向脑疾病临床数据的大规模平台，提供了数据存储、获取、展示、分析等一系列功能，方便研究人员进行研究，促进脑疾病研究发展。下设数据资源库、工作台、智能分析平台，分别为***功能。国家脑疾病临床大数据平台是我国首个面向脑疾病临床数据的大规模平台，提供了数据存储、获取、展示、分析等一系列功能。',
      type: '（1）国家脑疾病临床大数据平台是我国首个面向脑疾病临床数据的大规模平台（2）国家脑疾病临床大数据平台是我国首个面向脑疾病临床数据的大规模平台，提供了数据存储、获取、展示、分析等一系列功能。（3）提供了数据存储、获取、展示、分析等一系列功能',
      value: 'xx',
      keyword: 'xx',
      abstract: 'xx',
      haveNewData: true,
      newDataIntro: 'xx',
      month: 10,
    },
    {
      id: '2',
      title: '项目申请',
      status: 1,
      applyId: '45564',
      name: '肿瘤研究',
      applicant: '李四',
      startDate: '2024-1-17 16:25:51',
      target: 'xx',
      background: 'xx',
      method: 'xx',
      type: 'xx',
      value: 'xx',
      keyword: 'xx',
      abstract: 'xx',
      haveNewData: true,
      newDataIntro: 'xx',
      month: 5,
    },
    {
      id: '3',
      title: '项目申请',
      status: 2,
      applyId: '45564',
      name: '肿瘤研究',
      applicant: '李四',
      startDate: '2024-1-17 16:25:51',
      target: 'xx',
      background: 'xx',
      method: 'xx',
      type: 'xx',
      value: 'xx',
      keyword: 'xx',
      abstract: 'xx',
      haveNewData: true,
      newDataIntro: 'xx',
      month: 5,
    },
    {
      id: '4',
      title: '项目申请',
      status: 0,
      applyId: '45564',
      name: '肿瘤研究',
      applicant: '李四',
      startDate: '2024-1-17 16:25:51',
      target: 'xx',
      background: 'xx',
      method: 'xx',
      type: 'xx',
      value: 'xx',
      keyword: 'xx',
      abstract: 'xx',
      haveNewData: true,
      newDataIntro: 'xx',
      month: 5,
    },
  ]);
  const list = computed(() => {
    let result = [];
    switch (tabName.value) {
      case '全部':
        result = allList.value;
        break;
      case '待审批':
        result = allList.value.filter((item) => item.status === 0);
        break;
      case '已审批':
        result = allList.value.filter((item) => item.status === 1 || item.status === 2);
        break;
    }
    return result;
  });
  let active = ref(0);
  const emit = defineEmits(['select']);
  let selectItem = (item, index) => {
    active.value = index;
    emit('select', item);
  };
  let selectValue = allList.value[0];
  if (props.id) {
    allList.value.forEach((item, index) => {
      if (item.id == props.id) {
        selectValue = item;
        active.value = index;
      }
    });
  }
  selectItem(selectValue, active.value);
</script>

<style scoped lang="scss">
  .tabs {
    --el-border-color-light: #e1e3e6;
    :deep(.el-tabs__header) {
      margin: 0;
    }
  }

  :deep(.el-tabs--top .el-tabs__item.is-top:nth-child(2)) {
    padding-left: 20px;
  }

  .list {
    .active {
      border-color: $color-primary;
    }

    .status-todo {
      color: #3a73e6;
      background-color: #ebf1fd;
    }

    .status-pass {
      color: #29b586;
      background-color: #e8f7f2;
    }

    .status-reject {
      color: #e64848;
      background-color: #fdecec;
    }
  }
</style>
