<template>
  <el-select v-model="modelValue" placeholder="请选择角色" clearable filterable multiple>
    <el-option v-for="item in roles" :key="item.id" :label="item.roleName" :value="item.id!" />
  </el-select>
</template>

<script setup lang="ts">
  import { ref, watch } from 'vue';
  import { findAllRole } from '@/api';

  const modelValue = defineModel<string[]>({ required: true });

  const emit = defineEmits(['update:modelValue']);

  const roles = ref<RoleVO[]>([]);

  // 获取角色列表
  async function fetchRoles() {
    try {
      const { data } = await findAllRole(1, 99);
      roles.value = data?.content || [];
    } catch (error) {
      console.error(error);
    }
  }

  fetchRoles();
</script>
