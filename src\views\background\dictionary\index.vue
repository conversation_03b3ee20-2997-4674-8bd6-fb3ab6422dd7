<template>
  <div class="flex h-full flex-col overflow-hidden bg-baf">
    <div v-loading="loading" class="m-5 flex h-0 flex-1 flex-col rounded-md bg-w pt-5">
      <div class="flex gap-4 px-10">
        <el-select v-model="state" placeholder="请选择状态" style="width: 150px" clearable @change="onStateChange">
          <el-option v-for="item in ['启用', '停用']" :key="item" :label="item" :value="item"> </el-option>
        </el-select>
        <el-button type="primary" @click="onAdd">新增</el-button>

        <!-- <el-input
          v-model="search"
          placeholder="请输入字典名称搜索"
          style="width: 300px"
          clearable
          @clear="onSearch"
          @keyup.enter="onSearch"
        >
          <template #append>
            <el-button :icon="Search" @click="onSearch" />
          </template>
        </el-input> -->
      </div>

      <div class="mt-3 h-0 w-full flex-1 px-10">
        <el-table height="100%" :data="tableData" style="width: 100%" class="c-table-header">
          <el-table-column type="index" width="60" label="序号" />
          <el-table-column prop="title" label="字典名称" />
          <el-table-column prop="dictionaryCode" label="字典编码" />
          <el-table-column prop="state" label="状态">
            <template #default="{ row }">
              <el-tag :type="row.state === '启用' ? 'success' : 'danger'">{{
                // row.state === '启用' ? '启用' : '禁用'
                row.state
              }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="sortOrder" label="排序" />
          <el-table-column fixed="right" label="操作" width="120px">
            <template #default="{ row }">
              <el-tooltip content="字典值" effect="dark">
                <el-button link type="primary" icon="notebook" @click="onViewDetail(row)" />
              </el-tooltip>
              <el-tooltip content="编辑" effect="dark">
                <el-button link type="primary" icon="edit" @click="onEdit(row)" />
              </el-tooltip>
              <el-popconfirm title="确定删除？" @confirm="onDel(row)">
                <template #reference>
                  <span class="ml-3">
                    <el-tooltip content="删除" effect="dark">
                      <el-button link icon="delete" type="primary" />
                    </el-tooltip>
                  </span>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="pagination-bottom">
        <el-pagination
          background
          layout="total, prev, pager, next, jumper"
          :page-size="pagination.pageSize"
          :total="total"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <el-dialog v-model="showAdd" :title="formTitle" width="600px" @close="onAddClose">
      <el-form ref="formRef" :model="addForm" :rules="rules" label-width="140px">
        <el-form-item label="字典名称" prop="title">
          <el-input v-model="addForm.title" placeholder="请输入字典名称" />
        </el-form-item>
        <el-form-item label="字典编码" prop="dictionaryCode">
          <el-input
            v-model="addForm.dictionaryCode"
            :disabled="addForm.id ? true : false"
            placeholder="请输入字典编码"
          />
        </el-form-item>
        <el-form-item label="状态" prop="state">
          <el-switch
            v-model="addForm.state"
            active-value="启用"
            inactive-value="停用"
            active-text="启用"
            inactive-text="停用"
            inline-prompt
          >
          </el-switch>
        </el-form-item>
        <el-form-item label="排序" prop="sortOrder">
          <el-input-number v-model="addForm.sortOrder" :min="1" :step="1" :controls="true"> </el-input-number>
        </el-form-item>
        <el-form-item label="备注" prop="description">
          <el-input
            v-model="addForm.description"
            type="textarea"
            placeholder="请输入备注"
            :maxlength="200"
            :show-word-limit="false"
            :autosize="{ minRows: 4, maxRows: 8 }"
          >
          </el-input>
        </el-form-item>
      </el-form>

      <template #footer>
        <span>
          <el-button @click="onAddClose">取消</el-button>
          <el-button type="primary" :loading="addLoading" @click="onAddConfirm"> 确定 </el-button>
        </span>
      </template>
    </el-dialog>

    <DictionaryValue :id="dictionaryId" v-model="showValue" />
  </div>
</template>

<script setup lang="ts">
  import { deleteEntityById_42, findAll_13, findByState_1, newOrUpdateEntity_13 } from '@/api';
  import DictionaryValue from './DictionaryValue.vue';
  import { ElMessage, FormInstance } from 'element-plus';

  const loading = ref(false);
  const search = ref('');
  const onSearch = () => {
    pagination.page = 1;
    fetchData();
  };

  //---------新增字典-----------
  const formRef = ref<FormInstance>();
  const addForm = reactive<DictionaryDTO>({
    id: 0,
    title: '',
    dictionaryCode: '',
    state: '启用',
    sortOrder: 100,
    description: '',
  });
  const rules = ref({
    title: [{ required: true, message: '不能为空' }],
    dictionaryCode: [{ required: true, message: '不能为空' }],
    state: [{ required: true, message: '不能为空' }],
    sortOrder: [{ required: true, message: '不能为空' }],
  });
  const showAdd = ref(false);
  const addLoading = ref(false);
  const formTitle = computed(() => (addForm.id ? '编辑字典' : '新增字典'));

  const onAdd = () => {
    addForm.id = 0;
    showAdd.value = true;
  };
  const onEdit = (row: FileInfoVO) => {
    showAdd.value = true;
    nextTick(() => {
      Object.assign(addForm, row);
    });
  };
  const onAddClose = () => {
    showAdd.value = false;
    formRef.value?.resetFields();
  };
  const onAddConfirm = () => {
    formRef.value?.validate(async (valid) => {
      try {
        if (valid) {
          addLoading.value = true;
          await newOrUpdateEntity_13(addForm);
          ElMessage({ type: 'success', message: '操作成功' });
          onAddClose();
          fetchData();
        }
      } catch (error) {
        console.log(error);
      } finally {
        addLoading.value = false;
      }
    });
  };

  //----------字典列表----------
  const tableData = ref<DictionaryDTO[]>([]);
  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  const total = ref(0);
  async function fetchData() {
    try {
      loading.value = true;
      let data: any;
      if (state.value) {
        const res = await findByState_1(pagination.page, pagination.pageSize, { state: state.value } as any);
        data = res.data;
      } else {
        const res = await findAll_13(pagination.page, pagination.pageSize);
        data = res.data;
      }
      total.value = data.totalElement;
      tableData.value = data.content || [];
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }
  const handleCurrentChange = (e) => {
    pagination.page = e;
    fetchData();
  };

  //状态
  const state = ref('');
  const onStateChange = () => {
    fetchData();
  };

  //删除
  async function onDel(row: FileInfoVO) {
    try {
      loading.value = true;
      await deleteEntityById_42(row.id!);
      ElMessage({ type: 'success', message: '删除成功' });
      fetchData();
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  //---------字典值------------
  const showValue = ref(false);
  const dictionaryId = ref(0);
  const onViewDetail = (row) => {
    showValue.value = true;
    dictionaryId.value = row.id;
  };

  onBeforeMount(() => {
    pagination.page = 1;
    fetchData();
  });
</script>
