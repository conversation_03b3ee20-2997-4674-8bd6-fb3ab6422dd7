<template>
  <div class="flex h-full w-[400px] flex-col border-r border-border">
    <el-tabs v-model="tabName" class="tabs" @tab-change="tabChange">
      <el-tab-pane label="全部" name="全部" />
      <el-tab-pane label="待审批" name="待审批" />
      <el-tab-pane label="已审批" name="已审批" />
    </el-tabs>

    <el-scrollbar v-loading="loading" class="h-0 flex-1">
      <div class="overflow-hidden px-5 pt-5">
        <!-- <el-input v-model="search" placeholder="请输入关键字">
          <template #prepend>
            <el-select v-model="prepend" style="width: 100px">
              <el-option v-for="(item, index) in prependOptions" :key="index" :label="item.value" :value="item.value" />
            </el-select>
          </template>
          <template #append>
            <el-button :icon="Search" @click="onSearch" />
          </template>
        </el-input> -->

        <ul v-if="list.length" class="list text-sm">
          <li
            v-for="(item, index) in list"
            :key="index"
            class="mb-3 cursor-pointer rounded border border-border p-4 hover:shadow-md"
            :class="active == item.id ? 'active' : ''"
            @click="selectItem(item)"
          >
            <div class="flex items-center justify-between">
              <span class="text-base font-bold">{{ item.title }}</span>
              <div :class="statusClass[item.rltSybole.lockFlag]" class="h-6 px-3 leading-6">
                <span>{{ statusText[item.rltSybole.lockFlag] }}</span>
              </div>
            </div>

            <div class="mt-2">
              <span class="text-[#aeb1b2]">用户名</span>
              <span class="ml-2 text-[#595e5f]">{{ item.userName }}</span>
            </div>
            <div class="mt-2">
              <span class="text-[#aeb1b2]">邮箱</span>
              <span class="ml-2 text-[#595e5f]">{{ item.email }}</span>
            </div>
            <div class="mt-2">
              <span class="text-[#aeb1b2]">联系方式</span>
              <span class="ml-2 text-[#595e5f]">{{ item.phone }}</span>
            </div>
            <div class="mt-2">
              <span class="text-[#aeb1b2]">{{ item.rltTime.updateTime }}</span>
            </div>
          </li>
        </ul>
        <el-empty v-else description="暂无数据" />
      </div>
    </el-scrollbar>

    <div v-if="total" class="flex justify-center bg-w py-4">
      <el-pagination
        background
        layout="total, prev, pager, next"
        :page-size="pagination.pageSize"
        :total="total"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { findEnrollProcessVerify, findEnrollProcessing, findEnrollVerify } from '@/api/index';
  import { Search } from '@element-plus/icons-vue';
  import { useUsers } from '@/store/user-info.js';
  import { TabPaneName } from 'element-plus';
  const store = useUsers();
  const props = defineProps({ id: { type: [String, Number] } });

  const loading = ref(false);
  const search = ref('');
  const prepend = ref('用户名');
  const prependOptions = [
    {
      label: '用户名',
      value: '用户名',
    },
    {
      label: '邮箱',
      value: '邮箱',
    },
    {
      label: '联系方式',
      value: '联系方式',
    },
  ];
  const onSearch = () => {
    console.log(prepend.value);
    console.log(search.value);
  };

  const statusText = {
    0: '通过',
    1: '待审批',
    3: '驳回',
  };
  const statusClass = {
    0: 'status-pass',
    1: 'status-todo',
    3: 'status-reject',
  };
  let list = ref<any[]>([
    // {
    //   id: 1,
    //   title: '新用户注册申请',
    //   status: 0,
    //   userName: '张三',
    //   email: '<EMAIL>',
    //   phone: '15111112222',
    //   startDate: '2022-08-11 10:00:00',
    //   realName: '李桂芬',
    //   sex: '女',
    //   degree: '本科',
    //   jobTitle: '高级工程师',
    //   documentType: '身份证',
    //   idNumber: '450833198807085672',
    // },
  ]);

  //当前选中的信息
  let active = ref('0');
  const emit = defineEmits<{ select: [any] }>();
  const selectItem = (item) => {
    active.value = item.id;
    emit('select', item);
  };

  async function fetchData(page = 1, tabChange = true) {
    try {
      loading.value = true;
      let res: any = null;
      switch (tabName.value) {
        case '全部':
          res = await findEnrollProcessVerify('1', page, pagination.pageSize, {} as any);
          break;
        case '待审批':
          res = await findEnrollProcessing('1', page, pagination.pageSize, {} as any);
          break;
        case '已审批':
          res = await findEnrollVerify('1', page, pagination.pageSize, {} as any);
          break;
      }
      const data: any = res.data;
      list.value = data.content.map((item) => {
        item.title = '新用户注册申请';
        return item;
      });
      pagination.page = page;
      total.value = data.totalElement;
      if (list.value.length > 0) {
        if (tabChange) {
          selectItem(list.value[0]);
        } else {
          const item = list.value.find((item) => item.id == active.value);
          if (item) {
            selectItem(item);
          }
        }
      } else {
        selectItem({ id: 0, rltSybole: { lockFlag: '1' } });
      }
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  const tabName = ref('待审批');
  const tabChange = (name: TabPaneName) => {
    fetchData();
  };

  //分页
  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  const total = ref(0);
  const handleCurrentChange = (value: number) => {
    pagination.page = value;
    fetchData(value, false);
  };

  onBeforeMount(() => {
    fetchData();
  });

  defineExpose({ fetchData });
</script>

<style scoped lang="scss">
  .tabs {
    --el-border-color-light: #e1e3e6;
    :deep(.el-tabs__header) {
      margin: 0;
    }
  }

  :deep(.el-tabs--top .el-tabs__item.is-top:nth-child(2)) {
    padding-left: 20px;
  }

  .list {
    .active {
      border-color: $color-primary;
    }

    .status-todo {
      color: #3a73e6;
      background-color: #ebf1fd;
    }

    .status-pass {
      color: #29b586;
      background-color: #e8f7f2;
    }

    .status-reject {
      color: #e64848;
      background-color: #fdecec;
    }
  }
</style>
