<template>
  <div v-loading="loading" class="data-field relative pb-9 pl-10 pr-[240px] pt-9">
    <div>
      <h2 class="text-[28px] font-bold">{{ name }}&nbsp;{{ id }}</h2>
      <div class="mt-5">
        <span class="mr-5 text-tip">描述</span>
        <span>{{ description }}</span>
      </div>
      <div class="mt-3 flex items-center">
        <span class="mr-5 text-tip">类别</span>
        <Breadcrumb :list="breadList" type="dataField" @click-item="onBreadClick" />
      </div>

      <div v-if="instance" class="mt-5 bg-baf p-4">
        <el-descriptions direction="horizontal" :column="4">
          <el-descriptions-item label="实例ID">
            {{ instance.instanceId }}
          </el-descriptions-item>
          <el-descriptions-item label="实例代码">
            {{ instance.instanceSerialNumber }}
          </el-descriptions-item>
          <el-descriptions-item label="实例说明">
            {{ instance.instanceDescription }}
          </el-descriptions-item>
          <el-descriptions-item label="参与者">
            {{ instance.participantCount }}
          </el-descriptions-item>
          <el-descriptions-item label="记录数">
            {{ instance.recordCount }}
          </el-descriptions-item>
          <el-descriptions-item label="最大长度">
            {{ instance.maxLength }}
          </el-descriptions-item>
          <el-descriptions-item label="最小长度">
            {{ instance.minLength }}
          </el-descriptions-item>
          <el-descriptions-item label="平均长度">
            {{ instance.averageLength }}
          </el-descriptions-item>
          <el-descriptions-item v-if="rltTime.createTime" label="发布日期">
            {{ rltTime.createTime }}
          </el-descriptions-item>
          <el-descriptions-item v-if="rltTime.updateTime" label="更新日期">
            {{ rltTime.updateTime }}
          </el-descriptions-item>
          <!-- <el-descriptions-item label="生效起始日期">{{ rltTime.startTime }}</el-descriptions-item>
          <el-descriptions-item label="生效结束日期">{{ rltTime.endTime }}</el-descriptions-item> -->
        </el-descriptions>
      </div>

      <div v-for="(item, index) in navList" :key="index" ref="itemRefs">
        <template v-if="item === '数据' && showData">
          <h3 class="mt-10 text-xl">数据</h3>
          <div class="mt-5 border border-border px-10 py-6 text-regular">
            <!-- <p>317,677 项数据可用，涵盖 317,677 名参与者，使用 Data-Coding 1007 编码。</p> -->
            <div class="h-[300px]">
              <Chart v-if="chineseMeaning === '性别'" :option="pieOption" />
              <Chart v-else :option="option" />
            </div>
            <p class="mt-4 text-tip">
              参与者/项目的计数最后更新于{{ rltTime.updateTime ? rltTime.updateTime : rltTime.createTime }}
            </p>
          </div>
        </template>

        <template v-if="item === '备注' && notes.id">
          <h3 ref="list1" class="mt-10 text-xl">备注</h3>
          <div class="mt-5 border border-border px-10 py-6 text-regular">
            <p>该字段表示有多少参与者可以访问电子邮件，这是从他们向 UK Biobank 提供电子邮件地址中推断出来的。</p>
            <ul class="my-3 list-disc pl-5">
              <li>持有的电子邮件地址在语法上不正确。</li>
              <li>在时事通讯或其他邮寄方式从 UK Biobank 发送到该地址时收到了退回失败消息。</li>
              <li>参与者已要求撤回或删除他们的电子邮件地址。</li>
            </ul>
            <p>请注意，实际的电子邮件地址由英国生物银行高度保密，不会提供给研究人员。</p>
          </div>
        </template>

        <template v-if="item === '所属类别' && tableData.length > 0">
          <h3 ref="list2" class="mt-[26px] text-xl">所属类别（{{ tableData.length }}）</h3>
          <el-table :data="tableData" style="width: 100%" class="c-table-header mt-4">
            <el-table-column prop="typeId" label="类别ID">
              <template #default="{ row }">
                <el-button link type="primary" @click="gotoType(row)">
                  {{ row.id }}
                </el-button>
              </template>
            </el-table-column>
            <el-table-column prop="title" label="名称" min-width="100px" />
            <el-table-column prop="dataItemCount" label="数据字段" />
            <el-table-column prop="childrenCount" label="子类别" />
            <el-table-column prop="description" label="说明" />
          </el-table>
        </template>
      </div>
    </div>

    <!-- <div class="fixed right-0 top-[96px] w-[200px]">
      <div class="text-tip">本页目录</div>
      <ul class="nav mt-3">
        <li
          v-for="(item, index) in navList"
          :key="index"
          class="cursor-pointer select-none border-l-2 border-border py-2 pl-5"
          :class="{ active: activeIndex === index }"
          @click="clickNav(index)"
        >
          {{ item }}
        </li>
      </ul>
    </div> -->
  </div>
</template>

<script setup>
  /* 数据字段 */
  import { getMedicalFieldDetail, findAlternativeByMedicalFieldId, findCatalogueVoBymdfId } from '@/api/index';
  import Chart from '@/components/Chart.vue';
  import Breadcrumb from './components/Breadcrumb.vue';

  const props = defineProps({
    id: {
      type: String,
    },
  });
  const emit = defineEmits(['goto-type']);

  const loading = ref(true);
  const navList = ref(['数据', '备注', '所属类别']);
  const activeIndex = ref(0);
  const itemRefs = ref([]);
  const clickNav = (i) => {
    activeIndex.value = i;
    itemRefs.value[i].scrollIntoView();
  };

  const option = reactive({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: function (params) {
        // params 是一个数组，每个元素代表一个系列的数据
        let result = '<div style="font-size: 14px;">';
        for (let i = 0; i < params.length; i++) {
          let param = params[i];
          result += `<p>${param.name}：${param.value}</p>`;
          result += `<p>${param.data.percentileFlag}</p>`;
        }
        result += '</div>';
        return result;
      },
    },
    grid: {
      left: 0,
      right: 0,
      bottom: 0,
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: [],
      boundaryGap: true,
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#565B5C',
      },
      axisLine: {
        lineStyle: {
          color: '#C8C9CC',
        },
      },
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        color: '#939899',
      },
    },
    color: ['#1296B3'],
    series: {
      type: 'bar',
      barMaxWidth: '50px',
      data: [],
      label: {
        show: true,
        position: 'top',
        // formatter: function (param) {
        //   if (param.value == '0') {
        //     return '';
        //   } else {
        //     return param.name + param.value;
        //   }
        // },
      },
    },
  });

  const pieOption = reactive({
    tooltip: {
      trigger: 'item',
    },
    series: {
      type: 'pie',
      radius: '70%',
      data: [],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)',
        },
      },
      label: {
        color: '#939899',
        formatter: '{b} \n\r{@[]}({d}%)',
      },
    },
  });

  fetchData();
  const name = ref('');
  const description = ref('');
  const breadList = ref([
    // { name: '人口特征', id: '1', type: '1' },
    // { name: '持续特征', id: '2', type: '2' },
  ]);
  const instance = ref({});
  const rltTime = ref({});
  const notes = ref({}); //备注
  const chineseMeaning = ref('');
  async function fetchData() {
    try {
      loading.value = true;
      const { data } = await getMedicalFieldDetail(props.id);
      name.value = data.name;
      description.value = data.description;
      breadList.value = data.fullPathCatalogue[data.fullPathCatalogue.length - 1]
        .map((item) => {
          item.name = item.title;
          return item;
        })
        .reverse();
      instance.value = data.instanceFieldStatistics[0];
      rltTime.value = data.rltTime;
      chineseMeaning.value = data.chineseMeaning;
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  fetchAlternative();
  const showData = ref(false); //数据
  async function fetchAlternative() {
    try {
      const { data } = await findAlternativeByMedicalFieldId(props.id);
      let xData = [];
      let sData = [];
      let pieData = [];
      data.forEach((item) => {
        xData.push(item.binMidpoint);
        sData.push({
          value: item.frequency || 0,
          percentileFlag: item.percentileFlag,
        });
        pieData.push({
          name: item.binMidpoint,
          value: item.frequency || 0,
        });
      });
      option.xAxis.data = xData;
      option.series.data = sData;
      pieOption.series.data = pieData;
      showData.value = data.length > 0;
    } catch (error) {
      console.log(error);
    }
  }

  fetchType();
  //父类别
  const tableData = ref([]);
  async function fetchType() {
    try {
      const { data } = await findCatalogueVoBymdfId(props.id);
      tableData.value = data;
    } catch (error) {
      console.log(error);
    }
  }

  //跳转类别页面
  const gotoType = (row) => {
    emit('goto-type', row.id);
  };
  const onBreadClick = (row) => {
    gotoType(row);
  };
</script>

<style lang="scss" scoped>
  :deep(.el-descriptions) {
    .el-descriptions__body {
      background-color: transparent;
    }
    .el-descriptions__label {
      color: $color-tip-text;
    }
  }

  .nav {
    .active {
      color: $color-primary;
      border-left: 2px solid $color-primary;
    }
  }
</style>
