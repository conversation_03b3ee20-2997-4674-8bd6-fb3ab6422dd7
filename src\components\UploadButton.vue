<template>
  <div :class="link ? 'mr-2 h-[22px]' : ''">
    <el-upload
      ref="uploadRef"
      v-model:file-list="fileList"
      :multiple="multiple"
      :show-file-list="showFileList"
      :auto-upload="false"
      :accept="accept"
      :limit="limit"
      :on-exceed="handleExceed"
      :on-change="onChange"
    >
      <el-button :loading="loading" :link="link" :type="type"> 上传文件 </el-button>
    </el-upload>
  </div>
</template>

<script setup lang="ts">
  import { ElMessage, UploadFile, UploadFiles, UploadUserFile } from 'element-plus';
  import { upload } from '@/utils/request';

  const fileList = defineModel<UploadUserFile[]>({ default: [] });
  const props = defineProps({
    accept: {
      type: String,
      default: '*', //".pdf, .doc, .docx, .xls, .xlsx"
    },
    limit: {
      type: Number,
      default: 20,
    },
    maxSize: {
      type: Number,
      default: 200,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    showFileList: {
      type: Boolean,
      default: true,
    },
    link: {
      type: Boolean,
      default: true,
    },
    type: {
      type: String,
      default: 'primary',
    },
    //上传的文件字段名
    name: {
      type: String,
      default: 'file',
    },
    autoUpload: {
      type: Boolean,
      default: true,
    }, //自动上传
    action: String, //请求 URL
  });
  const emit = defineEmits<{ success: [] }>();

  const loading = ref(false);
  const uploadRef = ref();
  const handleExceed = () => {
    ElMessage.warning(`文件数量不能超过${props.limit}个`);
  };
  const onChange = (file: UploadFile, files: UploadFiles) => {
    const { uid, name, size } = file;
    //文件类型限制
    if (props.accept !== '*') {
      const fileType = name.substring(name.lastIndexOf('.')).toLowerCase();
      const fileTypeFlag = props.accept.includes(fileType);
      if (!fileTypeFlag) {
        ElMessage.warning(`文件类型非法`);
        delFileByUid(uid);
        return;
      }
    }

    if (size! / 1024 / 1024 > props.maxSize) {
      ElMessage.warning(`文件大小不能超过${props.maxSize}MB`);
      delFileByUid(uid);
      return;
    }
    save(file, files);
  };

  function delFileByUid(uid: number) {
    const selectFileList = fileList.value.filter((item) => {
      return item.uid != uid;
    });
    fileList.value = selectFileList;
  }

  async function save(file: UploadFile, files: UploadFiles) {
    if (props.autoUpload && props.action) {
      try {
        loading.value = true;
        const formData = new FormData();
        // const jsonBlob = new Blob([JSON.stringify(file)], { type: 'application/json' });
        // formData.append(props.name, jsonBlob);
        formData.append(props.name, file.raw!);
        await upload(props.action, { method: 'post', data: formData });
        ElMessage({ type: 'success', message: '上传成功' });
        emit('success');
      } catch (error) {
        console.log(error);
      } finally {
        // uploadRef.value.clearFiles();
        fileList.value = [];
        loading.value = false;
      }
    } else {
      fileList.value = files;
      emit('success');
    }
  }
</script>
