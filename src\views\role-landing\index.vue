<template>
  <div class="h-full bg-[#eef1f7]">
    <ul class="flex flex-wrap gap-4 overflow-hidden p-5">
      <MenuCard v-for="item in filteredMenuItems" :key="item.title" v-bind="{ ...item }" />
    </ul>
  </div>
</template>

<script setup lang="ts">
  import type { RoleCode } from 'types/router';
  import MenuCard from './MenuCard.vue';
  import { useUsers } from '@/store/index';

  const store = useUsers();

  // 定义所有菜单项的配置
  const menuItems = [
    {
      title: '系统管理',
      description: '用户管理、角色管理、字典管理',
      imageSrc: '/images/landing/system.png',
      routeName: 'Background',
      bgGradient: 'from-[#4aa1fc] to-[#456dfa]',
      requiredRoles: ['ADMIN'],
    },
    {
      title: '数据资源管理',
      description: '数据集管理、数据脱敏管理',
      imageSrc: '/images/landing/resources.png',
      routeName: 'IntelligentHome',
      bgGradient: 'from-[#34d6fc] to-[#339ffe]',
      requiredRoles: ['RESOURCE_OPERATOR'],
    },
    {
      title: '用户业务管理',
      description: '新用户注册申请、新机构注册申请、项目申请',
      imageSrc: '/images/landing/user.png',
      routeName: 'BusinessCustomer',
      bgGradient: 'from-[#52e6a4] to-[#0cc5a2]',
      requiredRoles: ['USER_OPERATOR'],
    },
    {
      title: '专题库管理',
      description: '数据源管理、数据表管理',
      imageSrc: '/images/landing/thematic.png',
      routeName: 'ThematicDataSource',
      bgGradient: 'from-[#7a5dfc] to-[#5b3eff]',
      requiredRoles: ['THEME_OPERATOR'],
    },
    {
      title: '项目管理',
      description: '项目申请、项目管理、相关新闻',
      imageSrc: '/images/landing/project.png',
      routeName: 'Personal',
      bgGradient: 'from-[#ff7e67] to-[#ff4e5a]',
      requiredRoles: ['CUSTOMER'],
    },
  ];

  // 计算属性：过滤出符合条件的菜单项
  const filteredMenuItems = computed(() => {
    return menuItems.filter((item) => hasRole(item.requiredRoles as any, store.user.roleCode));
  });

  // 简化角色检查函数
  const hasRole = (allowedRoles: RoleCode[], userRoles: RoleCode[]) => {
    return allowedRoles.some((role) => userRoles.includes(role));
  };
</script>
