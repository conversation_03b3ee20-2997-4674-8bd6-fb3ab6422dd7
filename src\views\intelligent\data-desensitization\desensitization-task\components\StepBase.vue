<template>
  <div class="h-full p-5">
    <div class="flex h-full justify-center rounded-md bg-w">
      <div class="w-[600px] pt-10">
        <el-form ref="formRef" label-width="100" label-position="right" :model="form" :rules="rules">
          <el-form-item label="任务ID:" prop="taskId">
            <el-input v-model="form.taskId" placeholder="请输入任务ID" maxlength="50" disabled />
          </el-form-item>
          <el-form-item label="任务名称:" prop="name">
            <el-input v-model="form.name" placeholder="请输入任务名称" maxlength="50" />
          </el-form-item>
          <el-form-item label="任务描述:" prop="desc">
            <el-input
              v-model="form.desc"
              placeholder="请输入任务描述"
              maxlength="300"
              type="textarea"
              :rows="4"
              show-word-limit
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSave"> 下一步 </el-button>
            <el-button @click="onCancel"> 取消 </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { useRouter } from 'vue-router';
  const router = useRouter();

  const form = reactive({
    taskId: 'JDY214463256333',
    name: '',
    desc: '',
  });
  const rules = reactive({
    name: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
    desc: [{ required: true, message: '请输入任务描述', trigger: 'blur' }],
  });

  const formRef = ref();
  const onCancel = () => {
    router.back();
  };
  const emit = defineEmits(['success']);
  const onSave = () => {
    formRef.value.validate((valid) => {
      if (valid) {
        emit('success', 1);
      }
    });
  };
</script>
