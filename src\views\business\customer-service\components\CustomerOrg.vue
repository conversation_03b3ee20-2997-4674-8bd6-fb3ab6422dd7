<template>
  <div class="mt-4 flex h-0 flex-1 rounded bg-w">
    <OrgAside :id="id" @select="onAsideSelect" />

    <div class="flex h-full w-0 flex-1 flex-col pt-5">
      <el-scrollbar height="100%" class="h-0 flex-1">
        <div class="px-10 pb-10">
          <h3 class="mb-5 text-xl font-bold">申请信息</h3>

          <div class="mb-4 flex items-center">
            <div class="h-[13px] w-[3px] bg-p" />
            <span class="ml-2">单位信息</span>
          </div>
          <div class="text-sm">
            <el-row>
              <el-col :span="12">
                <div class="flex flex-col justify-start">
                  <span class="mb-2 text-[#939899]">机构中文全称</span>
                  <span>{{ applicationInfo.fullNameCn }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="flex flex-col justify-start">
                  <span class="mb-2 text-[#939899]">机构中文简称</span>
                  <span>{{ applicationInfo.abbreviationCn }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row class="mt-4">
              <el-col :span="12">
                <div class="flex flex-col justify-start">
                  <span class="mb-2 text-[#939899]">机构英文全称</span>
                  <span>{{ applicationInfo.fullNameEn }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="flex flex-col justify-start">
                  <span class="mb-2 text-[#939899]">机构英文简称</span>
                  <span>{{ applicationInfo.abbreviationEn }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row class="mt-4">
              <el-col :span="12">
                <div class="flex flex-col justify-start">
                  <span class="mb-2 text-[#939899]">机构性质</span>
                  <span>{{ applicationInfo.character }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="flex flex-col justify-start">
                  <span class="mb-2 text-[#939899]">统一社会信用代码</span>
                  <span>{{ applicationInfo.usci }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row class="mt-4">
              <el-col :span="24">
                <div class="flex flex-col justify-start">
                  <div class="mb-2 text-[#939899]">机构证件扫描件</div>
                  <FileList :list="fileList" />
                </div>
              </el-col>
            </el-row>
            <el-row class="mt-4">
              <el-col :span="12">
                <div class="flex flex-col justify-start">
                  <span class="mb-2 text-[#939899]">法人姓名</span>
                  <span>{{ applicationInfo.corporateName }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="flex flex-col justify-start">
                  <span class="mb-2 text-[#939899]">法人代表身份证号码</span>
                  <span>{{ applicationInfo.corporateIdNumber }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row class="mt-4">
              <el-col :span="24">
                <div class="flex flex-col justify-start">
                  <div class="mb-2 text-[#939899]">机构简介</div>
                  <span>{{ applicationInfo.intro }}</span>
                </div>
              </el-col>
            </el-row>
          </div>

          <div class="mb-4 mt-10 flex items-center">
            <div class="h-[13px] w-[3px] bg-p" />
            <span class="ml-2">单位管理员信息</span>
          </div>
          <div class="text-sm">
            <el-row>
              <el-col :span="12">
                <div class="flex flex-col justify-start">
                  <span class="mb-2 text-[#939899]">姓名</span>
                  <span>{{ adminInfo.name }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="flex flex-col justify-start">
                  <span class="mb-2 text-[#939899]">电话</span>
                  <span>{{ adminInfo.phone }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row class="mt-4">
              <el-col :span="12">
                <div class="flex flex-col justify-start">
                  <span class="mb-2 text-[#939899]">证件类型</span>
                  <span>{{ adminInfo.idType }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="flex flex-col justify-start">
                  <span class="mb-2 text-[#939899]">证件号码</span>
                  <span>{{ adminInfo.idNumber }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-scrollbar>

      <div v-if="applicationInfo.id" class="border-t border-border py-5 pl-10">
        <template v-if="applicationInfo.status == 0">
          <div class="mb-1 text-sm font-bold">审批意见:</div>
          <el-input
            v-model="opinion"
            :rows="4"
            type="textarea"
            placeholder="请输入审批意见"
            show-word-limit
            maxlength="200"
          />
          <div class="mt-5">
            <el-button plain :icon="Check" color="#007f99" @click="onAgree"> 同意 </el-button>
            <el-button plain :icon="Close" color="#e74c4c" @click="onReject"> 拒绝 </el-button>
          </div>
        </template>
        <template v-else>
          <div class="text-sm font-bold text-tip">审批意见:</div>
          <div class="mt-2 text-sm">
            {{ applicationInfo.opinion }}
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { Check, Close } from '@element-plus/icons-vue';
  import OrgAside from './CustomerOrgAside.vue';
  import FileList from '@/components/FileList.vue';
  import { ElMessage, ElMessageBox } from 'element-plus';

  const id = ref('');
  const fileList = ref([
    {
      name: '文件示例文件示例.pdf',
      url: '',
    },
    {
      name: '文件示例.pdf',
      url: '',
    },
  ]);
  let applicationInfo = reactive({});
  const onAsideSelect = (e) => {
    Object.assign(applicationInfo, e);
  };

  const adminInfo = reactive({
    name: '张三',
    phone: '15994683045',
    idType: '身份证',
    idNumber: '450324999999999999',
  });

  const opinion = ref('');
  //同意
  const onAgree = () => {
    ElMessageBox.confirm('确定同意该申请？', '操作提示', { type: 'warning' })
      .then(() => {
        ElMessage({ type: 'success', message: '操作成功' });
      })
      .catch(() => {});
  };
  //拒绝
  const onReject = () => {
    ElMessageBox.confirm('确定拒绝该申请？', '操作提示', { type: 'warning' })
      .then(() => {
        ElMessage({ type: 'success', message: '操作成功' });
      })
      .catch(() => {});
  };
</script>

<style lang="scss" scoped></style>
