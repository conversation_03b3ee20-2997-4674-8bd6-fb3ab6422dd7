import dayjs from 'dayjs';

export function boolText(value: string | undefined | boolean) {
  return value === 'true' || value === true ? '是' : '否';
}

/**
 * 账号状态
 */
export function userLockFlagText(value: string | number | undefined) {
  switch (value) {
    case '0':
    case 0:
      return '启用';
    case '1':
    case 1:
      return '注册申请中';
    case '3':
    case 3:
      return '注册审核拒绝';
    case '9':
    case 9:
      return '锁定';
    default:
      return '';
  }
}

/**
 * 角色
 */
export function roleText(value: RoleVO[] | undefined) {
  if (value?.length) {
    return value.map((r) => r.roleName).join(',');
  }
  return '';
}

/**
 * 字段类型
 */
export function fieldTypeText(value: string | undefined) {
  switch (value) {
    case 'S':
      return '字符型(S)';
    case 'S1':
      return '字符型(S1)';
    case 'S2':
      return '枚举型(S2)';
    case 'S3':
      return '代码(S3)';
    case 'E':
      return '枚举型(E)';
    case 'radio':
      return '枚举型(radio)';
    case 'checkbox':
      return '枚举型(checkbox)';
    case 'L':
      return '布尔型(L)';
    case 'N':
      return '数值型(N)';
    case 'F':
      return '小数型(F)';
    case 'D':
      return '日期(D)';
    case 'DT':
      return '日期时间(DT)';
    case 'T':
      return '时间(T)';
    case 'C':
      return '组合型(C)';
    case 'B':
      return '二进制(B)';
    default:
      return value;
  }
}

/**
 * 日期显示成文字
 */
export function formatDateTextByType(value: string | undefined, valueType: string | undefined) {
  if (['D', 'DT', 'T'].includes(valueType!)) {
    return dayjs(value).format('YYYY-MM-DD');
  }
  return value || '无';
}
