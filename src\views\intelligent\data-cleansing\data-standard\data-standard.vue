<template>
  <div class="flex h-full flex-col overflow-hidden">
    <h2 class="flex h-[60px] items-center bg-w pl-5 text-xl font-semibold">数据标准管理</h2>

    <div class="m-5 flex h-0 flex-1 flex-col rounded-md bg-w pt-5">
      <div class="flex justify-between px-10">
        <div>
          <el-button type="primary" @click="onAdd"> 新增 </el-button>
        </div>
        <el-input v-model="search" placeholder="请输入关键字" style="width: 500px">
          <template #prepend>
            <el-select v-model="searchPre" style="width: 130px">
              <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </template>
          <template #append>
            <el-button :icon="Search" @click="onSearch" />
          </template>
        </el-input>
      </div>

      <div class="mt-3 h-0 w-full flex-1 px-10">
        <el-table height="100%" :data="tableData" style="width: 100%" class="c-table-header">
          <el-table-column type="index" width="55" label="序号" />
          <el-table-column prop="name" label="标准名称" min-width="100px" />
          <el-table-column prop="standardNumber" label="标准号" />
          <el-table-column prop="dictionary" label="标准字典" />
          <el-table-column prop="level" label="标准级别" />
          <el-table-column prop="date" label="创建时间" />
          <el-table-column label="操作" width="180">
            <template #default="{ row }">
              <el-button link type="primary" @click="onEdit(row)"> 编辑 </el-button>
              <el-popconfirm title="确定删除此项？" @confirm="onDel(row)">
                <template #reference>
                  <el-button link type="primary"> 删除 </el-button>
                </template>
              </el-popconfirm>
              <el-button link type="primary" @click="onDictionary(row)"> 字典项 </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="pagination-bottom">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :page-size="pagination.pageSize"
          :total="tableData.length"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>

  <Drawer v-model="showDrawer" :data="drawerData" @success="onSuccess" />
</template>

<script setup>
  import { Search } from '@element-plus/icons-vue';
  import Drawer from './components/Drawer.vue';

  const searchPre = ref('标准名称');
  const search = ref('');
  const options = [
    {
      value: '标准名称',
      label: '标准名称',
    },
    {
      value: '标准号',
      label: '标准号',
    },
    {
      value: '标准字典',
      label: '标准字典',
    },
  ];
  const onSearch = () => {};

  const showDrawer = ref(false);
  const onAdd = () => {
    drawerData.value = {};
    showDrawer.value = true;
  };
  let drawerData = ref({});
  const onEdit = (row) => {
    drawerData.value = row;
    showDrawer.value = true;
  };
  const onSuccess = () => {};

  //表格
  const tableData = ref([
    {
      id: '1',
      name: 'XXXXXXXX标准',
      standardNumber: '154543123',
      dictionary: '标准字段标准字段标准字段',
      level: '国家级',
      date: '2022-03-22 12:00:00',
    },
  ]);
  const onDel = (row) => {};
  const onDictionary = (row) => {};

  //页码
  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  const handleCurrentChange = (e) => {
    pagination.page = e;
  };
  const handleSizeChange = (e) => {
    pagination.pageSize = e;
  };
</script>
