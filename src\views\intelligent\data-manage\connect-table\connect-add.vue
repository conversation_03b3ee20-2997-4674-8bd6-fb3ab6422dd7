<template>
  <div class="flex h-full w-full flex-col">
    <h2 class="z-[1] flex h-[60px] cursor-pointer items-center bg-w pl-5 text-xl font-semibold shadow" @click="onBack">
      <el-icon class="mr-2" color="#939899">
        <ArrowLeft />
      </el-icon>
      添加挂接
    </h2>

    <el-container class="h-0 flex-1">
      <el-main class="rounded bg-w">
        <div class="flex h-full flex-col">
          <div class="flex justify-between px-10 pt-5">
            <div class="flex">
              <el-select
                v-model="valueType"
                placeholder="类型"
                clearable
                class="mr-4"
                style="width: 100px"
                @change="fetchData()"
              >
                <el-option v-for="item in valueTypes" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
              <el-select
                v-model="gendered"
                placeholder="是否按性别区分"
                clearable
                class="mr-4"
                style="width: 150px"
                @change="fetchData()"
              >
                <el-option v-for="item in boolTypes" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
              <el-input v-model="fieldName" placeholder="请输入字段名称" style="width: 300px">
                <template #append>
                  <el-button :icon="Search" @click="fetchData()" />
                </template>
              </el-input>
              <el-button class="ml-3" :icon="Refresh" @click="fetchData()" />
            </div>

            <div>
              <el-button type="primary" :disabled="!checkList.length" :loading="saveLoading" @click="onSave">
                保存
              </el-button>
            </div>
          </div>

          <div class="mt-4 h-0 flex-1 px-10">
            <el-table
              v-loading="loading"
              :data="tableData"
              style="width: 100%"
              height="100%"
              class="c-table-header"
              @selection-change="handleSelectionChange"
            >
              <el-table-column type="selection" width="55" />
              <el-table-column prop="name" label="名称" min-width="100px" />
              <el-table-column prop="valueType" label="类型" />
              <el-table-column label="是否按性别区分">
                <template #default="{ row }">
                  <span>{{ row.gendered ? '是' : '否' }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="description" label="说明" />
              <el-table-column prop="rltTime.updateTime" label="更新时间" />
            </el-table>
          </div>

          <div class="pagination-bottom">
            <el-pagination
              background
              layout="total, sizes, prev, pager, next, jumper"
              :current-page="pagination.page"
              :page-size="pagination.pageSize"
              :total="total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
  import dayjs from 'dayjs';
  import { findMedicalFieldVOByCriteria, mountMedicalFieldToCatalogue } from '@/api/index';
  import { Search, Operation, Refresh } from '@element-plus/icons-vue';
  import { ElMessageBox, ElMessage } from 'element-plus';
  import { valueTypes } from '@/utils/constants';
  import { useRouter } from 'vue-router';
  const router = useRouter();
  const props = defineProps({ catalogId: { type: String } });

  const emit = defineEmits(['back']);
  const onBack = () => {
    // router.back();
    emit('back');
  };

  const loading = ref(false);
  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  const total = ref(0);
  const handleCurrentChange = (e) => {
    pagination.page = e;
    fetchData(e);
  };
  const handleSizeChange = (e) => {
    pagination.pageSize = e;
    fetchData();
  };

  const fieldName = ref('');
  const valueType = ref('');
  const gendered = ref('');
  const boolTypes = ref([
    {
      label: '是',
      value: true,
    },
    {
      label: '否',
      value: false,
    },
  ]);
  fetchData();

  const tableData = ref([]);
  const checkList = ref([]);
  const handleSelectionChange = (val) => {
    checkList.value = val;
  };

  //编辑
  let id = ref('');
  const readonly = ref(false);
  const showDrawer = ref(false);
  const onAdd = () => {
    id.value = '';
    readonly.value = false;
    showDrawer.value = true;
  };
  const onEdit = (item) => {
    id.value = item.id;
    readonly.value = false;
    showDrawer.value = true;
  };
  const onSuccess = () => {
    fetchData(pagination.page);
  };

  async function fetchData(pageNum = 1) {
    try {
      loading.value = true;
      let params = {
        name: fieldName.value,
        valueType: valueType.value,
        gendered: gendered.value,
        // description: null,
        pageNum,
        pageSize: pagination.pageSize,
      };
      const { data } = await findMedicalFieldVOByCriteria(params);
      total.value = data.totalElement;
      pagination.page = pageNum;
      tableData.value = data.content;
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  const saveLoading = ref(false);
  const onSave = async () => {
    try {
      saveLoading.value = true;
      const params = checkList.value.map((item) => {
        return {
          ctlgId: props.catalogId,
          mfId: item.id,
        };
      });
      await mountMedicalFieldToCatalogue(params);
      ElMessage({ type: 'success', message: '保存成功' });
      emit('back');
    } catch (error) {
      console.log(error);
    } finally {
      saveLoading.value = false;
    }
  };
</script>

<style lang="scss" scoped>
  .el-main {
    --el-main-padding: 0;
  }
</style>
