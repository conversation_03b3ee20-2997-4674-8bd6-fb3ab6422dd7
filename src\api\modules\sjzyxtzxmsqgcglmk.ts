/*
 * @OriginalName: 数据资源系统中项目申请过程管理模块
 * @Description: 项目申请过程管理中源代码文件的创建、更新和查询
 */
import { request } from '@/utils/request';

/**
 * 新建或更新附加文件记录，并上传文档
 * @description 先新建或更新附加文件记录，然后覆盖式上传文档。此接口的操作将覆盖原文档。
 */
export function uploadFile(data: { entityDTO: SourceCodeDTO; file: string }) {
  return request<RCommonFileVO>(`/sourceCode/uploadFile`, {
    method: 'post',
    data,
  });
}

/**
 * 根据源代码文件记录id，上传文档
 * @description 为源代码文件记录，覆盖式上传文档。此接口的操作将覆盖原文档。
 */
export function uploadFile_1(sourceCodeId: number, data: { file: string }, params?: { sourceCodeId: number }) {
  return request<RCommonFileVO>(`/sourceCode/uploadFile/${sourceCodeId}`, {
    method: 'post',
    data,
    params,
  });
}

/**
 * 新建或更新数据表中的记录
 * @description 按tdto的信息，新建或更新数据表中的记录。
 */
export function newOrUpdateEntity_1(data: SourceCodeDTO) {
  return request<RSourceCodeVO>(`/sourceCode/newOrUpdateEntity`, {
    method: 'post',
    data,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条或多条数据记录。
 */
export function findEntityById_1(data: Array<number>) {
  return request<RListSourceCodeVO>(`/sourceCode/findEntityByIdCollection`, {
    method: 'post',
    data,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一或多条记录。
 */
export function deleteEntityById_1(data: Array<number>) {
  return request<R>(`/sourceCode/deleteEntityByIdCollection`, {
    method: 'post',
    data,
  });
}

/**
 * 新建或更新刊物和报告记录，并上传文档
 * @description 先新建或更新刊物和报告记录，然后覆盖式上传文档。此接口的操作将覆盖原文档。
 */
export function uploadFile_2(data: { entityDTO: PublicationReportDTO; file: string }) {
  return request<RCommonFileVO>(`/publicationreport/uploadFile`, {
    method: 'post',
    data,
  });
}

/**
 * 根据刊物和报告记录id，上传文档
 * @description 为附加刊物和报告记录，覆盖式上传文档。此接口的操作将覆盖原文档。
 */
export function uploadFile_3(
  publicationReportId: number,
  data: { file: string },
  params?: { publicationReportId: number }
) {
  return request<RCommonFileVO>(`/publicationreport/uploadFile/${publicationReportId}`, {
    method: 'post',
    data,
    params,
  });
}

/**
 * 新建或更新数据表中的记录
 * @description 按tdto的信息，新建或更新数据表中的记录。
 */
export function newOrUpdateEntity_2(data: PublicationReportDTO) {
  return request<RPublicationReportVO>(`/publicationreport/newOrUpdateEntity`, {
    method: 'post',
    data,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条或多条数据记录。
 */
export function findEntityById_2(data: Array<number>) {
  return request<RListPublicationReportVO>(`/publicationreport/findEntityByIdCollection`, {
    method: 'post',
    data,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一或多条记录。
 */
export function deleteEntityById_2(data: Array<number>) {
  return request<R>(`/publicationreport/deleteEntityByIdCollection`, {
    method: 'post',
    data,
  });
}

/**
 * 新建或更新附加文件记录，并上传文档
 * @description 先新建或更新附加文件记录，然后覆盖式上传文档。此接口的操作将覆盖原文档。
 */
export function uploadFile_4(data: { entityDTO: ManuscriptDTO; file: string }) {
  return request<RCommonFileVO>(`/manuscript/uploadFile`, {
    method: 'post',
    data,
  });
}

/**
 * 根据手稿记录id，上传文档
 * @description 为手稿记录，覆盖式上传文档。此接口的操作将覆盖原文档。
 */
export function uploadFile_5(manuscriptId: number, data: { file: string }, params?: { manuscriptId: number }) {
  return request<RCommonFileVO>(`/manuscript/uploadFile/${manuscriptId}`, {
    method: 'post',
    data,
    params,
  });
}

/**
 * 新建或更新数据表中的记录
 * @description 按tdto的信息，新建或更新数据表中的记录。
 */
export function newOrUpdateEntity_4(data: ManuscriptDTO) {
  return request<RManuscriptVO>(`/manuscript/newOrUpdateEntity`, {
    method: 'post',
    data,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条或多条数据记录。
 */
export function findEntityById_4(data: Array<number>) {
  return request<RListManuscriptVO>(`/manuscript/findEntityByIdCollection`, {
    method: 'post',
    data,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一或多条记录。
 */
export function deleteEntityById_4(data: Array<number>) {
  return request<R>(`/manuscript/deleteEntityByIdCollection`, {
    method: 'post',
    data,
  });
}

/**
 * 新建或更新数据文件记录，并上传文档
 * @description 先新建或更新数据文件记录，然后覆盖式上传文档。此接口的操作将覆盖原文档。
 */
export function uploadFile_6(data: { entityDTO: DatasetFileDTO; file: string }) {
  return request<RCommonFileVO>(`/datasetFile/uploadFile`, {
    method: 'post',
    data,
  });
}

/**
 * 根据数据文件记录id，上传文档
 * @description 为项目申请过程管理中的数据文件记录，覆盖式上传文档。此接口的操作将覆盖原文档。
 */
export function uploadFile_7(datasetFileId: number, data: { file: string }, params?: { datasetFileId: number }) {
  return request<RCommonFileVO>(`/datasetFile/uploadFile/${datasetFileId}`, {
    method: 'post',
    data,
    params,
  });
}

/**
 * 新建或更新数据表中的记录
 * @description 按tdto的信息，新建或更新数据表中的记录。
 */
export function newOrUpdateEntity_6(data: DatasetFileDTO) {
  return request<RDatasetFileVO>(`/datasetFile/newOrUpdateEntity`, {
    method: 'post',
    data,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条或多条数据记录。
 */
export function findEntityById_6(data: Array<number>) {
  return request<RListDatasetFileVO>(`/datasetFile/findEntityByIdCollection`, {
    method: 'post',
    data,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一或多条记录。
 */
export function deleteEntityById_6(data: Array<number>) {
  return request<R>(`/datasetFile/deleteEntityByIdCollection`, {
    method: 'post',
    data,
  });
}

/**
 * 新建或更新附加文件记录，并上传文档
 * @description 先新建或更新附加文件记录，然后覆盖式上传文档。此接口的操作将覆盖原文档。
 */
export function uploadFile_8(data: { entityDTO: AttachmentDTO; file: string }) {
  return request<RCommonFileVO>(`/attachment/uploadFile`, {
    method: 'post',
    data,
  });
}

/**
 * 根据附加文件记录id，上传文档
 * @description 为附加文件记录，覆盖式上传文档。此接口的操作将覆盖原文档。
 */
export function uploadFile_9(attachmentId: number, data: { file: string }, params?: { attachmentId: number }) {
  return request<RCommonFileVO>(`/attachment/uploadFile/${attachmentId}`, {
    method: 'post',
    data,
    params,
  });
}

/**
 * 新建或更新数据表中的记录
 * @description 按tdto的信息，新建或更新数据表中的记录。
 */
export function newOrUpdateEntity_8(data: AttachmentDTO) {
  return request<RAttachmentVO>(`/attachment/newOrUpdateEntity`, {
    method: 'post',
    data,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条或多条数据记录。
 */
export function findEntityById_8(data: Array<number>) {
  return request<RListAttachmentVO>(`/attachment/findEntityByIdCollection`, {
    method: 'post',
    data,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一或多条记录。
 */
export function deleteEntityById_8(data: Array<number>) {
  return request<R>(`/attachment/deleteEntityByIdCollection`, {
    method: 'post',
    data,
  });
}

/**
 * 获取文档
 * @description 按源代码文件记录的ID，以InputStream类型从系统中获取文件。
 */
export function getFileStream(sourceCodeId: number, params?: { sourceCodeId: number }) {
  return request(`/sourceCode/getFileStream/${sourceCodeId}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条或多条数据记录。
 */
export function findEntityById_17(idArray: Array<object>, params?: { idArray: Array<object> }) {
  return request<RListSourceCodeVO>(`/sourceCode/findEntityByIdArray/${idArray}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条数据记录。
 */
export function findEntityById_18(id: number, params?: { id: number }) {
  return request<RSourceCodeVO>(`/sourceCode/findEntityById/${id}`, {
    method: 'get',
    params,
  });
}

/**
 * 显示数据表中的全部记录
 * @description 分页显示数据表中的全部记录
 */
export function findAll_1(pageNum: number, pageSize: number, params?: { pageNum: number; pageSize: number }) {
  return request<R>(`/sourceCode/findAll/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一或多条记录。
 */
export function deleteEntityById_17(idArray: Array<object>, params?: { idArray: Array<object> }) {
  return request<R>(`/sourceCode/deleteEntityByIdArray/${idArray}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一条记录。
 */
export function deleteEntityById_18(id: number, params?: { id: number }) {
  return request<R>(`/sourceCode/deleteEntityById/${id}`, {
    method: 'get',
    params,
  });
}

/**
 * 获取文档
 * @description 按刊物和报告的ID，以InputStream类型从系统中获取文件。
 */
export function getFileStream_1(publicationReportId: number, params?: { publicationReportId: number }) {
  return request(`/publicationreport/getFileStream/${publicationReportId}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条或多条数据记录。
 */
export function findEntityById_19(idArray: Array<object>, params?: { idArray: Array<object> }) {
  return request<RListPublicationReportVO>(`/publicationreport/findEntityByIdArray/${idArray}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条数据记录。
 */
export function findEntityById_20(id: number, params?: { id: number }) {
  return request<RPublicationReportVO>(`/publicationreport/findEntityById/${id}`, {
    method: 'get',
    params,
  });
}

/**
 * 显示数据表中的全部记录
 * @description 分页显示数据表中的全部记录
 */
export function findAll_2(pageNum: number, pageSize: number, params?: { pageNum: number; pageSize: number }) {
  return request<R>(`/publicationreport/findAll/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一或多条记录。
 */
export function deleteEntityById_19(idArray: Array<object>, params?: { idArray: Array<object> }) {
  return request<R>(`/publicationreport/deleteEntityByIdArray/${idArray}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一条记录。
 */
export function deleteEntityById_20(id: number, params?: { id: number }) {
  return request<R>(`/publicationreport/deleteEntityById/${id}`, {
    method: 'get',
    params,
  });
}

/**
 * 获取文档
 * @description 按手稿记录的ID，以InputStream类型从系统中获取文件。
 */
export function getFileStream_2(manuscriptId: number, params?: { manuscriptId: number }) {
  return request(`/manuscript/getFileStream/${manuscriptId}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条或多条数据记录。
 */
export function findEntityById_23(idArray: Array<object>, params?: { idArray: Array<object> }) {
  return request<RListManuscriptVO>(`/manuscript/findEntityByIdArray/${idArray}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条数据记录。
 */
export function findEntityById_24(id: number, params?: { id: number }) {
  return request<RManuscriptVO>(`/manuscript/findEntityById/${id}`, {
    method: 'get',
    params,
  });
}

/**
 * 显示数据表中的全部记录
 * @description 分页显示数据表中的全部记录
 */
export function findAll_4(pageNum: number, pageSize: number, params?: { pageNum: number; pageSize: number }) {
  return request<R>(`/manuscript/findAll/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一或多条记录。
 */
export function deleteEntityById_23(idArray: Array<object>, params?: { idArray: Array<object> }) {
  return request<R>(`/manuscript/deleteEntityByIdArray/${idArray}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一条记录。
 */
export function deleteEntityById_24(id: number, params?: { id: number }) {
  return request<R>(`/manuscript/deleteEntityById/${id}`, {
    method: 'get',
    params,
  });
}

/**
 * 获取文档
 * @description 按数据集文件记录的ID，以InputStream类型从系统中获取文件。
 */
export function getFileStream_3(datasetFileId: number, params?: { datasetFileId: number }) {
  return request(`/datasetFile/getFileStream/${datasetFileId}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条或多条数据记录。
 */
export function findEntityById_27(idArray: Array<object>, params?: { idArray: Array<object> }) {
  return request<RListDatasetFileVO>(`/datasetFile/findEntityByIdArray/${idArray}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条数据记录。
 */
export function findEntityById_28(id: number, params?: { id: number }) {
  return request<RDatasetFileVO>(`/datasetFile/findEntityById/${id}`, {
    method: 'get',
    params,
  });
}

/**
 * 显示数据表中的全部记录
 * @description 分页显示数据表中的全部记录
 */
export function findAll_6(pageNum: number, pageSize: number, params?: { pageNum: number; pageSize: number }) {
  return request<R>(`/datasetFile/findAll/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一或多条记录。
 */
export function deleteEntityById_27(idArray: Array<object>, params?: { idArray: Array<object> }) {
  return request<R>(`/datasetFile/deleteEntityByIdArray/${idArray}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一条记录。
 */
export function deleteEntityById_28(id: number, params?: { id: number }) {
  return request<R>(`/datasetFile/deleteEntityById/${id}`, {
    method: 'get',
    params,
  });
}

/**
 * 获取文档
 * @description 按附加文件记录的ID，以InputStream类型从系统中获取文件。
 */
export function getFileStream_4(attachmentId: number, params?: { attachmentId: number }) {
  return request(`/attachment/getFileStream/${attachmentId}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条或多条数据记录。
 */
export function findEntityById_31(idArray: Array<object>, params?: { idArray: Array<object> }) {
  return request<RListAttachmentVO>(`/attachment/findEntityByIdArray/${idArray}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条数据记录。
 */
export function findEntityById_32(id: number, params?: { id: number }) {
  return request<RAttachmentVO>(`/attachment/findEntityById/${id}`, {
    method: 'get',
    params,
  });
}

/**
 * 显示数据表中的全部记录
 * @description 分页显示数据表中的全部记录
 */
export function findAll_8(pageNum: number, pageSize: number, params?: { pageNum: number; pageSize: number }) {
  return request<R>(`/attachment/findAll/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一或多条记录。
 */
export function deleteEntityById_31(idArray: Array<object>, params?: { idArray: Array<object> }) {
  return request<R>(`/attachment/deleteEntityByIdArray/${idArray}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一条记录。
 */
export function deleteEntityById_32(id: number, params?: { id: number }) {
  return request<R>(`/attachment/deleteEntityById/${id}`, {
    method: 'get',
    params,
  });
}
