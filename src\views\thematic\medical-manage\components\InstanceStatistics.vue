<template>
  <el-dialog v-model="show" title="实例统计信息" width="80%" class="dialog-height" @close="onCancel">
    <el-form ref="formRef" :model="addForm" inline :rules="rules" label-width="150px">
      <el-form-item label="参与者或被试的数量" prop="participantCount">
        <el-input-number v-model="addForm.participantCount" style="width: 200px" :min="0" />
      </el-form-item>
      <el-form-item label="记录数" prop="recordCount">
        <el-input-number v-model="addForm.recordCount" style="width: 200px" :min="0" />
      </el-form-item>

      <template v-if="valueType === '文本'">
        <el-form-item label="最短文本长度" prop="minLength">
          <el-input-number v-model="addForm.minLength" style="width: 200px" :min="0" />
        </el-form-item>
        <el-form-item label="平均文本长度" prop="averageLength">
          <el-input-number v-model="addForm.averageLength" style="width: 200px" :min="0" />
        </el-form-item>
        <el-form-item label="最长文本长度" prop="maxLength">
          <el-input-number v-model="addForm.maxLength" style="width: 200px" :min="0" />
        </el-form-item>
      </template>

      <template v-if="valueType === '整数' || valueType === '实数' || valueType === '日期时间'">
        <el-form-item label="不重复数值数量" prop="distinctValueCount">
          <el-input-number v-model="addForm.distinctValueCount" style="width: 200px" :min="0" />
        </el-form-item>
        <el-form-item label="最小值" prop="min">
          <el-input-number v-model="addForm.min" style="width: 200px" />
        </el-form-item>
        <el-form-item label="最大值" prop="max">
          <el-input-number v-model="addForm.max" style="width: 200px" />
        </el-form-item>
        <el-form-item label="均值" prop="mean">
          <el-input-number v-model="addForm.mean" style="width: 200px" />
        </el-form-item>
        <el-form-item label="方差" prop="stdDev">
          <el-input-number v-model="addForm.stdDev" style="width: 200px" />
        </el-form-item>
        <el-form-item label="10%分位数" prop="decile1">
          <el-input-number v-model="addForm.decile1" style="width: 200px" />
        </el-form-item>
        <el-form-item label="20%分位数" prop="decile2">
          <el-input-number v-model="addForm.decile2" style="width: 200px" />
        </el-form-item>
        <el-form-item label="30%分位数" prop="decile3">
          <el-input-number v-model="addForm.decile3" style="width: 200px" />
        </el-form-item>
        <el-form-item label="40%分位数" prop="decile4">
          <el-input-number v-model="addForm.decile4" style="width: 200px" />
        </el-form-item>
        <el-form-item label="中位数" prop="median">
          <el-input-number v-model="addForm.median" style="width: 200px" />
        </el-form-item>
        <el-form-item label="60%分位数" prop="decile6">
          <el-input-number v-model="addForm.decile6" style="width: 200px" />
        </el-form-item>
        <el-form-item label="70%分位数" prop="decile7">
          <el-input-number v-model="addForm.decile7" style="width: 200px" />
        </el-form-item>
        <el-form-item label="80%分位数" prop="decile8">
          <el-input-number v-model="addForm.decile8" style="width: 200px" />
        </el-form-item>
        <el-form-item label="90%分位数" prop="decile9">
          <el-input-number v-model="addForm.decile9" style="width: 200px" />
        </el-form-item>
      </template>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="danger" @click="onDel"> 删除 </el-button>
        <el-button @click="onCancel"> 取消 </el-button>
        <el-button type="primary" @click="onSave"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
  // import {
  //   addStatisticTxt,
  //   addStatisticLongInt,
  //   addStatisticFloat,
  //   addStatisticDateTime,
  //   addStatisticCategorical,
  //   deleteStatistic,
  //   findStatisticByInstanceId,
  // } from '@/api/index';
  import { ElMessage, ElMessageBox } from 'element-plus';

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
    id: {
      type: [String, Number],
    },
    valueType: {
      type: String,
    },
  });
  const show = ref(false);
  watchEffect(() => {
    show.value = props.modelValue;
    if (show.value) {
      fetchData();
    }
  });

  const formRef = ref();
  const addForm = reactive({
    id: 0,
    // participantCount: 0,
    // recordCount: 0,
  });
  const rules = ref({
    participantCount: [{ required: true, message: '不能为空' }],
    recordCount: [{ required: true, message: '不能为空' }],
  });
  const emit = defineEmits(['update:modelValue', 'success']);
  const onCancel = () => {
    emit('update:modelValue', false);
    emit('success');
    formRef.value.resetFields();
  };
  const addLoading = ref(false);
  const onSave = () => {
    // formRef.value.validate(async (valid) => {
    //   try {
    //     if (valid) {
    //       addLoading.value = true;
    //       switch (props.valueType) {
    //         case '文本':
    //           await addStatisticTxt(props.id, addForm);
    //           break;
    //         case '整数':
    //           await addStatisticLongInt(props.id, addForm);
    //           break;
    //         case '实数':
    //           await addStatisticFloat(props.id, addForm);
    //           break;
    //         case '日期时间':
    //           await addStatisticDateTime(props.id, addForm);
    //           break;
    //         case '单分类':
    //         case '多分类':
    //           await addStatisticCategorical(props.id, addForm);
    //           break;
    //       }
    //       ElMessage({ type: 'success', message: '操作成功' });
    //       onCancel();
    //     }
    //   } catch (error) {
    //     console.log(error);
    //   } finally {
    //     addLoading.value = false;
    //   }
    // });
  };
  const onDel = async () => {
    ElMessageBox.confirm('确定删除？', '操作提示', { type: 'warning' })
      .then(async () => {
        try {
          await deleteStatistic(props.id);
          ElMessage({ type: 'success', message: '删除成功' });
          onCancel();
        } catch (error) {
          console.log(error);
        }
      })
      .catch(() => {});
  };

  async function fetchData() {
    try {
      const { data } = await findStatisticByInstanceId(props.id);
      nextTick(() => {
        Object.assign(addForm, data);
      });
    } catch (error) {
      return Promise.reject(error);
    }
  }
</script>

<style lang="scss">
  .dialog-height {
    height: 75vh;
    overflow: auto;
    display: flex;
    flex-direction: column;

    .el-dialog__body {
      height: 0;
      flex: 1;
      display: flex;
      flex-direction: column;
    }
  }
</style>

<style lang="scss" scoped>
  .c-table-header {
    flex: 1;
    height: 0;
  }
</style>
