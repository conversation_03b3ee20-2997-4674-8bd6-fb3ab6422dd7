<template>
  <div class="flex h-full w-full flex-col">
    <h2 class="flex h-[60px] cursor-pointer items-center bg-w pl-5 text-xl font-semibold">元数据</h2>

    <el-container class="mt-5 flex h-0 flex-1 p-5 pt-0">
      <el-aside class="mr-[10px] h-full w-[280px] rounded bg-w">
        <CustomMenu v-model="menuId" :data="catalogData" />
      </el-aside>

      <el-main class="rounded border-border bg-w">
        <div class="flex h-full flex-col">
          <div class="flex items-center justify-end px-10 pt-5">
            <div class="flex">
              <el-input v-model="search" placeholder="请输入字段名称 ">
                <template #append>
                  <el-button :icon="Search" @click="onSearch" />
                </template>
              </el-input>
              <el-button class="ml-3" :icon="Refresh" @click="onRefresh" />
              <el-button class="ml-3" :icon="Operation" @click="changeSort" />
            </div>
          </div>

          <div class="mt-4 h-0 flex-1 px-10">
            <el-table
              :data="tableData"
              style="width: 100%"
              height="100%"
              class="c-table-header"
              @selection-change="handleSelectionChange"
            >
              <el-table-column type="selection" width="55" />
              <el-table-column type="index" width="55" label="序号" />
              <el-table-column prop="name" label="字段名称" min-width="100px" />
              <el-table-column prop="annotation" label="字段注释" />
              <el-table-column prop="isPrimary" label="是否主键" />
              <el-table-column prop="isAllowNull" label="是否允许为空" />
              <el-table-column prop="type" label="数据类型" />
              <el-table-column prop="size" label="数据长度" />
              <el-table-column prop="accuracy" label="数据精度" />
              <el-table-column prop="decimalPlace" label="数据小数位" />
              <el-table-column prop="defaultValue" label="数据默认值">
                <template #default="{ row }">
                  <span>{{ ['', null, undefined].includes(row.defaultValue) ? '-' : row.defaultValue }}</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="60">
                <template #default="{ row }">
                  <el-button link type="primary" @click="onView(row)"> 查看 </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <div class="pagination-bottom">
            <el-pagination
              background
              layout="total, sizes, prev, pager, next, jumper"
              :page-size="pagination.pageSize"
              :total="tableData.length"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
  import { Search, Operation, Refresh } from '@element-plus/icons-vue';
  import CustomMenu from '@/components/custom-menu/CustomMenu.vue';
  import { useRouter } from 'vue-router';
  const router = useRouter();

  //目录
  const menuId = ref('11');
  const catalogData = ref([
    {
      id: '1',
      title: 'mysql数据库',
      svgname: 'icon-shujuku1',
      children: [
        {
          id: '11',
          title: '设备表',
          svgname: 'icon-dingdandingdanmingxishouzhimingxi-xianxing',
          num: 0,
        },
      ],
    },
    {
      id: '2',
      title: 'robot数据库',
      svgname: 'icon-shujuku1',
      children: [
        {
          id: '21',
          title: '患者表',
          svgname: 'icon-dingdandingdanmingxishouzhimingxi-xianxing',
          num: 0,
        },
        {
          id: '22',
          title: '部位表',
          svgname: 'icon-dingdandingdanmingxishouzhimingxi-xianxing',
          num: 0,
        },
        {
          id: '23',
          title: '症状表',
          svgname: 'icon-dingdandingdanmingxishouzhimingxi-xianxing',
          num: 0,
        },
      ],
    },
  ]);

  const search = ref('');
  const onSearch = () => {};
  const onRefresh = () => {};
  const changeSort = () => {};

  //表格
  const tableData = ref([
    {
      id: 1,
      name: 'ID',
      annotation: '姓名',
      isPrimary: 'Y',
      isAllowNull: 'N',
      type: 'int',
      size: 20,
      accuracy: 10,
      decimalPlace: 0,
      defaultValue: '',
    },
    {
      id: 2,
      name: 'Name',
      annotation: '姓名',
      isPrimary: 'Y',
      isAllowNull: 'N',
      type: 'int',
      size: 20,
      accuracy: 10,
      decimalPlace: 0,
      defaultValue: '',
    },
  ]);
  for (let i = 0; i < 20; i++) {
    tableData.value.push({
      id: 3 + i,
      name: 'ID',
      annotation: '姓名',
      isPrimary: 'Y',
      isAllowNull: 'N',
      type: 'int',
      size: 20,
      accuracy: 10,
      decimalPlace: 0,
      defaultValue: '',
    });
  }
  const checkList = ref([]);
  const handleSelectionChange = (val) => {
    checkList.value = val;
  };
  const onView = (row) => {
    router.push({ name: 'ThematicDataDetail', params: { id: row.id } });
  };

  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  const handleCurrentChange = (e) => {
    pagination.page = e;
  };
  const handleSizeChange = (e) => {
    pagination.pageSize = e;
  };

  const onSubmit = () => {};
</script>

<style lang="scss" scoped>
  .el-main {
    --el-main-padding: 0;
  }
</style>
