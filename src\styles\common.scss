html,
body {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  height: 100%;
  color: $color-main-text;
}

#app {
  height: 100%;
}

a {
  text-decoration: none;
  color: inherit;
  cursor: pointer;
  color: $color-blue;
}

ul {
  list-style: none;
}

.center-content {
  width: $main-width;
  margin: 0 auto;
}

.box-shadow {
  box-shadow: var(--el-box-shadow-light);
  z-index: 1;
}

.pagination-bottom {
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: 40px;
  width: 100%;
  height: 56px;
  background: #ffffff;
  box-shadow: var(--el-box-shadow-light);
}

.action-footer {
  display: flex;
  justify-content: center;
  background: #fff;
  padding: 16px 0;
  box-shadow: var(--el-box-shadow-light);
  z-index: 1;
}

.status::before {
  content: '';
  display: inline-block;
  border-radius: 999px;
  width: 6px;
  height: 6px;
  margin-right: 6px;
  position: relative;
  top: -2px;
}
.status-green::before {
  background: var(--el-color-success);
}
.status-gray::before {
  background: $color-tip-text;
}
.status-blue::before {
  background: $color-blue;
}
.status-red::before {
  background: var(--el-color-danger);
}
.status-yellow::before {
  background: var(--el-color-warning);
}
