<template>
  <div class="flex h-full min-h-fit w-full flex-col justify-start rounded-lg bg-w p-[3%]">
    <div class="flex h-fit w-full justify-between">
      <span class="text-sm font-bold">我的消息</span>
      <div>
        <span class="text-xs text-[#939899]">查看更多</span>
        <el-icon :size="10">
          <ArrowRightBold color="#939899" :size="20" />
        </el-icon>
      </div>
    </div>
    <div class="h-fit p-3">
      <div
        v-for="item in datali"
        :key="item.id"
        class="flex w-full items-center border-b-[1px] border-solid border-[#e8eaed] pb-3 pt-2"
      >
        <div v-if="item.isread" class="flex items-center justify-center rounded-[50%] bg-[#f0f2f5] p-3">
          <el-icon :size="15" color="#939899">
            <Bell />
          </el-icon>
        </div>
        <div v-else class="flex items-center justify-center rounded-[50%] bg-[#ebf5f7] p-3">
          <el-icon :size="15" color="#007f99">
            <Bell />
          </el-icon>
        </div>
        <div v-if="item.isread" style="color: #939899" class="ml-4 w-fit flex-1">
          <span class="text-sm">{{ item.title }}</span>
          <div class="mt-1 flex justify-between">
            <span class="text-[xx-small]">发送人：{{ item.sender }}</span>
            <span class="text-[xx-small]">{{ item.date }}</span>
          </div>
        </div>
        <div v-if="!item.isread" class="ml-4 w-fit flex-1">
          <span class="text-sm">{{ item.title }}</span>
          <div style="color: #939899" class="mt-1 flex justify-between">
            <span class="text-[xx-small]">发送人：{{ item.sender }}</span>
            <span class="text-[xx-small]">{{ item.date }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
  let datali = [
    {
      title: '5362362脑疾病研究',
      sender: '张三',
      date: '2022-03-12 12:00:00',
      isread: false,
    },
    {
      title: '5362362脑疾病研究',
      sender: '张三',
      date: '2022-03-12 12:00:00',
      isread: true,
    },
    {
      title: '5362362脑疾病研究',
      sender: '张三',
      date: '2022-03-12 12:00:00',
      isread: true,
    },
    {
      title: '5362362脑疾病研究',
      sender: '张三',
      date: '2022-03-12 12:00:00',
      isread: false,
    },
    {
      title: '5362362脑疾病研究',
      sender: '张三',
      date: '2022-03-12 12:00:00',
      isread: false,
    },
  ];
</script>

<style>
  .isread {
    background-color: #ebf5f7;
  }
  .notread {
    background-color: #f0f2f5;
  }
</style>
