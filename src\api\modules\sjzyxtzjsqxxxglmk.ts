/*
 * @OriginalName: 数据资源系统中角色权限信息管理模块
 * @Description: 管理维修系统中的角色和权限记录该模块主要为系统管理员进行系统基础信息维护提供支持。
 */
import { request } from '@/utils/request';

/**
 * 新建或更新角色记录
 * @description 根据RoleVO中的信息，在数据库的Role表中若已存在id相同的记录，则更新表中的相应记录；不存在则添加一条新记录。
 */
export function newOrUpdateRole(data: RoleVO) {
  return request<RRoleVO>(`/rolePrivilege/newOrUpdateRoleVO`, {
    method: 'post',
    data,
  });
}

/**
 * 新建或更新权限记录
 * @description 根据RoleVO中的信息，在数据库的Role表中若已存在id相同的记录，则更新表中的相应记录；不存在则添加一条新记录。
 */
export function newOrUpdatePrivilegeVO(data: PrivilegeVO) {
  return request<RPrivilegeVO>(`/rolePrivilege/newOrUpdatePrivilegeVO`, {
    method: 'post',
    data,
  });
}

/**
 * 查找角色
 * @description 按动态条件，获取满足相应条件的角色的基本信息。如果有主键，按主键精确查找，若无主键，则其它条件按与操作进行模糊查询。所有条件均为空时，返回全部记录。
 */
export function findRoleByCriteria(data: RoleCriteria) {
  return request<REntityVOPage>(`/rolePrivilege/findRoleByCriteria`, {
    method: 'post',
    data,
  });
}

/**
 * 查找权限
 * @description 按动态条件，获取满足相应条件的权限的基本信息。如果有主键，按主键精确查找，若无主键，则其它条件按与操作进行模糊查询。所有条件均为空时，返回全部记录。
 */
export function findPrivilegeByCriteria(data: PrivilegeCriteria) {
  return request<REntityVOPage>(`/rolePrivilege/findPrivilegeByCriteria`, {
    method: 'post',
    data,
  });
}

/**
 * 赋权或更新赋权
 * @description 根据角色记录主键ID和权限ID，给角色赋予或更新一个权限。
 */
export function assignRole(data: RolePrivilegeDTO) {
  return request<RRolePrivilegeVO>(`/rolePrivilege/assignPrivilege`, {
    method: 'post',
    data,
  });
}

/**
 * 查看角色合规的赋权记录
 * @description 根据用户ID，查看角色合规的权限赋予记录。
 */
export function findValidRolePrivilegeByRoleId(roleId: number, params?: { roleId: number }) {
  return request<RListRolePrivilegeVO>(`/rolePrivilege/findValidRolePrivilegeByRoleId/${roleId}`, {
    method: 'get',
    params,
  });
}

/**
 * 查看角色赋予权限记录
 * @description 根据用户ID，查看给角色赋予权限的全部记录。
 */
export function findRolePrivilegeByRoleIdVO(roleId: number, params?: { roleId: number }) {
  return request<RListRolePrivilegeVO>(`/rolePrivilege/findRolePrivilegeByRoleId/${roleId}`, {
    method: 'get',
    params,
  });
}

/**
 * 查询角色数据记录
 * @description 按角色名，模糊查找角色数据记录。
 */
export function findRoleByRoleNameContains(roleName: string, params?: { roleName: string }) {
  return request<RListRoleVO>(`/rolePrivilege/findRoleByRoleNameContains/${roleName}`, {
    method: 'get',
    params,
  });
}

/**
 * 查询权限数据记录
 * @description 按权限名，模糊查找权限数据记录。
 */
export function findPrivilegeByPrivilegeNameContains(PrivilegeName: string, params?: { PrivilegeName: string }) {
  return request<RListPrivilegeVO>(`/rolePrivilege/findPrivilegeByPrivilegeNameContains/${PrivilegeName}`, {
    method: 'get',
    params,
  });
}

/**
 * 分页查询全部角色数据记录
 * @description 查询出系统中的全部角色数据记录。
 */
export function findAllRole(pageNum: number, pageSize: number, params?: { pageNum: number; pageSize: number }) {
  return request<REntityVOPage>(`/rolePrivilege/findAllRole/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 分页查询全部权限数据记录
 * @description 查询出系统中的全部权限数据记录。
 */
export function findAllPrivilege(pageNum: number, pageSize: number, params?: { pageNum: number; pageSize: number }) {
  return request<REntityVOPage>(`/rolePrivilege/findAllPrivilege/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除赋予权限记录
 * @description 根据角色权限记录ID，删除给角色赋予权限的记录。
 */
export function deleteUserRoleById(rolePrivilegeId: Array<number>, params?: { rolePrivilegeId: Array<number> }) {
  return request<R>(`/rolePrivilege/deleteRolePrivilegeById/${rolePrivilegeId}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除赋予权限记录
 * @description 根据角色记录主键ID和权限ID，删除给角色赋予的某种权限的记录。
 */
export function deleteRolePrivilege(
  roleId: number,
  privilegeId: number,
  params?: { roleId: number; privilegeId: number }
) {
  return request<R>(`/rolePrivilege/deleteRolePrivilege/${roleId}/${privilegeId}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除角色记录
 * @description 根据角色记录主键ID,删除一条或多条角色记录。
 */
export function deleteRoleById(roleId: Array<number>, params?: { roleId: Array<number> }) {
  return request<R>(`/rolePrivilege/deleteRoleByIds/${roleId}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除权限记录
 * @description 根据权限记录主键ID,删除一条或多条权限记录。
 */
export function deletePrivilegeById_1(privilegeId: Array<number>, params?: { privilegeId: Array<number> }) {
  return request<R>(`/rolePrivilege/deletePrivilegeByIds/${privilegeId}`, {
    method: 'get',
    params,
  });
}
