<template>
  <div class="flex h-full flex-col overflow-hidden">
    <h2 class="flex h-[60px] items-center bg-w pl-5 text-xl font-semibold">脱敏算法模板</h2>

    <div class="mx-5 mt-5 flex justify-between rounded bg-w px-10 pt-[18px]">
      <el-form ref="formRef" inline :model="form" class="w-0 flex-1" :rules="rules">
        <el-form-item label="模板名称:" prop="name">
          <el-input v-model="form.name" placeholder="请输入模板名称" maxlength="50" />
        </el-form-item>
        <el-form-item label="匹配类型:" prop="type">
          <el-select v-model="form.type" placeholder="请选择匹配类型" style="width: 100%">
            <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间:" prop="date">
          <el-select v-model="form.date" placeholder="请选择创建时间" style="width: 100%">
            <el-option v-for="item in dateOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <div class="ml-5">
        <el-button type="primary" @click="onSearch"> 查询 </el-button>
        <el-button @click="onReset"> 重置 </el-button>
      </div>
    </div>

    <div class="m-5 flex h-0 flex-1 flex-col rounded-md bg-w pt-5">
      <div class="flex justify-between px-10">
        <div>
          <el-button type="primary" @click="onAdd"> 添加 </el-button>
          <el-popconfirm width="180" title="确定删除选中数据？" @confirm="onDel">
            <template #reference>
              <el-button :disabled="checkList.length <= 0"> 删除 </el-button>
            </template>
          </el-popconfirm>
        </div>
      </div>

      <div class="mt-3 h-0 w-full flex-1 px-10">
        <el-table
          height="100%"
          :data="tableData"
          style="width: 100%"
          class="c-table-header"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column label="模板ID">
            <template #default="{ row }">
              <el-button link type="primary" @click="onView(row)">
                {{ row.templateId }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="模板名称" min-width="100px" />
          <el-table-column prop="count" label="脱敏算法数" />
          <el-table-column label="状态" width="120">
            <template #default="{ row }">
              <el-switch v-model="row.state" />
            </template>
          </el-table-column>
          <el-table-column prop="date" label="创建时间" />
          <el-table-column label="操作" width="150">
            <template #default="{ row }">
              <el-button link type="primary" @click="onEdit(row)"> 编辑 </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="pagination-bottom">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :page-size="pagination.pageSize"
          :total="tableData.length"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>

  <Drawer v-model="showDrawer" :data="drawerData" :readonly="readonly" @success="onSuccess" />
</template>

<script setup>
  import Drawer from './components/Drawer.vue';

  const typeOptions = ref([
    {
      value: '1',
      label: '全部',
    },
  ]);
  const dateOptions = ref([
    {
      value: '1',
      label: '全部',
    },
    {
      value: '2',
      label: '今天',
    },
    {
      value: '3',
      label: '近一周',
    },
    {
      value: '4',
      label: '近一月',
    },
    {
      value: '5',
      label: '近一年',
    },
  ]);
  const form = reactive({
    name: '',
    type: '1',
    date: '1',
  });
  const rules = reactive({
    name: [{ required: false }],
    type: [{ required: false }],
    date: [{ required: false }],
  });
  const formRef = ref();
  const onSearch = () => {
    console.log(form);
  };
  const onReset = () => {
    formRef.value.resetFields();
    onSearch();
  };

  const tableData = ref([
    {
      id: 1,
      templateId: '00001',
      name: '脑疾病数据研究',
      count: 5,
      state: true,
      date: '2022-03-12 12:00:00',
    },
    {
      id: 2,
      templateId: '00002',
      name: '肿瘤研究',
      count: 2,
      state: false,
      date: '2022-11-26 16:00:00',
    },
  ]);
  const checkList = ref([]);
  const handleSelectionChange = (val) => {
    checkList.value = val;
  };

  const showDrawer = ref(false);
  const readonly = ref(false);
  let drawerData = ref({});
  const onAdd = () => {
    drawerData.value = {};
    readonly.value = false;
    showDrawer.value = true;
  };
  const onView = (row) => {
    drawerData.value = row;
    readonly.value = true;
    showDrawer.value = true;
  };
  const onDel = () => {
    console.log(checkList.value);
  };
  const onEdit = (row) => {
    drawerData.value = row;
    readonly.value = false;
    showDrawer.value = true;
  };
  const onSuccess = () => {};

  //页码
  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  const handleCurrentChange = (e) => {
    pagination.page = e;
  };
  const handleSizeChange = (e) => {
    pagination.pageSize = e;
  };
</script>
