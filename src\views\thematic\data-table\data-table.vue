<template>
  <div class="flex h-full flex-col overflow-hidden">
    <h2 class="flex h-[60px] items-center bg-w pl-5 text-xl font-semibold">数据表</h2>

    <div class="m-5 flex h-0 flex-1">
      <div class="mr-[10px] h-full w-[280px] rounded bg-w">
        <CustomMenu v-model="dataBaseId" :data="catalogData" />
      </div>

      <div class="flex w-0 flex-1 flex-col rounded-md bg-w pt-5">
        <div class="flex justify-between px-10">
          <div>
            <el-button type="primary" @click="onAdd"> 新增 </el-button>
            <el-button :disabled="checkList.length <= 0" :loading="batchLoading" @click="onBatchDel">
              批量删除
            </el-button>
          </div>
          <!-- <el-input v-model="search" placeholder="请输入关键字" style="width: 500px">
            <template #prepend>
              <el-select v-model="searchPre" style="width: 130px">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </template>
            <template #append>
              <el-button :icon="Search" @click="onSearch" />
            </template>
          </el-input> -->
        </div>

        <div class="mt-3 h-0 w-full flex-1 px-10">
          <el-table
            v-loading="tableLoading"
            height="100%"
            :data="tableData"
            style="width: 100%"
            class="c-table-header"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column type="index" width="55" label="序号" />
            <el-table-column label="表名称">
              <template #default="{ row }">
                <a @click="onViewFiled(row)">{{ row.tableChineseName }}<span v-if="row.visitPhaseChinese">（{{ row.visitPhaseChinese }}）</span></a>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="描述" />
            <el-table-column label="是否源数据映射">
              <template #default="{ row }">
                <span>{{ row.hasTableInDataSource ? '是' : '否' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="180">
              <template #default="{ row }">
                <el-button link type="primary" @click="onViewFiled(row)"> 查看字段 </el-button>
                <el-button link type="primary" @click="onEdit(row)"> 编辑 </el-button>
                <el-popconfirm title="确定删除此项？" @confirm="onDel(row)">
                  <template #reference>
                    <el-button link type="primary"> 删除 </el-button>
                  </template>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- <div class="pagination-bottom">
          <el-pagination
            background
            layout="total, sizes, prev, pager, next, jumper"
            :page-size="pagination.pageSize"
            :total="tableData.length"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div> -->
      </div>
    </div>
  </div>

  <Drawer v-model="showDrawer" :data="drawerData" @success="onSuccess" />

  <el-dialog v-model="showDesc" title="编辑表" width="500px" @close="onCancelDesc">
    <el-input v-model="currentItem.description" placeholder="请输入描述" maxlength="200" />
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="onCancelDesc">取消</el-button>
        <el-button type="primary" :loading="editLoding" @click="onSaveDesc">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
  import { findAllDb, findAllTableVOByDbId, deleteTableById, addTable } from '@/api/index';
  import { Search } from '@element-plus/icons-vue';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import Drawer from './components/Drawer.vue';
  import CustomMenu from '@/components/custom-menu/CustomMenu.vue';
  import { useRouter } from 'vue-router';
  import { cloneDeep } from 'lodash-es';
  const router = useRouter();

  //目录
  const dataBaseId = ref('');
  const catalogData = ref([
    // {
    //   id: '1',
    //   title: 'mysql数据库',
    //   svgname: 'icon-shujuku1',
    // },
  ]);
  fetchDb();
  async function fetchDb() {
    try {
      const { data } = await findAllDb();
      catalogData.value = data.map((item) => {
        item.title = item.databaseName;
        item.svgname = 'icon-shujuku1';
        return item;
      });
      dataBaseId.value = catalogData.value[0].id;
    } catch (error) {
      console.log(error);
    }
  }

  //表格
  const checkList = ref([]);
  const handleSelectionChange = (val) => {
    checkList.value = val;
  };
  const batchLoading = ref(false);
  const onBatchDel = () => {
    ElMessageBox.confirm('确定删除所有选中项？', '操作提示', { type: 'warning' })
      .then(async () => {
        try {
          batchLoading.value = true;
          await Promise.all(checkList.value.map((row) => deleteTableById(row.id, true)));
          ElMessage({ type: 'success', message: '删除成功' });
          fetchTable();
        } catch (error) {
          console.log(error);
        } finally {
          batchLoading.value = false;
        }
      })
      .catch(() => {});
  };
  const tableData = ref([]);
  const tableLoading = ref(false);
  watch(dataBaseId, () => {
    fetchTable();
  });
  async function fetchTable() {
    try {
      tableLoading.value = true;
      const { data } = await findAllTableVOByDbId(dataBaseId.value);
      tableData.value = data[0];
    } catch (error) {
      console.log(error);
    } finally {
      tableLoading.value = false;
    }
  }
  const onDel = async (row) => {
    try {
      await deleteTableById(row.id, true);
      fetchTable();
    } catch (error) {
      console.log(error);
    }
  };
  const onViewFiled = (row) => {
    router.push({ name: 'ThematicDataField', params: { tableId: row.id, dbId: dataBaseId.value } });
  };

  const searchPre = ref('标准名称');
  const search = ref('');
  const options = [
    {
      value: '标准名称',
      label: '标准名称',
    },
    {
      value: '标准号',
      label: '标准号',
    },
    {
      value: '标准字典',
      label: '标准字典',
    },
  ];
  const onSearch = () => {};

  const showDrawer = ref(false);
  const onAdd = () => {
    drawerData.value = {
      dataBaseId: dataBaseId.value,
    };
    showDrawer.value = true;
  };
  let drawerData = ref({});
  function onEdit(row) {
    currentItem.value = cloneDeep(row);
    showDesc.value = true;
  }
  const onSuccess = () => {
    fetchTable();
  };

  //页码
  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  const handleCurrentChange = (e) => {
    pagination.page = e;
  };
  const handleSizeChange = (e) => {
    pagination.pageSize = e;
  };

  //编辑表
  const showDesc = ref(false);
  const editLoding = ref(false);
  const currentItem = ref({});
  const onCancelDesc = () => {
    showDesc.value = false;
  };
  const onSaveDesc = async () => {
    try {
      editLoding.value = true;
      await addTable(currentItem.value);
      ElMessage({ type: 'success', message: '保存成功' });
      onCancelDesc();
      fetchTable();
    } catch (error) {
      console.log(error);
    } finally {
      editLoding.value = false;
    }
  };
</script>
