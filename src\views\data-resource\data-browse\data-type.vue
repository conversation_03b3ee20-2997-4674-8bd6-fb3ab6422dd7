<template>
  <div v-loading="loading" class="px-10 pt-5">
    <Breadcrumb :list="breadList" />

    <h2 class="mt-[25px] text-[28px] font-bold">{{ title }}（ID-{{ localId }}）</h2>
    <p class="mt-5 border-b border-border pb-[26px]">
      {{ description }}
    </p>

    <template v-if="total > 0">
      <h3 class="mt-[26px] text-xl">父类别（{{ total }}）</h3>
      <el-table :data="tableData" style="width: 100%" class="c-table-header mb-4 mt-4">
        <el-table-column prop="id" label="类别ID">
          <template #default="{ row }">
            <el-button link type="primary" @click="gotoType(row)">
              {{ row.id }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="title" label="名称" min-width="100px" />
        <!-- <el-table-column prop="dataItemCount" label="数据字段" /> -->
        <el-table-column prop="childrenCount" label="子类别" />
        <el-table-column prop="description" label="说明" />
      </el-table>
    </template>

    <template v-if="subTotal > 0">
      <h3 class="mt-[26px] text-xl">子类别（{{ subTotal }}）</h3>
      <el-table
        :data="subData"
        style="width: 100%"
        class="c-table-header mb-4 mt-4"
        @selection-change="handleSelectionChange2"
      >
        <el-table-column v-if="isSelectMode" type="selection" width="55" />
        <el-table-column prop="id" label="类别ID">
          <template #default="{ row }">
            <el-button link type="primary" @click="gotoType(row)">
              {{ row.id }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="metaCode" label="量表英文名称" min-width="100px" />
        <el-table-column prop="title" label="量表中文名称" min-width="100px">
          <template #default="{ row }">
            {{ row.title }}<span v-if="row.visitPhaseChinese">（{{ row.visitPhaseChinese }}）</span>
          </template>
        </el-table-column>
        <el-table-column prop="dataItemCount" label="数据字段" />
        <el-table-column prop="childrenCount" label="子类别" />
        <el-table-column prop="description" label="说明" />
      </el-table>
    </template>

    <template v-if="fieldTotal > 0">
      <h3 class="mt-[38px] text-xl">数据字段（{{ fieldTotal }}）</h3>
      <el-table
        :key="'fieldTable' + id"
        ref="fieldTableRef"
        :data="fieldData"
        style="width: 100%"
        class="c-table-header mb-4 mt-4"
        @selection-change="handleSelectionChange2"
      >
        <!-- <el-table-column v-if="isSelectMode" type="selection" width="55" /> -->
        <el-table-column prop="id" label="字段ID">
          <template #default="{ row }">
            <el-button link type="primary" @click="gotoFiled(row)">
              {{ row.id }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="tableName" label="量表英文名称" />
        <el-table-column prop="tableChineseName" label="量表中文名称">
          <template #default="{ row }">
            {{ row.tableChineseName }}<span v-if="row.visitPhaseChinese">（{{ row.visitPhaseChinese }}）</span>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="变量名称" />
        <el-table-column prop="chineseMeaning" label="变量中文含义" />
        <el-table-column label="类型">
          <template #default="{ row }">
            <span>{{ fieldTypeText(row.valueType) }}</span>
          </template>
        </el-table-column>
      </el-table>
    </template>
  </div>
</template>

<script setup lang="ts">
  /* 数据浏览-分类 */
  import { getCatalogue, getChildCatalogue, getParentCatalogue, getMedicalFieldInCatalogue } from '@/api/index';
  import Breadcrumb from './components/Breadcrumb.vue';
  import { useDataBrowse } from '@/store/index';
  import { ElTable } from 'element-plus';
  import { fieldTypeText } from '@/utils/format';
  const storeBrowse = useDataBrowse();
  const isSelectMode = computed(() => {
    return storeBrowse.dataBrowse.isSelectMode;
  });
  const props = defineProps({
    id: {
      type: String,
    },
  });
  const emit = defineEmits(['change-type', 'goto-filed']);
  const loading = ref(true);
  const localId = ref('');

  const breadList = ref([]);
  //父类别
  const tableData = ref<CatalogueVO[]>([]);
  const total = computed(() => {
    return tableData.value.length;
  });
  //子类别
  const subData = ref<CatalogueVO[]>([]);
  const subTotal = computed(() => {
    return subData.value.length;
  });
  const checkList1 = ref<CatalogueVO[]>([]);
  const handleSelectionChange1 = (val) => {
    checkList1.value = val;
  };

  //数据字段
  const fieldData = ref<MedicalFieldVO[]>([]);
  const fieldTotal = computed(() => {
    return fieldData.value.length;
  });
  const fieldTableRef = ref<InstanceType<typeof ElTable>>();
  const handleSelectionChange2 = (val: MedicalFieldVO[]) => {
    storeBrowse.setFileds(
      storeBrowse.dataBrowse.id,
      val.map((item) => item.id!)
    );
  };

  const gotoType = (row) => {
    emit('change-type', row.id);
  };

  const gotoFiled = (row) => {
    emit('goto-filed', row.id);
  };

  const setFiledSelection = () => {
    const fileds = storeBrowse.dataBrowse.fileds;
    const existItem = fileds.find((f) => f.id === storeBrowse.dataBrowse.id);
    if (existItem) {
      existItem.data.forEach((f) => {
        fieldData.value.forEach((v) => {
          if (v.id === f) {
            fieldTableRef.value?.toggleRowSelection(v, true);
          }
        });
      });
    }
  };

  const title = ref('');
  const description = ref('');
  let childrenCount = 0;
  let fieldCount = 0;
  let parentId = 0;
  async function loadData() {
    await fetchData();
    if (parentId) {
      fetchParent();
    } else {
      tableData.value = [];
    }
    if (childrenCount > 0) {
      fetchChildren();
    } else {
      subData.value = [];
    }
    if (fieldCount > 0) {
      fetchFieldList();
    } else {
      fieldData.value = [];
    }
  }
  async function fetchData() {
    try {
      loading.value = true;
      const { data } = await getCatalogue(+localId.value);
      title.value = data?.title || '';
      description.value = data?.description || '';
      childrenCount = data?.childrenCount || 0;
      fieldCount = data?.dataItemCount || 0;
      parentId = data?.parentId || 0;
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }
  async function fetchChildren() {
    try {
      const { data } = await getChildCatalogue(+localId.value);
      subData.value = data || [];
    } catch (error) {
      console.log(error);
    }
  }
  async function fetchParent() {
    try {
      const { data } = await getParentCatalogue(+localId.value);
      if (data) {
        tableData.value = [data];
      } else {
        tableData.value = [];
      }
    } catch (error) {
      console.log(error);
    }
  }
  async function fetchFieldList() {
    try {
      const { data } = await getMedicalFieldInCatalogue(+localId.value);
      fieldData.value = data || [];
      nextTick(() => {
        setFiledSelection();
      });
    } catch (error) {
      console.log(error);
    }
  }

  watch(
    () => props.id,
    () => {
      localId.value = props.id!;
      loadData();
    },
    {
      immediate: true,
    }
  );
</script>

<style lang="scss" scoped>
  .c-table-header {
    :deep(.el-checkbox__inner) {
      border-color: #007f99;
    }
  }
</style>
