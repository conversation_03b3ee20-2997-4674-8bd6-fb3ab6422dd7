<template>
  <div class="pl-10 pr-10 pt-4 text-sm">
    <h4 class="text-xl font-bold">账号安全</h4>

    <div class="border-bottom mt-6">
      <div class="flex w-full justify-between">
        <div class="flex w-1/2 flex-col justify-start">
          <span>用户名</span>
          <span class="my-5 text-[#a5a9a9]"> {{ userInfo.name }} </span>
        </div>
        <!-- <div class="flex flex-col justify-end">
          <span class="mb-5 cursor-pointer text-sm text-p" @click="clickchange(1)">修改</span>
        </div> -->
      </div>
    </div>

    <div class="border-bottom mt-4">
      <div class="flex w-full justify-between">
        <div class="flex w-1/2 flex-col justify-start">
          <span>密码</span>
          <!-- <span class="my-5 text-[#a5a9a9]"> 当前密码强度: 中 </span> -->
        </div>
        <div class="flex flex-col justify-end">
          <span class="mb-5 cursor-pointer text-sm text-p" @click="clickchange(2)">修改</span>
        </div>
      </div>
    </div>

    <!-- <div class="border-bottom mt-4">
      <div class="flex w-full justify-between">
        <div class="flex w-1/2 flex-col justify-start">
          <span>邮箱</span>
          <span class="my-5 text-[#a5a9a9]"> {{ userInfo.email }} </span>
        </div>
        <div class="flex flex-col justify-end">
          <span class="mb-5 cursor-pointer text-sm text-p" @click="clickchange(3)">修改</span>
        </div>
      </div>
    </div> -->
  </div>

  <el-drawer v-model="drawer1" class="relative">
    <template #header>
      <h4 class="text-m">修改用户名</h4>
    </template>
    <el-form ref="userNameForm" label-position="top" :model="form" :rules="rules">
      <el-form-item label="新用户名：" prop="userName">
        <el-tooltip effect="light" placement="bottom-start">
          <template #content>
            <div class="text-tip">
              <div class="text-p">长度为4-16位字符</div>
              <div class="mt-1">可使用英文、数字、下划线组合，不能只有数字或下划线，必须包含英文</div>
            </div>
          </template>
          <el-input v-model="form.userName" placeholder="请输入新用户名" maxlength="16" />
        </el-tooltip>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="onCancelUser"> 取消 </el-button>
      <el-button color="#007f99" type="primary" @click="onSaveUserName"> 保存 </el-button>
    </template>
  </el-drawer>

  <el-drawer v-model="drawer2" class="relative" direction="rtl">
    <template #header>
      <h4 class="text-m">修改密码</h4>
    </template>
    <el-form ref="passFormRef" label-position="top" :model="passForm" :rules="passRules">
      <el-form-item label="原密码：" prop="password">
        <el-input v-model="passForm.password" type="password" placeholder="请输入原密码" maxlength="20" />
      </el-form-item>
      <el-form-item label="新密码：" prop="newPass">
        <el-tooltip effect="light" placement="right-start" :visible="pwdVisible">
          <template #content>
            <div class="text-xs text-tip">
              <div class="mb-1">安全程度：{{ safeStrength }}</div>
              <el-progress :stroke-width="8" :percentage="pwdProgress" :show-text="false" />
              <ul class="mt-2 pl-5">
                <li>最少8个字符</li>
                <li>不能全为数字、字母或特殊符号</li>
                <li>数字、字母、特殊字符任意组合</li>
              </ul>
            </div>
          </template>
          <el-input
            v-model="passForm.newPass"
            type="password"
            placeholder="请输入"
            maxlength="20"
            @input="onPwdInput"
            @focus="onPwdFocus"
            @blur="onPwdBlur"
          />
        </el-tooltip>
      </el-form-item>
      <el-form-item label="确认新密码：" prop="confirmPass">
        <el-input v-model="passForm.confirmPass" type="password" placeholder="请再次输入新密码" maxlength="20" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="onCancelPass"> 取消 </el-button>
      <el-button color="#007f99" type="primary" :loading="passwordLoading" @click="onSavePass"> 保存 </el-button>
    </template>
  </el-drawer>

  <!-- <el-drawer v-model="drawer3" class="relative" direction="rtl">
    <template #header>
      <h4 class="text-m">修改邮箱</h4>
    </template>
    <el-form ref="emailRef" label-position="top" :model="emailForm" :rules="emailRules">
      <el-form-item label="新邮箱：" prop="email">
        <el-input v-model="emailForm.email" placeholder="请输入" maxlength="50" />
      </el-form-item>
      <el-form-item label="邮箱验证码：" prop="code">
        <div class="flex items-center">
          <el-input v-model="emailForm.code" placeholder="请输入" maxlength="8" />
          <el-button v-disable-button="30" class="ml-5" plain @click="getCode"> 获取验证码 </el-button>
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="onCancelEmail"> 取消 </el-button>
      <el-button color="#007f99" type="primary" @click="onSaveEmail"> 保存 </el-button>
    </template>
  </el-drawer> -->
</template>

<script setup lang="ts">
  /* 账号安全 */
  import { getUserInfor } from '@/api/index';
  import { validateUsername, validatePassword } from '@/utils/validator';
  import { usePasswordStrength } from '@/utils/form';
  import { ElMessage } from 'element-plus';
  import { useUsers } from '@/store/user-info.js';
  const store = useUsers();
  const { pwdVisible, safeStrength, pwdProgress, onPwdInput, onPwdFocus, onPwdBlur } = usePasswordStrength();
  import { useRouter } from 'vue-router';
  const router = useRouter();

  const loading = ref(false);
  let userInfo = reactive({
    name: '',
    pass: '',
    // email: '',
  });

  const drawer1 = ref(false);
  let form = reactive({
    userName: '',
  });
  let rules = reactive({
    userName: [
      { required: true, message: '请输入新用户名', trigger: 'blur' },
      {
        validator: validateUsername,
      },
    ],
  });
  const userNameForm = ref();
  const onSaveUserName = async () => {
    try {
      await userNameForm.value.validate();
      drawer1.value = false;
    } catch (error) {
      console.log(error);
    }
  };
  const onCancelUser = () => {
    drawer1.value = false;
  };

  const drawer2 = ref(false);
  let passForm = reactive({
    password: '',
    newPass: '',
    confirmPass: '',
  });
  let passRules = reactive({
    password: [{ required: true, message: '请输入原密码', trigger: 'blur' }],
    newPass: [
      { required: true, message: '请输入新密码', trigger: 'blur' },
      {
        validator: validatePassword,
      },
    ],
    confirmPass: [
      { required: true, message: '请输入确认密码', trigger: 'blur' },
      {
        validator: validateConfirmPassword,
      },
    ],
  });
  const passFormRef = ref();
  const passwordLoading = ref(false);
  const onSavePass = async () => {
    try {
      passwordLoading.value = true;
      await passFormRef.value.validate();
      await store.changePassword({
        oldPassword: passForm.password,
        password: passForm.newPass,
      });
      ElMessage({ type: 'success', message: '修改密码成功' });
      await store.quit();
      router.push({ name: 'Login' });
      drawer2.value = false;
    } catch (error) {
      console.log(error);
    } finally {
      passwordLoading.value = false;
    }
  };
  const onCancelPass = () => {
    drawer2.value = false;
  };

  const drawer3 = ref(false);
  let emailForm = reactive({
    email: '',
    code: '',
  });
  let emailRules = reactive({
    email: [
      { required: true, message: '请输入邮箱', trigger: 'blur' },
      {
        type: 'email',
        message: '请输入有效的邮箱地址',
        trigger: ['blur', 'change'],
      },
    ],
    code: [{ required: true, message: '请输入验证码', trigger: 'blur' }],
  });
  const emailRef = ref();
  const onSaveEmail = async () => {
    try {
      await emailRef.value.validate();
      drawer3.value = false;
    } catch (error) {
      console.log(error);
    }
  };
  const onCancelEmail = () => {
    drawer3.value = false;
  };

  const clickchange = (e) => {
    if (e == 1) {
      drawer1.value = true;
    } else {
      if (e == 2) {
        drawer2.value = true;
      } else {
        drawer3.value = true;
      }
    }
  };

  function validateConfirmPassword(rule, value, callback) {
    // 自定义校验规则函数，用于确认密码是否与密码一致
    if (value !== passForm.newPass) {
      callback(new Error('两次输入的密码不一致'));
    } else {
      callback();
    }
  }

  fetchBase();
  async function fetchBase() {
    try {
      loading.value = true;
      const { data } = await getUserInfor(store.user.username, { userName: store.user.username });
      if (data) {
        userInfo.name = data.userName!;
      }
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }
</script>

<style lang="scss" scoped>
  .border-bottom {
    border-bottom: 1px solid #e1e3e6;
  }
</style>
