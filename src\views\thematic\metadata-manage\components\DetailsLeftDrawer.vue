<template>
  <el-drawer v-model="drawer" class="relative" @close="onCancel">
    <template #header>
      <h4 class="text-m">元数据编辑</h4>
    </template>

    <el-form ref="formRef" :model="form" />
    <el-form ref="formRef" label-position="top" :model="form" :rules="rules" class="mt-3">
      <el-form-item label="变更字段">
        <el-input v-model="form.changedField" disabled />
      </el-form-item>
      <el-form-item label="版本号" prop="versionNumber">
        <el-input v-model="form.versionNumber" />
      </el-form-item>
      <el-form-item label="原来的值">
        <el-input v-model="form.originalValue" disabled />
      </el-form-item>
      <el-form-item label="最新的值" prop="newestValue">
        <el-input v-model="form.newestValue" />
      </el-form-item>
      <el-form-item label="备注">
        <el-input v-model="form.remark" type="textarea" />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="onCancel"> 取消 </el-button>
      <el-button type="primary" @click="onSave"> 保存 </el-button>
    </template>
  </el-drawer>
</template>

<script setup>
  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
      required: true,
    },
    data: {
      type: Object,
      required: true,
    },
    readonly: {
      type: Boolean,
      required: false,
    },
  });

  const drawer = ref(false);
  let form = reactive({
    changedField: '',
    versionNumber: '',
    originalValue: '',
    newestValue: '',
    remark: '',
  });
  const rules = reactive({
    versionNumber: [{ required: true, message: '请输入版本号', trigger: 'blur' }],
    newestValue: [{ required: true, message: '请输入最新的值', trigger: 'blur' }],
  });

  watchEffect(() => {
    drawer.value = props.modelValue;
  });
  watch(
    () => props.data,
    (value) => {
      for (const key in form) {
        form[key] = value[key];
      }
    }
  );
  const emit = defineEmits(['update:modelValue', 'success']);
  const formRef = ref();
  const onCancel = () => {
    emit('update:modelValue', false);
    formRef.value.resetFields();
  };
  const onSave = () => {
    formRef.value.validate((valid) => {
      if (valid) {
        onCancel();
        emit('success');
      }
    });
  };
</script>
