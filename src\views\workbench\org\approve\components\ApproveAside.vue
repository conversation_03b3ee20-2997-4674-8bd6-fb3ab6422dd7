<template>
  <div class="h-full w-[400px] rounded bg-w">
    <el-tabs v-model="tabName" class="tabs flex h-full flex-col">
      <el-scrollbar height="100%">
        <el-tab-pane label="全部" name="1">
          <div class="overflow-hidden px-5 pt-5">
            <el-input v-model="search" placeholder="请输入关键字">
              <template #prepend>
                <el-select v-model="prepend" style="width: 100px">
                  <el-option label="申请人" value="申请人" />
                  <el-option label="部门" value="部门" />
                  <el-option label="职位" value="职位" />
                </el-select>
              </template>
              <template #append>
                <el-button :icon="Search" @click="onSearch" />
              </template>
            </el-input>

            <ul class="list mt-5 text-sm">
              <li
                v-for="(item, index) in allList"
                :key="index"
                class="mb-3 cursor-pointer rounded border border-border p-4 hover:shadow-md"
                :class="active == index ? 'active' : ''"
                @click="selectItem(item, index)"
              >
                <div class="flex items-center justify-between">
                  <span class="text-base font-bold">{{ item.title }}</span>
                  <div :class="statusClass[item.status]" class="h-6 px-3 leading-6">
                    <span>{{ statusText[item.status] }}</span>
                  </div>
                </div>

                <div class="mt-2">
                  <span class="text-[#aeb1b2]">姓名 </span>
                  <span class="ml-2 text-[#595e5f]">{{ item.userName }}</span>
                </div>
                <div class="mt-2">
                  <span class="text-[#aeb1b2]">部门 </span>
                  <span class="ml-2 text-[#595e5f]">{{ item.department }}</span>
                </div>
                <div class="mt-2">
                  <span class="text-[#aeb1b2]">职位 </span>
                  <span class="ml-2 text-[#595e5f]">{{ item.posts }}</span>
                </div>
                <div class="mt-2">
                  <span class="text-[#aeb1b2]">{{ item.startDate }}</span>
                </div>
              </li>
            </ul>
          </div>
        </el-tab-pane>

        <el-tab-pane label="待审批" name="2">
          <div class="overflow-hidden px-5 pt-5">
            <el-input v-model="search" placeholder="请输入关键字">
              <template #prepend>
                <el-select v-model="prepend" style="width: 100px">
                  <el-option label="申请人" value="申请人" />
                  <el-option label="部门" value="部门" />
                  <el-option label="职位" value="职位" />
                </el-select>
              </template>
              <template #append>
                <el-button :icon="Search" @click="onSearch" />
              </template>
            </el-input>

            <ul class="list mt-5 text-sm">
              <li
                v-for="(item, index) in todoList"
                :key="index"
                class="mb-3 cursor-pointer rounded border border-border p-4 hover:shadow-md"
                :class="active == index ? 'active' : ''"
                @click="selectItem(item, index)"
              >
                <div class="flex items-center justify-between">
                  <span class="text-base font-bold">{{ item.title }}</span>
                  <div :class="statusClass[item.status]" class="h-6 px-3 leading-6">
                    <span>{{ statusText[item.status] }}</span>
                  </div>
                </div>

                <div class="mt-2">
                  <span class="text-[#aeb1b2]">姓名 </span>
                  <span class="ml-2 text-[#595e5f]">{{ item.userName }}</span>
                </div>
                <div class="mt-2">
                  <span class="text-[#aeb1b2]">部门 </span>
                  <span class="ml-2 text-[#595e5f]">{{ item.department }}</span>
                </div>
                <div class="mt-2">
                  <span class="text-[#aeb1b2]">职位 </span>
                  <span class="ml-2 text-[#595e5f]">{{ item.posts }}</span>
                </div>
                <div class="mt-2">
                  <span class="text-[#aeb1b2]">{{ item.startDate }}</span>
                </div>
              </li>
            </ul>
          </div>
        </el-tab-pane>

        <el-tab-pane label="已审批" name="3">
          <div class="overflow-hidden px-5 pt-5">
            <el-input v-model="search" placeholder="请输入关键字">
              <template #prepend>
                <el-select v-model="prepend" style="width: 100px">
                  <el-option label="申请人" value="申请人" />
                  <el-option label="部门" value="部门" />
                  <el-option label="职位" value="职位" />
                </el-select>
              </template>
              <template #append>
                <el-button :icon="Search" @click="onSearch" />
              </template>
            </el-input>

            <ul class="list mt-5 text-sm">
              <li
                v-for="(item, index) in doneList"
                :key="index"
                class="mb-3 cursor-pointer rounded border border-border p-4 hover:shadow-md"
                :class="active == index ? 'active' : ''"
                @click="selectItem(item, index)"
              >
                <div class="flex items-center justify-between">
                  <span class="text-base font-bold">{{ item.title }}</span>
                  <div :class="statusClass[item.status]" class="h-6 px-3 leading-6">
                    <span>{{ statusText[item.status] }}</span>
                  </div>
                </div>

                <div class="mt-2">
                  <span class="text-[#aeb1b2]">姓名 </span>
                  <span class="ml-2 text-[#595e5f]">{{ item.userName }}</span>
                </div>
                <div class="mt-2">
                  <span class="text-[#aeb1b2]">部门 </span>
                  <span class="ml-2 text-[#595e5f]">{{ item.department }}</span>
                </div>
                <div class="mt-2">
                  <span class="text-[#aeb1b2]">职位 </span>
                  <span class="ml-2 text-[#595e5f]">{{ item.posts }}</span>
                </div>
                <div class="mt-2">
                  <span class="text-[#aeb1b2]">{{ item.startDate }}</span>
                </div>
              </li>
            </ul>
          </div>
        </el-tab-pane>
      </el-scrollbar>
    </el-tabs>
  </div>
</template>

<script setup>
  import { Search } from '@element-plus/icons-vue';
  const props = defineProps({ id: { type: [String, Number] } });

  const tabName = ref('1');
  const search = ref('');
  const prepend = ref('申请人');
  const onSearch = () => {
    console.log(prepend.value);
    console.log(search.value);
  };

  const statusText = {
    0: '待审批',
    1: '通过',
    2: '驳回',
  };
  const statusClass = {
    0: 'status-todo',
    1: 'status-pass',
    2: 'status-reject',
  };
  let allList = ref([
    {
      id: 1,
      title: '加入机构申请',
      status: 0,
      userName: '张筱雨',
      department: 'XXXXX部门 / 技术部',
      posts: '技术经理',
      startDate: '2022-08-11 10:00:00',
      gender: 1,
      documentType: '身份证',
      idnumber: '545125863241456652',
      degree: '本科',
      jobTitle: '高级工程师',
      phone: '***********',
    },
    {
      id: 2,
      title: '加入机构申请',
      status: 1,
      userName: '黎明',
      department: 'XXXXX部门 / 技术部',
      posts: '技术经理',
      startDate: '2022-08-11 10:00:00',
      gender: 0,
      documentType: '身份证',
      idnumber: '545125863241456652',
      degree: '本科',
      jobTitle: '高级工程师',
      phone: '***********',
      opinion: '这里是一段审批意见',
    },
    {
      id: 3,
      title: '加入机构申请',
      status: 2,
      userName: '张学友',
      department: 'XXXXX部门 / 技术部',
      posts: '技术经理',
      startDate: '2022-08-11 10:00:00',
      gender: 0,
      documentType: '身份证',
      idnumber: '545125863241456652',
      degree: '本科',
      jobTitle: '高级工程师',
      phone: '***********',
      opinion: '这里是一段审批意见',
    },
    {
      id: 4,
      title: '加入机构申请',
      status: 0,
      userName: '周星星',
      department: 'XXXXX部门 / 技术部',
      posts: '技术经理',
      startDate: '2022-08-11 10:00:00',
      gender: 0,
      documentType: '身份证',
      idnumber: '545125863241456652',
      degree: '本科',
      jobTitle: '高级工程师',
      phone: '***********',
    },
  ]);
  let active = ref(0);
  const emit = defineEmits(['select']);
  let selectItem = (item, index) => {
    active.value = index;
    emit('select', item);
  };
  let selectValue = allList.value[0];
  if (props.id) {
    allList.value.forEach((item, index) => {
      if (item.id == props.id) {
        selectValue = item;
        active.value = index;
      }
    });
  }
  selectItem(selectValue, active.value);

  const todoList = computed(() => {
    return allList.value.filter((item) => item.status === 0);
  });
  const doneList = computed(() => {
    return allList.value.filter((item) => item.status === 1 || item.status === 2);
  });
</script>

<style scoped lang="scss">
  .tabs {
    --el-border-color-light: #e1e3e6;

    :deep(.el-tabs__content) {
      height: 0;
      flex: 1;
      background: #fff;
      border-radius: 4px;
    }

    :deep(.el-tabs__header) {
      margin: 0;
    }
  }

  :deep(.el-tabs--top .el-tabs__item.is-top:nth-child(2)) {
    padding-left: 20px;
  }

  .list {
    .active {
      border-color: $color-primary;
    }

    .status-todo {
      color: #3a73e6;
      background-color: #ebf1fd;
    }

    .status-pass {
      color: #29b586;
      background-color: #e8f7f2;
    }

    .status-reject {
      color: #e64848;
      background-color: #fdecec;
    }
  }
</style>
