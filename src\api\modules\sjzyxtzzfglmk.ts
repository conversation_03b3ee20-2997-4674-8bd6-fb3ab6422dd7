/*
 * @OriginalName: 数据资源系统中支付管理模块
 * @Description: 支付信息的创建、更新和查询
 */
import { request } from '@/utils/request';

/**
 * 新建或更新数据表中的记录
 * @description 按tdto的信息，新建或更新数据表中的记录。
 */
export function newOrUpdateEntity_3(data: PaymentDTO) {
  return request<RPaymentVO>(`/payment/newOrUpdateEntity`, {
    method: 'post',
    data,
  });
}

/**
 * 查找订单
 * @description 按动态条件，获取满足相应条件的订单的基本信息。各条件按与操作进行模糊查询。所有条件均为空时，返回全部记录。
 */
export function findPaymentByCriteria(data: PaymentCriteria) {
  return request<RVOPage>(`/payment/findPaymentByCriteria`, {
    method: 'post',
    data,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条或多条数据记录。
 */
export function findEntityById_3(data: Array<number>) {
  return request<RListPaymentVO>(`/payment/findEntityByIdCollection`, {
    method: 'post',
    data,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一或多条记录。
 */
export function deleteEntityById_3(data: Array<number>) {
  return request<R>(`/payment/deleteEntityByIdCollection`, {
    method: 'post',
    data,
  });
}

/**
 * 生成二维码(PNG流)
 * @description 直接返回二进制图片流
 */
export function generateQRCode(params?: { content: string; width?: number; height?: number }) {
  return request<R>(`/payment/generateQRCode`, {
    method: 'get',
    params,
  });
}

/**
 * 查找订单信息
 * @description 按支付的Id，查询该笔支付的相关订单。
 */
export function findUserByByPaymentId(paymentId: number, params?: { paymentId: number }) {
  return request<RMDMUserVO>(`/payment/findUserByByPaymentId/${paymentId}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找支付信息
 * @description 按订单的Id，查询订单的相关支付信息。
 */
export function findPaymentByOrderId_02(orderId: number, params?: { orderId: number }) {
  return request<RListPaymentVO>(`/payment/findPaymentByOrderId/${orderId}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找订单信息
 * @description 按支付的Id，查询该笔支付的相关订单。
 */
export function findOrderByPaymentId(paymentId: number, params?: { paymentId: number }) {
  return request<RListOrderVO>(`/payment/findOrderByPaymentId/${paymentId}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条或多条数据记录。
 */
export function findEntityById_21(idArray: Array<object>, params?: { idArray: Array<object> }) {
  return request<RListPaymentVO>(`/payment/findEntityByIdArray/${idArray}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条数据记录。
 */
export function findEntityById_22(id: number, params?: { id: number }) {
  return request<RPaymentVO>(`/payment/findEntityById/${id}`, {
    method: 'get',
    params,
  });
}

/**
 * 显示数据表中的全部记录
 * @description 分页显示数据表中的全部记录
 */
export function findAll_3(pageNum: number, pageSize: number, params?: { pageNum: number; pageSize: number }) {
  return request<R>(`/payment/findAll/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一或多条记录。
 */
export function deleteEntityById_21(idArray: Array<object>, params?: { idArray: Array<object> }) {
  return request<R>(`/payment/deleteEntityByIdArray/${idArray}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一条记录。
 */
export function deleteEntityById_22(id: number, params?: { id: number }) {
  return request<R>(`/payment/deleteEntityById/${id}`, {
    method: 'get',
    params,
  });
}
