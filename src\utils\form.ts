import { ref } from 'vue';

/**
 * 密码强度验证钩子
 * @returns {object} 返回包含密码相关状态和处理方法的对象
 */
export const usePasswordStrength = () => {
  /**
   * 密码是否可见
   * @type {boolean}
   */
  const pwdVisible = ref(false);

  /**
   * 密码安全等级
   * @type {string}
   */
  const safeStrength = ref('');

  /**
   * 密码强度进度条
   * @type {number}
   */
  const pwdProgress = ref(0);

  /**
   * 输入密码时的处理方法
   * @param {string} password - 输入的密码
   */
  const onPwdInput = (password) => {
    let i = 0;
    const pattern_digit_letter = /^(?=.*\d)(?=.*[a-zA-Z]).{8,20}$/;
    const pattern_digit_special = /^(?=.*\d)(?=.*[^a-zA-Z0-9]).{8,20}$/;
    const pattern_letter_special = /^(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9]).{8,20}$/;
    const pattern_all = /^(?=.*\d)(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9]).{8,20}$/;

    // 检查密码是否符合特定模式
    if (
      pattern_digit_letter.test(password) ||
      pattern_digit_special.test(password) ||
      pattern_letter_special.test(password)
    ) {
      i = 1; // 低级安全性
    }

    // 根据密码长度和模式匹配确定安全性级别
    if (pattern_all.test(password) && password.length < 12) {
      i = 2; // 中级安全性
    } else if (pattern_all.test(password) && password.length >= 12) {
      i = 3; // 高级安全性
    }

    // 根据安全性级别设置密码强度和进度条
    if (i === 1) {
      safeStrength.value = '低';
    } else if (i === 2) {
      safeStrength.value = '中';
    } else if (i === 3) {
      safeStrength.value = '高';
    }

    pwdProgress.value = i * 33.33; // 根据安全性级别设置进度条值
  };

  /**
   * 密码输入框获得焦点时的处理方法
   */
  const onPwdFocus = () => {
    pwdVisible.value = true; // 设置密码可见
  };

  /**
   * 密码输入框失去焦点时的处理方法
   */
  const onPwdBlur = () => {
    pwdVisible.value = false; // 设置密码不可见
  };

  // 返回所需的方法和数据
  return { pwdVisible, safeStrength, pwdProgress, onPwdInput, onPwdFocus, onPwdBlur };
};
