<template>
  <el-drawer v-model="drawer" class="relative" @close="onCancel">
    <template #header>
      <div class="flex h-full justify-between pr-4">
        <h4 class="text-m">数据源详情</h4>
        <div class="flex items-center justify-start text-sm text-p">
          <!-- <div v-if="form.synchState === 1" class="flex h-full cursor-pointer items-center" @click="onSync">
            <el-icon color="#007f99"><RefreshRight /></el-icon>
            <span class="ml-1">元数据同步</span>
          </div>
          <div v-if="form.synchState === 2" class="flex h-full cursor-pointer items-center" @click="onDocument">
            <el-icon color="#007f99"><Document /></el-icon>
            <span class="ml-1">数据库文档</span>
          </div> -->
          <div
            v-loading="connectLoading"
            class="ml-3 flex h-full cursor-pointer items-center"
            @click="connectivityTest"
          >
            <el-icon color="#007f99">
              <Setting />
            </el-icon>
            <span class="mx-1">连通性检测</span>
          </div>
        </div>
      </div>
    </template>

    <div>
      <div class="mb-4 flex w-full items-center">
        <div class="h-[13px] w-[3px] bg-p" />
        <span class="ml-2">数据源信息</span>
      </div>
      <el-descriptions direction="vertical" :column="1">
        <!-- <el-descriptions-item label="数据源类型">{{ form.type }}</el-descriptions-item>
        <el-descriptions-item label="数据源名称">{{ form.name }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <span class="status" :class="metadata.statusClass[form.state]">{{ metadata.statusText[form.state] }}</span>
        </el-descriptions-item> -->
        <el-descriptions-item label="同步状态">
          <span class="status" :class="synchStateClass[+form.synchronized]">{{
            synchStateText[+form.synchronized]
          }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="数据库类型">
          {{ form.databaseType }}
        </el-descriptions-item>
        <el-descriptions-item v-if="form.note" label="备注">
          {{ form.note }}
        </el-descriptions-item>
      </el-descriptions>
      <div class="mb-4 mt-5 flex w-full items-center">
        <div class="h-[13px] w-[3px] bg-p" />
        <span class="ml-2">字段信息</span>
      </div>
      <el-descriptions direction="vertical" :column="1">
        <el-descriptions-item label="主机">
          {{ form.address }}
        </el-descriptions-item>
        <el-descriptions-item label="端口">
          {{ form.port }}
        </el-descriptions-item>
        <el-descriptions-item label="数据库">
          {{ form.databaseName }}
        </el-descriptions-item>
        <el-descriptions-item label="用户名">
          {{ form.userName }}
        </el-descriptions-item>
        <el-descriptions-item label="密码">
          {{ form.password }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
  </el-drawer>
</template>

<script setup>
  import { synchStateClass, synchStateText } from '@/utils/constants';
  import { checkConnectionDBVOByDbId } from '@/api/index';
  import { ElMessage } from 'element-plus';

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
      required: true,
    },
    data: {
      type: Object,
      required: true,
    },
  });

  //元数据同步
  const onSync = () => {};
  //数据库文档
  const onDocument = () => {};
  //连通性检测
  const connectLoading = ref(false);
  const connectivityTest = async () => {
    try {
      connectLoading.value = true;
      await checkConnectionDBVOByDbId(props.data.id);
      ElMessage({ type: 'success', message: '连接数据库成功' });
    } catch (error) {
      console.log(error);
    } finally {
      connectLoading.value = false;
    }
  };

  const drawer = ref(false);
  let form = ref({});
  watchEffect(() => {
    drawer.value = props.modelValue;
  });
  watch(
    () => props.data,
    (value) => {
      form.value = value;
    }
  );
  const emit = defineEmits(['update:modelValue']);
  const onCancel = () => {
    emit('update:modelValue', false);
  };
</script>

<style lang="scss">
  .el-descriptions {
    .el-descriptions__label {
      color: $color-tip-text;
    }
  }
</style>
