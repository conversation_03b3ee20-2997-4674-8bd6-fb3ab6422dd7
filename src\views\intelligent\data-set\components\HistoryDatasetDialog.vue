<template>
  <!-- 历史数据集弹窗 -->
  <el-dialog v-model="modelValue" title="历史数据集" width="80%" @close="modelValue = false">
    <div v-loading="loading">
      <el-table :data="tableData" style="width: 100%" class="c-table-header">
        <el-table-column prop="datasetNameCn" label="数据集名称(中文)" show-overflow-tooltip />
        <el-table-column prop="datasetName" label="数据集名称(英文)" show-overflow-tooltip />
        <el-table-column prop="name" min-width="100px" label="文件" show-overflow-tooltip />
        <el-table-column fixed="right" label="操作" width="80px">
          <template #default="{ row }">
            <div class="flex flex-wrap justify-center gap-y-2">
              <el-button link type="primary" @click="handleDownload(row)"> 下载 </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
  import { findHistoryFileByFileInforId } from '@/api';
  import { downloadFile } from '@/utils/downloadUtils';
  import { ElMessage } from 'element-plus';

  interface Props {
    currentRow: FileInfoVO;
  }
  const props = defineProps<Props>();
  const modelValue = defineModel<boolean>({ required: true });
  const tableData = ref<any[]>([]);
  const loading = ref(false);

  async function fetchData() {
    try {
      loading.value = true;
      const { data } = await findHistoryFileByFileInforId(props.currentRow!.id!);
      tableData.value = data!.dataFileList!.map((e) => {
        return {
          datasetName: data?.datasetName,
          datasetNameCn: data?.datasetNameCn,
          name: e,
        };
      });
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  const handleDownload = async (row: any) => {
    try {
      loading.value = true;
      const success = await downloadFile(
        '/resources/FileInfor/downloadHistoryFile',
        row.name,
        row.name.split('/').pop() // 备用文件名
      );
      if (success) {
        ElMessage.success('下载成功');
      }
    } finally {
      loading.value = false;
    }
  };

  fetchData();
</script>
