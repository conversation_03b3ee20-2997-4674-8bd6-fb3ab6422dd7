<template>
  <div class="flex h-full flex-col overflow-hidden">
    <div class="bg-w pl-5 text-xl font-semibold">
      <h2 class="flex h-[60px] w-fit cursor-pointer items-center" @click="router.back()">
        <el-icon class="mr-2" color="#939899">
          <ArrowLeft />
        </el-icon>
        <span>数据字段</span>
      </h2>
    </div>

    <div class="m-5 flex h-0 flex-1">
      <div class="flex w-0 flex-1 flex-col rounded-md bg-w pt-5">
        <div class="flex justify-between px-10">
          <div>
            <el-button type="primary" @click="onAdd"> 新增 </el-button>
            <el-button :disabled="checkList.length <= 0" :loading="batchLoading" @click="onBatchDel">
              批量删除
            </el-button>
          </div>
          <!-- <el-input v-model="search" placeholder="请输入关键字" style="width: 500px">
            <template #prepend>
              <el-select v-model="searchPre" style="width: 130px">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </template>
            <template #append>
              <el-button :icon="Search" @click="onSearch" />
            </template>
          </el-input> -->
        </div>

        <div class="mt-3 h-0 w-full flex-1 px-10">
          <el-table
            v-loading="tableLoading"
            height="100%"
            :data="tableData"
            style="width: 100%"
            class="c-table-header"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column type="index" width="55" label="序号" />
            <el-table-column prop="name" label="字段名称" />
            <el-table-column prop="dataType" label="字段类型" />
            <el-table-column label="是否源数据映射">
              <template #default="{ row }">
                <div>{{ row.hasFieldInDataSource ? '是' : '否' }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="描述" min-width="100px" />
            <!-- <el-table-column label="存在系统">
              <template #default="{ row }">
                <div>{{ row.hasFieldRecordInSystem ? '是' : '否' }}</div>
              </template>
            </el-table-column> -->
            <el-table-column label="操作" width="120">
              <template #default="{ row }">
                <el-button link type="primary" @click="onEdit(row)"> 编辑 </el-button>
                <el-popconfirm title="确定删除此项？" @confirm="onDel(row)">
                  <template #reference>
                    <el-button link type="primary"> 删除 </el-button>
                  </template>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- <div class="pagination-bottom">
          <el-pagination
            background
            layout="total, sizes, prev, pager, next, jumper"
            :page-size="pagination.pageSize"
            :total="tableData.length"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div> -->
      </div>
    </div>
  </div>

  <Drawer v-model="showDrawer" :data="drawerData" @success="onSuccess" />

  <el-dialog v-model="showDesc" title="编辑表" width="500px" @close="onCancelDesc">
    <el-input v-model="currentItem.description" placeholder="请输入描述" maxlength="200" />
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="onCancelDesc">取消</el-button>
        <el-button type="primary" :loading="editLoding" @click="onSaveDesc">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { findAllFieldVOByDbIdTblId, deleteFieldById, addField } from '@/api/index';
  import { Search } from '@element-plus/icons-vue';
  import Drawer from './components/Drawer.vue';
  import { useRouter } from 'vue-router';
  import { cloneDeep } from 'lodash-es';
  const router = useRouter();

  const props = defineProps({
    dbId: { type: String },
    tableId: { type: String },
  });

  //表格
  const checkList = ref([]);
  const handleSelectionChange = (val) => {
    checkList.value = val;
  };
  const batchLoading = ref(false);
  const onBatchDel = () => {
    ElMessageBox.confirm('确定删除所有选中项？', '操作提示', { type: 'warning' })
      .then(async () => {
        try {
          batchLoading.value = true;
          await Promise.all(checkList.value.map((row) => deleteFieldById(row.id)));
          ElMessage({ type: 'success', message: '删除成功' });
          fetchData();
        } catch (error) {
          console.log(error);
        } finally {
          batchLoading.value = false;
        }
      })
      .catch(() => {});
  };
  const tableData = ref([]);
  const tableLoading = ref(false);
  watch(
    () => props.tableId,
    () => {
      fetchData();
    },
    {
      immediate: true,
    }
  );
  async function fetchData() {
    try {
      tableLoading.value = true;
      const { data } = await findAllFieldVOByDbIdTblId(props.dbId, props.tableId);
      tableData.value = data[0];
    } catch (error) {
      console.log(error);
    } finally {
      tableLoading.value = false;
    }
  }
  const onDel = async (row) => {
    try {
      await deleteFieldById(row.id);
      fetchData();
    } catch (error) {
      console.log(error);
    }
  };

  const showDrawer = ref(false);
  const onAdd = () => {
    drawerData.value = {
      dataBaseId: props.dbId,
      tableId: props.tableId,
    };
    showDrawer.value = true;
  };
  let drawerData = ref({});
  function onEdit(row) {
    currentItem.value = cloneDeep(row);
    showDesc.value = true;
  }
  const onSuccess = () => {
    fetchData();
  };

  //页码
  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  const handleCurrentChange = (e) => {
    pagination.page = e;
  };
  const handleSizeChange = (e) => {
    pagination.pageSize = e;
  };

  //编辑
  const showDesc = ref(false);
  const editLoding = ref(false);
  const currentItem = ref({});
  const onCancelDesc = () => {
    showDesc.value = false;
  };
  const onSaveDesc = async () => {
    try {
      editLoding.value = true;
      await addField(currentItem.value);
      ElMessage({ type: 'success', message: '保存成功' });
      onCancelDesc();
      fetchData();
    } catch (error) {
      console.log(error);
    } finally {
      editLoding.value = false;
    }
  };
</script>
