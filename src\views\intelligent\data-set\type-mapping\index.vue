<template>
  <div class="flex h-full flex-col overflow-hidden">
    <h2 class="bg-w flex h-[60px] items-center pl-5 text-xl font-semibold">数据集类型映射管理</h2>

    <!-- 主体内容区 -->
    <div v-loading="loading" class="bg-w m-5 flex h-0 flex-1 flex-col rounded-md pt-5">
      <!-- 操作栏 -->
      <div class="flex gap-4 px-10">
        <el-button type="primary" :disabled="!multipleSelection.length" @click="batchDialogVisible = true">
          批量修改
        </el-button>
        <el-select
          v-model="selectedType"
          placeholder="请选择疾病类型"
          style="width: 200px"
          clearable
          @change="fetchData(1)"
        >
          <el-option v-for="item in illnesses" :key="item.id" :label="item.title" :value="item.id" />
        </el-select>
        <el-input
          v-model="search"
          placeholder="请输入关键字搜索"
          style="width: 300px"
          clearable
          @clear="fetchData(1)"
          @keyup.enter="fetchData(1)"
        >
          <template #append>
            <el-button :icon="Search" @click="fetchData(1)" />
          </template>
        </el-input>
      </div>

      <!-- 表格区域 -->
      <div class="mt-3 h-0 w-full flex-1 px-10">
        <el-table
          height="100%"
          :data="tableData"
          style="width: 100%"
          class="c-table-header"
          row-key="id"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" reserve-selection />
          <el-table-column prop="datasetNameCn" min-width="100px" label="数据集名称(中文)" show-overflow-tooltip />
          <el-table-column prop="datasetName" min-width="100px" label="数据集名称(英文)" show-overflow-tooltip />
          <el-table-column prop="projectCode" label="课题编码缩写" />
          <el-table-column prop="createDate" label="更新日期" width="170px" />
          <el-table-column prop="affiliatedUnit" label="所属单位" />
          <el-table-column prop="state" label="状态" />
          <el-table-column prop="diseaseTypeAnnotation" label="疾病类型">
            <template #default="{ row }">
              <el-select
                v-model="row.diseaseTypeAnnotation"
                placeholder="请选择"
                style="width: 120px"
                @change="(val) => handleDiseaseTypeChange(row, val)"
              >
                <el-option v-for="item in illnesses" :key="item.id" :label="item.title" :value="item.id" />
              </el-select>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-bottom">
        <el-pagination
          background
          layout="total, prev, pager, next, jumper"
          :page-size="pagination.pageSize"
          :total="total"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 批量修改弹窗 -->
    <el-dialog v-model="batchDialogVisible" title="批量修改归属疾病类型" width="400px">
      <el-select v-model="batchDiseaseType" placeholder="请选择疾病类型" style="width: 100%">
        <el-option v-for="item in illnesses" :key="item.id" :label="item.title" :value="item.id" />
      </el-select>
      <template #footer>
        <el-button @click="batchDialogVisible = false">取消</el-button>
        <el-button type="primary" :disabled="!batchDiseaseType" @click="handleBatchUpdate">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<!--
 * 数据集类型映射管理
 * 说明: 用于维护数据集与疾病类型之间的关联关系
 -->
<script setup lang="ts">
  import { findByBictionaryCode, findFileInforByAnnotationId, setFileInforAnnotation_02 } from '@/api';
  import { ElMessage } from 'element-plus';
  import { Search } from '@element-plus/icons-vue';

  const loading = ref(false);
  // 疾病类型
  const illnesses = ref<DictionaryValueStatisticDTO[]>([]);
  const selectedType = ref('');
  const search = ref('');
  async function fetchTypes() {
    try {
      loading.value = true;
      const { data } = await findByBictionaryCode(1, 999, { dictionaryCode: 'TYPE_DISEASE' } as any);
      illnesses.value = data?.content || [];
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  // 表格数据
  const tableData = ref<FileInfoVO[]>([]);
  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  const total = ref(0);
  const fetchData = async (pageNum = 1) => {
    try {
      loading.value = true;
      const { data } = await findFileInforByAnnotationId({
        annotationIDList: selectedType.value ? [Number(selectedType.value)] : [],
        pageNum: pageNum,
        pageSize: pagination.pageSize,
        otherFilter: search.value.trim(),
      });
      total.value = data!.totalElement!;
      tableData.value = data?.content || [];
    } catch (error) {
      console.error(error);
    } finally {
      loading.value = false;
    }
  };
  const handleCurrentChange = (page: number) => {
    pagination.page = page;
    fetchData(page);
  };

  const handleDiseaseTypeChange = async (row: FileInfoVO, newType: string) => {
    try {
      loading.value = true;
      await setFileInforAnnotation_02(Number(newType), [row.id!]);
      ElMessage.success('更新成功');
      fetchData(pagination.page);
    } catch (e) {
      ElMessage.error('更新失败');
    } finally {
      loading.value = false;
    }
  };

  const multipleSelection = ref<FileInfoVO[]>([]);
  const batchDialogVisible = ref(false);
  const batchDiseaseType = ref('');

  const handleSelectionChange = (val: FileInfoVO[]) => {
    multipleSelection.value = val;
  };

  const handleBatchUpdate = async () => {
    if (!batchDiseaseType.value) return;
    try {
      loading.value = true;
      await setFileInforAnnotation_02(
        Number(batchDiseaseType.value),
        multipleSelection.value.map((item) => item.id!)
      );
      ElMessage.success('批量更新成功');
      batchDialogVisible.value = false;
      fetchData(pagination.page);
    } catch (e) {
      ElMessage.error('批量更新失败');
    } finally {
      loading.value = false;
    }
  };

  onBeforeMount(() => {
    fetchTypes();
    fetchData();
  });
</script>
