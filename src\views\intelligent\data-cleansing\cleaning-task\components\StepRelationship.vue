<template>
  <div class="flex h-full flex-col">
    <el-scrollbar class="h-0 flex-1 px-10">
      <div class="py-5">
        <div class="mb-5 flex">
          <el-button type="primary" @click="onAdd"> 添加 </el-button>
          <el-button @click="onName"> 名称映射 </el-button>
          <el-button @click="onOrder"> 顺序映射 </el-button>
          <el-button @click="onHide"> 隐藏已映射 </el-button>
          <el-button @click="onCancel"> 取消映射 </el-button>
        </div>

        <div ref="container" class="container relative flex justify-between">
          <div class="w-[40%] rounded bg-w px-10 py-7">
            <h3 class="text-lg">源端表：{{ sourceTableTitle }}</h3>
            <el-table
              :data="sourceTable"
              style="width: 100%"
              class="c-table-header mt-6"
              :row-class-name="({ row }) => `left-row id-${row.id}`"
              :cell-style="{ background: 'revert' }"
            >
              <el-table-column prop="fieldName" label="源端字段名" align="center" />
              <el-table-column prop="fieldType" label="字段类型" align="center" />
            </el-table>
          </div>

          <div class="w-[40%] rounded bg-w px-10 py-7">
            <h3 class="text-lg">目标端表：{{ targetTableTitle }}</h3>
            <el-table
              :data="targetTable"
              style="width: 100%"
              class="c-table-header mt-6"
              :row-class-name="({ row }) => `right-row id-${row.id}`"
              :cell-style="{ background: 'revert' }"
            >
              <el-table-column prop="fieldName" label="目标端字段名" />
              <el-table-column prop="fieldType" label="字段类型" />
            </el-table>
          </div>
        </div>
      </div>
    </el-scrollbar>

    <div class="box-shadow flex h-[68px] items-center justify-center bg-w">
      <el-button type="primary" @click="onSave"> 下一步 </el-button>
      <el-button @click="lastStep"> 上一步 </el-button>
    </div>
  </div>
</template>

<script setup>
  let instance = null;
  let allConnect = [];
  let relationship = [
    { sourceId: 10, targetId: 20 },
    { sourceId: 11, targetId: 23 },
  ];
  const onAdd = () => {};
  const onName = () => {
    clearConnect();
    setConnect(relationship);
  };
  const onOrder = () => {
    clearConnect();
    let temp = [
      { sourceId: 10, targetId: 20 },
      { sourceId: 11, targetId: 21 },
      { sourceId: 12, targetId: 22 },
    ];
    setConnect(temp);
  };
  const onHide = () => {
    clearConnect();
  };
  const onCancel = () => {
    clearConnect();
  };

  const sourceTableTitle = ref('DJ_JGFNN');
  const targetTableTitle = ref('T_TY_DJ_JGFNN');
  const sourceTable = ref([]);
  for (let i = 0; i < 20; i++) {
    sourceTable.value.push({
      id: `1${i}`,
      fieldName: `源字段${i}`,
      fieldType: 'String',
    });
  }
  const targetTable = ref([]);
  for (let i = 0; i < 20; i++) {
    targetTable.value.push({
      id: `2${i}`,
      fieldName: `目标字段${i}`,
      fieldType: 'Number',
    });
  }

  const emit = defineEmits(['success']);
  const onSave = () => {
    emit('success', 1);
  };
  const lastStep = () => {
    emit('success', -1);
  };

  import { newInstance, ready } from '@jsplumb/browser-ui';
  const container = ref();
  function init() {
    instance = newInstance({
      container: container.value, //目标容器id
      paintStyle: { strokeWidth: 2, stroke: '#007f99' }, //连接线样式
      // 鼠标滑过线的样式
      // hoverPaintStyle: { stroke: 'skyblue', strokeWidth: 2, cursor: 'pointer' },
      endpoint: { type: 'Dot', options: { radius: 10 } },
      endpointStyle: { stroke: '#cce5ea', strokeWidth: 6, fill: '#007f99' }, //端点样式
      anchors: ['Right', 'Left'],
      connectionOverlays: [{ type: 'PlainArrow', options: { width: 10, location: 1 } }],
    });
  }
  const initJsPlumb = () => {
    ready(() => {
      init();
    });
  };
  onMounted(() => {
    initJsPlumb();
    onOrder();
  });
  // 设置连线
  function setConnect(relationship) {
    relationship.forEach(function (data) {
      // source是连线起点元素id target是连线终点元素id
      let conn = instance.connect({
        source: document.querySelector(`.id-${data.sourceId}`),
        target: document.querySelector(`.id-${data.targetId}`),
      });
      allConnect.push(conn);
    });
  }
  function clearConnect() {
    allConnect.forEach((conn) => {
      instance.deleteConnection(conn);
    });
    allConnect = [];
  }
</script>
