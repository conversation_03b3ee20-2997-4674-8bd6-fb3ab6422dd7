/*
 * @OriginalName: 数据资源系统中发票管理模块
 * @Description: 发票的创建、更新和查询
 */
import { request } from '@/utils/request';

/**
 * 新建或更新发票
 * @description 按productDTO的信息，新建或更新发票的信息。
 */
export function newOrUpdateOrg_1(data: InvoiceDTO) {
  return request<RInvoiceVO>(`/invoice/newOrUpdateProduct`, {
    method: 'post',
    data,
  });
}

/**
 * 查找发票
 * @description 按动态条件，获取满足相应条件的发票的基本信息。各条件按与操作进行模糊查询。所有条件均为空时，返回全部记录。
 */
export function findInvoiceByCriteria(data: InvoiceCriteria) {
  return request<REntityVOPage>(`/invoice/findInvoiceByCriteria`, {
    method: 'post',
    data,
  });
}

/**
 * 查找发票
 * @description 按发票的Id，精确查找数据记录。
 */
export function findAllInvoiceById(processId) {
  return request<RListInvoiceVO>(`/invoice/findAllInvoiceById/${processId}`, {
    method: 'get',
  });
}

/**
 * 分页显示全部发票
 * @description 分页显示全部发票
 */
export function findAllInvoice(pageNum: number, pageSize: number, params?: { pageNum: number; pageSize: number }) {
  return request<REntityVOPage>(`/invoice/findAllInvoice/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除发票
 * @description 按 Product的Id，删除一个或多个发票的记录。
 */
export function deleteProductById(productId) {
  return request<R>(`/invoice/deleteProductById/${productId}`, {
    method: 'get',
  });
}
