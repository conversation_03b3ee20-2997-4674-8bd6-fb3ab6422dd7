<template>
  <div class="flex justify-center pt-10">
    <News1 v-if="id === '1'" />
    <News2 v-if="id === '2'" />
    <News3 v-if="id === '3'" />
    <News4 v-if="id === '4'" />
    <News5 v-if="id === '5'" />
  </div>
</template>

<script setup lang="ts">
  interface Props {
    id: string;
  }
  const props = defineProps<Props>();
  import News1 from './components/News1.vue';
  import News2 from './components/News2.vue';
  import News3 from './components/News3.vue';
  import News4 from './components/News4.vue';
  import News5 from './components/News5.vue';
</script>
