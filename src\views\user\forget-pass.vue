<template>
  <div class="page">
    <h2 class="title">忘记密码</h2>

    <div class="box">
      <div class="box-inner">
        <el-form ref="formRef" size="large" :model="form" class="form" :rules="rules">
          <el-form-item prop="username">
            <el-input v-model="form.username" placeholder="请输入账号" maxlength="30" />
          </el-form-item>
          <el-form-item prop="email">
            <el-input v-model="form.email" placeholder="请输入邮箱" maxlength="50" />
          </el-form-item>
          <el-form-item prop="emailCode">
            <div class="code-container">
              <el-input v-model="form.emailCode" class="code-input" placeholder="请输入邮箱验证码" maxlength="10" />
              <el-button plain @click="getCode"> 获取验证码 </el-button>
            </div>
          </el-form-item>
          <el-form-item prop="password">
            <el-tooltip effect="light" placement="right-start" :visible="pwdVisible">
              <template #content>
                <div class="password-tip">
                  <div>安全程度：{{ safeStrength }}</div>
                  <el-progress :stroke-width="8" :percentage="pwdProgress" :show-text="false" />
                  <ul class="password-ul">
                    <li>最少8个字符</li>
                    <li>不能全为数字、字母或特殊符号</li>
                    <li>数字、字母、特殊字符任意组合</li>
                  </ul>
                </div>
              </template>
              <el-input
                v-model="form.password"
                placeholder="请输入新密码"
                show-password
                maxlength="20"
                @input="onPwdInput"
                @focus="onPwdFocus"
                @blur="onPwdBlur"
              />
            </el-tooltip>
          </el-form-item>
          <el-form-item prop="confirmPassword">
            <el-input v-model="form.confirmPassword" placeholder="请确认新密码" show-password max-length="20" />
          </el-form-item>
          <el-form-item>
            <el-button class="btn" color="#007f99" @click="onSubmit"> 确认 </el-button>
          </el-form-item>
        </el-form>
        <div class="link" @click="gologin">
          <a>返回登录</a>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { validatePassword } from '@/utils/validator';
  import { ElMessage } from 'element-plus';
  import { useRouter } from 'vue-router';
  const router = useRouter();
  import { usePasswordStrength } from '@/utils/form';
  const { pwdVisible, safeStrength, pwdProgress, onPwdInput, onPwdFocus, onPwdBlur } = usePasswordStrength();

  let form = reactive({
    username: '',
    email: '',
    emailCode: '',
    password: '',
    confirmPassword: '',
  });

  const getCode = () => {
    console.log('获取验证码');
  };

  let gologin = () => {
    router.replace({ name: 'Login' });
  };

  const rules = {
    username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
    email: [
      { required: true, message: '请输入邮箱', trigger: 'blur' },
      {
        type: 'email',
        message: '请输入有效的邮箱地址',
        trigger: ['blur', 'change'],
      },
    ],
    emailCode: [{ required: true, message: '请输入邮箱验证码', trigger: 'blur' }],
    password: [
      { required: true, message: '请输入新密码', trigger: 'blur' },
      {
        validator: validatePassword,
        trigger: 'blur',
      },
    ],
    confirmPassword: [{ required: true, message: '请输入确认密码', trigger: 'blur' }],
  };

  let formRef = ref('');
  function onSubmit() {
    formRef.value.validate((valid) => {
      if (valid) {
        ElMessage.success('修改成功');
        gologin();
      }
    });
  }
</script>

<style scoped lang="scss">
  .page {
    padding-top: 100px;
    padding-bottom: 20px;
    font-size: 14px;

    .title {
      text-align: center;
      font-size: 30px;
      font-weight: 700;
      line-height: 43px;
    }
  }

  .box {
    width: 1200px;
    border-radius: 8px;
    background: #ffffff;
    margin: 0 auto;
    margin-top: 55px;
    padding-top: 60px;
    padding-bottom: 34px;
    display: flex;
    flex-direction: column;
    align-items: center;

    .box-inner {
      width: 480px;
      text-align: center;
    }

    .form {
      .el-form-item {
        margin-bottom: 28px;
      }
    }

    .btn {
      margin-top: 20px;
      width: 100%;
    }

    .link {
      margin-top: 19px;
      color: $color-primary;
    }

    .code-container {
      width: 100%;
      display: flex;
      justify-content: space-between;

      .code-input {
        width: 362px;
      }
    }
  }

  .password-tip {
    color: $color-tip-text;
    font-size: 12px;

    .password-ul {
      margin-top: 8px;
      padding-left: 20px;
    }
  }
</style>
