export function validatePassword(_rule, value, callback) {
  // 密码长度至少为8位
  if (value.length < 8) {
    callback(new Error('密码长度最少8位'));
  } else if (value.length > 20) {
    callback(new Error('密码长度最多20位'));
  } else {
    // 使用正则表达式进行密码复杂度校验
    const hasNumber = /\d/.test(value); // 包含数字
    const hasLetter = /[a-zA-Z]/.test(value); // 包含字母
    const hasSpecialChar = /[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(value); // 包含特殊字符
    // 判断密码是否符合要求
    if ((hasNumber && hasLetter) || (hasNumber && hasSpecialChar) || (hasLetter && hasSpecialChar)) {
      callback();
    } else {
      callback(new Error('密码需包含数字、字母、特殊字符的组合'));
    }
  }
}

export function validateUsername(_rule, value, callback) {
  const reg = /^(?=.*[a-zA-Z])[a-zA-Z0-9_]{4,16}$/;
  if (!reg.test(value)) {
    callback(new Error('可使用英文、数字、下划线组合，不能只有数字或下划线，必须包含英文'));
  } else {
    callback();
  }
}

export function validateIDCard(_rule, value, callback) {
  const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
  if (!reg.test(value)) {
    callback(new Error('请输入合法的中国大陆身份证号码'));
  } else {
    callback();
  }
}

export function validatePhoneNumber(_rule, value, callback) {
  // 支持手机号：1[3-9]\d{9} 或 固话：0\d{2,3}-\d{7,8} 或 0\d{2,3}\d{7,8}
  const reg = /^(1[3-9]\d{9}|0\d{2,3}-?\d{7,8})$/;
  if (!reg.test(value)) {
    callback(new Error('请输入合法的中国大陆手机号或固定电话号码'));
  } else {
    callback();
  }
}

export function validateEmail(_rule, value, callback) {
  if (!value) {
    callback();
    return;
  }
  const reg = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  if (!reg.test(value)) {
    callback(new Error('邮箱格式不正确'));
  } else {
    callback();
  }
}
