<template>
  <div class="flex h-full">
    <div class="aside bg-w">
      <asidemenu />
    </div>

    <div class="w-0 flex-1">
      <el-scrollbar height="100%">
        <router-view />
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup>
  import asidemenu from '@/components/aside.vue';
  import { useRoute } from 'vue-router';
  let route = useRoute();

  let activeId = ref('0');
  let menuList = ref([
    {
      title: '目录注册',
      id: '1',
      pathname: 'IntelligentRegister',
      svgname: 'icon-shenqing',
    },
    {
      title: '目录挂接',
      id: '2',
      pathname: 'IntelligentConnect',
      svgname: 'icon-ziyuan35',
    },
    // {
    //   title: '目录审核',
    //   id: '3',
    //   pathname: 'IntelligentAudit',
    //   svgname: 'icon-a-shenpi2',
    // },
    // {
    //   title: '目录发布',
    //   id: '4',
    //   pathname: 'IntelligentIssue',
    //   pathname: '',
    //   svgname: 'icon-fabu',
    // },
  ]);

  watch(
    () => route.name,
    (val, old) => {
      menuList.value.forEach((e) => {
        if (e.pathname == val) {
          activeId.value = e.id;
        }
      });
    },
    {
      immediate: true,
    }
  );
  provide('datalist', menuList);
  provide('openid', activeId);
</script>

<style scoped lang="scss">
  .aside {
    width: 256px;
    height: 100%;
    box-shadow: 2px 0 12px 0 rgb(0 0 0 / 10%);
    z-index: 10;
  }

  .main-header {
    padding-top: 15px;
    padding-left: 28px;
    background: #fff;
  }
</style>
