<template>
  <div class="flex h-full flex-col">
    <el-scrollbar height="h-0 flex-1">
      <ul v-loading="fieldLoading" class="p-5">
        <!-- <el-checkbox-group v-model="checkList"> -->
        <li class="demand-item">
          <div class="demand-row">
            <div>新增数据字段</div>
            <!-- <el-checkbox :disabled="['审核通过'].includes(state)" value="新增数据字段"> 新增数据字段 </el-checkbox> -->
            <el-button type="primary" :loading="orderLoading" @click="onAddOrder"> 创建数据订单 </el-button>
          </div>
          <div class="demand-content">
            <DataField :order="orderTable" @delete="fetchFieldOrder" />
          </div>
        </li>

        <!-- <li class="demand-item">
            <div class="demand-row">
              <el-checkbox :disabled="['审核通过'].includes(state)" label="扩大研究范围"> 扩大研究范围 </el-checkbox>
            </div>
            <div v-show="checkList.includes('扩大研究范围')" class="demand-content">
              <el-form ref="RangRef" :model="rangForm" :rules="rangRules" label-position="top">
                <el-form-item label="当前范围：">
                  <el-input v-model="rangForm.currentRang" type="textarea" :rows="4" disabled />
                </el-form-item>
                <el-form-item label="新的范围：" prop="newRang">
                  <el-input
                    v-model="rangForm.newRang"
                    type="textarea"
                    :rows="4"
                    maxlength="300"
                    show-word-limit
                    placeholder="请输入新的范围"
                  />
                </el-form-item>
                <el-form-item label="理由：" prop="reason">
                  <el-input
                    v-model="rangForm.reason"
                    type="textarea"
                    :rows="4"
                    maxlength="300"
                    show-word-limit
                    placeholder="请输入理由"
                  />
                </el-form-item>
              </el-form>
            </div>
          </li> -->

        <li class="demand-item">
          <div class="demand-row">
            <div>延长研究时限</div>
            <!-- <el-checkbox :disabled="['审核通过'].includes(state)" value="延长研究时限"> 延长研究时限 </el-checkbox> -->
          </div>
          <div class="demand-content">
            <el-form ref="limitRef" :model="limitForm" :rules="limitRules" label-position="top">
              <div class="flex">
                <el-form-item label="当前时限：">
                  <el-input v-model="limitForm.currentValue" disabled style="width: 200px" />
                  <span class="ml-2">个月</span>
                </el-form-item>
                <!-- <el-form-item label="预计截止日期：" class="ml-[60px]">
                  <el-input v-model="currentDate" disabled style="width: 200px" />
                </el-form-item> -->
              </div>
              <div class="flex">
                <el-form-item label="延长时限：">
                  <el-input-number
                    v-model="limitForm.newValue"
                    :min="1"
                    :max="999"
                    step-strictly
                    style="width: 200px"
                  />
                  <span class="ml-2">个月</span>
                </el-form-item>
                <!-- <el-form-item label="预计截止日期：" class="ml-[60px]">
                  <el-input v-model="newDate" disabled style="width: 200px" />
                </el-form-item> -->
              </div>
              <!-- <el-form-item label="理由：" prop="reason">
                  <el-input
                    v-model="limitForm.reason"
                    type="textarea"
                    :rows="4"
                    maxlength="300"
                    show-word-limit
                    placeholder="请输入理由"
                  />
                </el-form-item> -->

              <el-form-item>
                <el-button :loading="saveLoading" type="primary" @click="onChangeDate">确定</el-button>
              </el-form-item>
            </el-form>
          </div>
        </li>
        <!-- </el-checkbox-group> -->
      </ul>
    </el-scrollbar>

    <!-- <div  class="footer">
      <el-button class="w-[88px]" type="primary" @click="onSubmit"> 提交 </el-button>
      <el-button class="w-[88px]" @click="onSaveTemp"> 保存草稿 </el-button>
    </div> -->
  </div>
</template>

<script setup lang="ts">
  /* 更新需求 */
  import { newOrUpdateOrder, findOrderByApplicationId, findAppById, newOrUpdateApplication } from '@/api/index';
  import dayjs from 'dayjs';
  import DataField from './DataField.vue';
  import { useUsers } from '@/store/user-info.js';
  import { ElMessage } from 'element-plus';

  export type Order = OrderVO & { tableData: any[] };

  const store = useUsers();
  const props = defineProps({
    state: {
      type: String,
      default: '',
    },
    id: String,
  });
  const checkList = ref<string[]>([]);
  const orderTable = ref<OrderVO[]>([]);
  const orderLoading = ref(false);
  const fieldLoading = ref(false);

  async function fetchFieldOrder() {
    try {
      fieldLoading.value = true;
      const { data } = await findOrderByApplicationId(+props.id!, 1, 100);
      orderTable.value = data?.content || [];
    } catch (error) {
      console.log('🚀 ~ fetchFieldOrder ~ error:', error);
    } finally {
      fieldLoading.value = false;
    }
  }

  //创建数据订单
  const onAddOrder = async () => {
    try {
      orderLoading.value = true;
      await newOrUpdateOrder({ useId: store.user.id, applicationId: +props.id! });
      fetchFieldOrder();
    } catch (error) {
      console.log(error);
    } finally {
      orderLoading.value = false;
    }
  };

  // watch(checkList, (value) => {
  //   if (value.includes('新增数据字段')) {
  //     fetchFieldOrder();
  //   }
  // });

  const RangRef = ref();
  const rangForm = reactive({
    currentRang: '',
    newRang: '',
    reason: '',
  });
  const rangRules = reactive({
    newRang: [{ required: true, message: '请输入新的范围', trigger: 'blur' }],
    reason: [{ required: true, message: '请输入理由', trigger: 'blur' }],
  });

  const limitRef = ref();
  const limitForm = reactive({
    currentValue: 0,
    newValue: 1,
    // reason: '',
  });
  const limitRules = reactive({
    newValue: [{ required: true, message: '请输入延长时限', trigger: 'blur' }],
    // reason: [{ required: true, message: '请输入理由', trigger: 'blur' }],
  });
  const saveLoading = ref(false);
  const appData = ref<ApplicationVO | null>(null);

  async function fetchDate() {
    try {
      fieldLoading.value = true;
      const { data } = await findAppById(+props.id!);
      appData.value = data || null;
      limitForm.currentValue = data?.duration || 0;
    } catch (error) {
      console.log(error);
    } finally {
      fieldLoading.value = false;
    }
  }

  const onChangeDate = () => {
    limitRef.value.validate(async (valid) => {
      if (valid) {
        try {
          saveLoading.value = true;
          appData.value!.duration = limitForm.currentValue + limitForm.newValue;
          await newOrUpdateApplication(appData.value!);
          fetchDate();
          ElMessage({
            type: 'success',
            message: '修改成功',
          });
        } catch (error) {
          console.log(error);
        } finally {
          saveLoading.value = false;
        }
      }
    });
  };

  //提交
  const onSubmit = () => {};
  //保存草稿
  const onSaveTemp = () => {};

  onBeforeMount(() => {
    fetchFieldOrder();
    fetchDate();
  });
</script>

<style lang="scss" scoped>
  .demand-item {
    border-radius: 4px;
    background: #fff;
    margin-bottom: 20px;

    .demand-row {
      padding-left: 28px;
      padding-right: 40px;
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .demand-content {
      border-top: 1px solid #e1e3e6;
      padding: 20px 40px;
    }
  }

  .footer {
    display: flex;
    justify-content: center;
    background: #fff;
    padding: 16px 0;
    box-shadow: var(--el-box-shadow-light);
    z-index: 1;
  }
</style>
