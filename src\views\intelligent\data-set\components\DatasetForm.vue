<template>
  <div>
    <!-- 导入元数据部分 -->
    <div v-if="showMetadata" v-loading="uploadLoading" class="text-tip mb-3 flex items-center">
      <el-upload
        :show-file-list="false"
        accept=".xls,.xlsx"
        :limit="1"
        :auto-upload="false"
        :on-change="handleFileChange"
      >
        <el-button type="primary">导入元数据</el-button>
      </el-upload>
    </div>

    <!-- 表单部分 -->
    <el-form v-else ref="formRef" :model="addForm" :rules="rules" label-width="140px">
      <!-- 基本信息 -->
      <el-form-item v-for="field in basicFields" :key="field.prop" :label="field.label" :prop="field.prop">
        <el-input v-model="addForm[field.prop]" :placeholder="field.placeholder" />
      </el-form-item>

      <!-- 所属单位 -->
      <el-form-item prop="affiliatedUnit">
        <template #label>
          <el-tooltip
            content="您可以直接输入文字来填写所属单位，或者联系系统管理员新增单位"
            placement="top"
            effect="dark"
          >
            <span class="flex items-center">
              <el-icon class="mr-1"><QuestionFilled /></el-icon>
              所属单位
            </span>
          </el-tooltip>
        </template>
        <el-select
          v-model="addForm.affiliatedUnit"
          filterable
          allow-create
          default-first-option
          placeholder="请选择所属单位"
        >
          <el-option v-for="item in unitOptions" :key="item.id" :label="item.title" :value="item.title!" />
        </el-select>
      </el-form-item>

      <!-- 日期选择器 -->
      <el-form-item label="更新日期" prop="createDate">
        <el-date-picker v-model="addForm.createDate" type="date" placeholder="选择更新日期" />
      </el-form-item>

      <!-- 数据类型选择 -->
      <template v-for="field in dataTypeFields" :key="field.selectProp">
        <el-form-item :label="field.label" :prop="field.selectProp">
          <el-select v-model="addForm[field.selectProp]" :placeholder="field.selectPlaceholder">
            <el-option v-for="(item, index) in boolOptions" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="addForm[field.selectProp] === '是'" :label="field.inputLabel" :prop="field.inputProp">
          <el-input v-model="addForm[field.inputProp]" :placeholder="field.inputPlaceholder" />
        </el-form-item>
      </template>

      <!-- 其他数据类型 -->
      <el-form-item label="其他数据类型" prop="otherDataTypes">
        <el-input v-model="addForm.otherDataTypes" placeholder="其他数据类型" />
      </el-form-item>

      <!-- 数据库选择 -->
      <el-form-item v-if="showDatabase && !addForm.id" label="目标数据库" prop="databaseId">
        <el-select v-model="addForm.databaseId" placeholder="请选择要导入到哪个数据库">
          <el-option
            v-for="item in databaseList"
            :key="item.id"
            :value="item.id!"
            :label="`${item.databaseName}(${item.address})`"
          />
        </el-select>
      </el-form-item>

      <!-- 存放目录 -->
      <!-- <el-form-item prop="parentId">
        <template #label>
          <el-tooltip content="数据存储于平台指定的文件夹位置" placement="top" effect="dark">
            <span class="flex items-center">
              <el-icon class="mr-1"><QuestionFilled /></el-icon>
              存放目录
            </span>
          </el-tooltip>
        </template>
        <el-select v-model="addForm.parentId" placeholder="请选择数据集存放目录">
          <el-option v-for="item in parentIds" :key="item.id" :label="item.title" :value="item.id!" />
        </el-select>
      </el-form-item> -->
    </el-form>
  </div>
</template>

<script setup lang="ts">
  import { findAllDb, findByBictionaryCode, getTopDirectory } from '@/api';
  import { upload } from '@/utils/request';
  import { ElMessage, FormInstance, UploadFile } from 'element-plus';

  // 类型定义
  type FileInfoForm = FileInfoDTO & {
    databaseId?: number;
    id?: number | string;
  };

  interface Props {
    showDatabase?: boolean;
    showMetadata: boolean;
  }

  // 基础配置
  const props = defineProps<Props>();
  const emit = defineEmits<{ changeShowMeatadata: [value: boolean] }>();

  // 表单相关
  const formRef = ref<FormInstance>();
  const addForm = reactive<FileInfoForm>({
    id: '',
    description: '',
    datasetName: '',
    datasetNameCn: '',
    diseaseType: '',
    // parentId: '' as any,
    projectName: '',
    contactPhone: '',
    dataManager: '',
    projectCode: '',
    projectLeader: '',
    affiliatedUnit: '',
    uploader: '',
    officeEmail: '',
    otherDataTypes: '',
    biologicalSampleType: '',
    dataModalityType: '',
    dataSignalType: '',
    hasBiologicalSample: '是',
    hasEEGData: '是',
    hasImagingData: '是',
    databaseId: undefined,
    createDate: '',
    affiliatedProjectUnit: '',
  });

  // 基础字段配置
  const basicFields = [
    { label: '数据集名称(中文)', prop: 'datasetNameCn', placeholder: '请输入数据集中文名称' },
    { label: '数据集名称(英文)', prop: 'datasetName', placeholder: '请输入数据集英文名称' },
    { label: '数据集说明', prop: 'description', placeholder: '请输入数据集说明' },
    { label: '课题编码缩写', prop: 'projectCode', placeholder: '请输入课题编码缩写' },
    { label: '初始疾病类型', prop: 'diseaseType', placeholder: '请输入初始疾病类型' },
    { label: '数据负责人', prop: 'dataManager', placeholder: '请输入数据负责人' },
    { label: '上传人', prop: 'uploader', placeholder: '请输入上传人' },
    { label: '联系电话', prop: 'contactPhone', placeholder: '请输入联系电话' },
    { label: '办公邮箱', prop: 'officeEmail', placeholder: '请输入办公邮箱' },
    { label: '项目名称', prop: 'projectName', placeholder: '请输入项目名称' },
    { label: '项目负责人', prop: 'projectLeader', placeholder: '请输入所属项目负责人' },
    { label: '所属项目单位', prop: 'affiliatedProjectUnit', placeholder: '请输入所属项目单位' },
  ];

  // 数据类型字段配置
  const dataTypeFields = [
    {
      label: '是否有生物样本',
      selectProp: 'hasBiologicalSample',
      selectPlaceholder: '请选择是否有生物样本',
      inputLabel: '生物样本类型',
      inputProp: 'biologicalSampleType',
      inputPlaceholder: '生物样本类型',
    },
    {
      label: '是否有影像数据',
      selectProp: 'hasImagingData',
      selectPlaceholder: '请选择是否有影像数据',
      inputLabel: '数据模态类型',
      inputProp: 'dataModalityType',
      inputPlaceholder: '数据模态类型',
    },
    {
      label: '是否有脑电数据',
      selectProp: 'hasEEGData',
      selectPlaceholder: '请选择是否有脑电数据',
      inputLabel: '数据信号类型',
      inputProp: 'dataSignalType',
      inputPlaceholder: '数据信号类型',
    },
  ];

  // 验证规则
  const rules = ref({
    title: [{ required: true, message: '不能为空' }],
    description: [{ required: true, message: '不能为空' }],
    createDate: [{ required: true, message: '不能为空' }],
    datasetName: [{ required: true, message: '不能为空' }],
    projectCode: [{ required: true, message: '不能为空' }],
    datasetNameCn: [{ required: true, message: '不能为空' }],
    diseaseType: [{ required: true, message: '不能为空' }],
    // parentId: [{ required: true, message: '不能为空' }],
    databaseId: [{ required: true, message: '不能为空' }],
    uploader: [{ required: true, message: '不能为空' }],
    dataManager: [{ required: true, message: '不能为空' }],
    hasBiologicalSample: [{ required: true, message: '不能为空' }],
    hasEEGData: [{ required: true, message: '不能为空' }],
    hasImagingData: [{ required: true, message: '不能为空' }],
    biologicalSampleType: [{ required: true, message: '不能为空' }],
    dataModalityType: [{ required: true, message: '不能为空' }],
    dataSignalType: [{ required: true, message: '不能为空' }],
    affiliatedUnit: [{ required: true, message: '不能为空' }],
  });

  // 状态管理
  // const parentIds = ref<DirectoryVO[]>([]);
  const databaseList = ref<CBDDefDatabaseVO[]>([]);
  const unitOptions = ref<DictionaryVO[]>([]);
  const uploadLoading = ref(false);
  const boolOptions = ref([
    { label: '是', value: '是' },
    { label: '否', value: '否' },
  ]);

  // 文件上传相关
  const handleFileChange = (file: UploadFile) => {
    if (file) {
      uploadFile(file.raw!);
    }
  };

  const uploadFile = async (file: File) => {
    if (!file) {
      ElMessage.error('请选择文件');
      return;
    }

    const formData = new FormData();
    formData.append('mddFile', file);

    try {
      uploadLoading.value = true;
      const res: any = await upload('/FileInfor/getDataInventory', {
        method: 'post',
        data: formData,
      });

      if (res.data) {
        Object.keys(res.data).forEach((key) => {
          if (res.data[key] != null) {
            addForm[key] = res.data[key];
          }
        });
        emit('changeShowMeatadata', false);
        // ElMessage.success('上传元数据成功');
      }
    } catch (error) {
      console.error('上传失败:', error);
    } finally {
      uploadLoading.value = false;
    }
  };

  // 数据获取相关
  const fetchUnitOptions = async () => {
    try {
      const { data } = await findByBictionaryCode(1, 999, {
        dictionaryCode: 'TYPE_ORGANIZATION',
      } as any);
      unitOptions.value = data?.content || [];
    } catch (error) {
      console.error('获取单位选项失败:', error);
    }
  };

  // const fetchTopDirectory = async () => {
  //   try {
  //     const { data } = await getTopDirectory();
  //     parentIds.value = data || [];
  //   } catch (error) {
  //     console.error('获取顶级目录失败:', error);
  //   }
  // };

  const fetchDatabase = async () => {
    try {
      const { data } = await findAllDb();
      databaseList.value = data!;
    } catch (error) {
      console.error('获取数据库列表失败:', error);
    }
  };

  // 生命周期
  onBeforeMount(() => {
    // fetchTopDirectory();
    fetchDatabase();
    fetchUnitOptions();
  });

  // 暴露接口
  defineExpose({
    formRef,
    addForm,
  });
</script>
