<template>
  <div class="flex h-full flex-col">
    <el-scrollbar class="h-0 flex-1 px-5">
      <div class="h-5" />
      <div class="rounded bg-w px-10 py-7">
        <el-table :data="tableData" style="width: 100%" class="c-table-header mt-6">
          <el-table-column prop="sourceTable" label="源端表名" />
          <el-table-column prop="sourceCount" label="源端表字段数量" />
          <el-table-column prop="targetTable" label="目标端表名" />
          <el-table-column prop="successCount" label="字段自动映射成功数量" />
          <el-table-column label="操作" width="120">
            <template #default="{ row }">
              <el-button link type="primary" @click="onPreview"> 数据预览 </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="mt-5 flex justify-center">
          <el-button type="primary" plain @click="onAutomatch"> 自动匹配 </el-button>
        </div>
      </div>

      <div class="mt-3 rounded bg-w px-10 py-7">
        <h3 class="text-lg">源端表：{{ sourceTableTitle }}</h3>
        <el-table :data="sourceTable" style="width: 100%" class="c-table-header mt-6">
          <el-table-column prop="fieldName" label="字段名称" />
          <el-table-column prop="fieldType" label="字段类型" />
          <el-table-column label="同步">
            <template #default="{ row }">
              <el-switch v-model="row.syncState" :active-value="true" :inactive-value="false" />
            </template>
          </el-table-column>
          <el-table-column label="过滤规则">
            <template #default="{ row }">
              <el-select v-model="row.filteringRule">
                <el-option v-for="item in filteringRules" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="替换规则">
            <template #default="{ row }">
              <el-select v-model="row.replacementRule">
                <el-option v-for="item in replacementRules" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="h-5" />
    </el-scrollbar>

    <div class="box-shadow flex h-[68px] items-center justify-center bg-w">
      <el-button type="primary" @click="onSave"> 下一步 </el-button>
      <el-button @click="lastStep"> 上一步 </el-button>
    </div>
  </div>
</template>

<script setup>
  const tableData = ref([
    {
      sourceTable: 'ahhhhhh',
      sourceCount: 10,
      targetTable: 'bhhhhhhh',
      successCount: 5,
    },
  ]);
  const onPreview = () => {};
  const onAutomatch = () => {};

  const filteringRules = ref([
    {
      label: 'XXXXX过滤规则',
      value: '1',
    },
  ]);

  const replacementRules = ref([
    {
      label: 'XXXXX替换规则',
      value: '1',
    },
  ]);
  const sourceTableTitle = ref('ahhhhhh');
  const sourceTable = ref([
    {
      id: '1',
      fieldName: 'JHKDHLKN',
      fieldType: 'NUMBER',
      syncState: false,
      filteringRule: '',
      replacementRule: '',
    },
  ]);

  const emit = defineEmits(['success']);
  const onSave = () => {
    emit('success', 1);
  };
  const lastStep = () => {
    emit('success', -1);
  };
</script>

<style lang="scss" scoped>
  :deep(.el-input-group__prepend) {
    width: 165px;
  }
</style>
