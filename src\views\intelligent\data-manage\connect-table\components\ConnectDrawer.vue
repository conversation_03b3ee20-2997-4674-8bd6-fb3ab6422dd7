<template>
  <el-drawer v-model="drawer" @close="onCancel">
    <template #header>
      <h4 class="text-m">查看字段</h4>
    </template>

    <el-descriptions direction="vertical" :column="1">
      <el-descriptions-item label="字段名称">
        {{ data.name }}
      </el-descriptions-item>
      <el-descriptions-item label="字段注释">
        {{ data.annotation }}
      </el-descriptions-item>
      <el-descriptions-item label="是否主键">
        {{ data.isPrimary }}
      </el-descriptions-item>
      <el-descriptions-item label="是否允许为空">
        {{ data.isAllowNull }}
      </el-descriptions-item>
      <el-descriptions-item label="数据类型">
        {{ data.type }}
      </el-descriptions-item>
      <el-descriptions-item label="数据长度">
        {{ data.size }}
      </el-descriptions-item>
      <el-descriptions-item label="数据精度">
        {{ data.accuracy }}
      </el-descriptions-item>
      <el-descriptions-item label="数据小数位">
        {{ data.decimalPlace }}
      </el-descriptions-item>
      <el-descriptions-item label="数据默认值">
        <span>{{ ['', null, undefined].includes(data.defaultValue) ? '-' : data.defaultValue }}</span>
      </el-descriptions-item>
      <el-descriptions-item label="备注">
        {{ data.remark }}
      </el-descriptions-item>
    </el-descriptions>
  </el-drawer>
</template>

<script setup>
  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
      required: true,
    },
    data: {
      type: Object,
      required: true,
    },
  });
  const drawer = ref(false);
  watchEffect(() => {
    drawer.value = props.modelValue;
  });

  const emit = defineEmits(['update:modelValue']);
  const onCancel = () => {
    emit('update:modelValue', false);
  };
</script>

<style lang="scss">
  .el-descriptions__label:not(.is-bordered-label) {
    color: $color-tip-text;
  }
</style>
