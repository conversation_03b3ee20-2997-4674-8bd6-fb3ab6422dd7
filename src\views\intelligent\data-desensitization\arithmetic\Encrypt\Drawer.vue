<template>
  <el-drawer v-model="drawer" class="relative" @close="onCancel">
    <template #header>
      <h4 class="text-m">{{ action }}字符脱敏</h4>
    </template>

    <div class="mb-4 flex items-center">
      <div class="h-[13px] w-[3px] bg-p" />
      <span class="ml-2">配置</span>
    </div>
    <el-form ref="formRef" label-position="top" :model="form" :rules="rules">
      <el-form-item label="脱敏算法" prop="type">
        <el-select v-model="form.type" placeholder="请选择脱敏算法" style="width: 100%">
          <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="自定义密钥" prop="secretKey">
        <el-input v-model="form.secretKey" placeholder="请输入密钥" maxlength="50" :disabled="readonly" />
      </el-form-item>
    </el-form>

    <div class="mb-4 mt-7 flex items-center">
      <div class="h-[13px] w-[3px] bg-p" />
      <span class="ml-2">测试</span>
    </div>
    <el-form ref="testRef" label-position="top" :model="testForm" :rules="testRules" hide-required-asterisk>
      <el-form-item label="输入原始数据" prop="originData">
        <div class="flex w-full">
          <el-input v-model="form.originData" class="mr-4 flex-1" placeholder="请输入原始数据" maxlength="300" />
          <el-button type="primary" plain @click="onTest"> 测试 </el-button>
        </div>
      </el-form-item>
      <el-form-item label="脱敏结果">
        <el-input v-model="result" type="textarea" disabled :rows="4" />
      </el-form-item>
    </el-form>

    <template v-if="!readonly" #footer>
      <el-button @click="onCancel"> 取消 </el-button>
      <el-button type="primary" @click="onSave"> 保存 </el-button>
    </template>
  </el-drawer>
</template>

<script setup>
  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
      required: true,
    },
    data: {
      type: Object,
    },
    readonly: {
      type: Boolean,
      default: false,
    },
  });

  const typeOptions = ref([
    {
      value: '1',
      label: 'DES算法',
    },
    {
      value: '2',
      label: 'AES算法',
    },
  ]);
  const form = reactive({
    type: '',
    secretKey: '',
  });
  const rules = reactive({
    type: [{ required: true, message: '请选择脱敏算法', trigger: 'blur' }],
    secretKey: [{ required: true, message: '请输入密钥', trigger: 'blur' }],
  });
  const testForm = reactive({
    originData: '',
  });
  const testRules = reactive({
    originData: [{ required: true, message: '请输入原始数据', trigger: 'blur' }],
  });
  const result = ref('');
  const testRef = ref();
  const onTest = () => {
    testRef.value.validate((valid) => {
      if (valid) {
        console.log('开测');
      }
    });
  };

  const action = computed(() => {
    return props.data?.id ? (props.readonly ? '查看' : '编辑') : '添加';
  });
  const drawer = ref(false);
  watchEffect(() => {
    drawer.value = props.modelValue;
  });
  watch(
    () => props.data,
    (value) => {
      for (const key in form) {
        form[key] = value[key];
      }
    }
  );

  const emit = defineEmits(['update:modelValue', 'success']);
  const formRef = ref();
  const onCancel = () => {
    emit('update:modelValue', false);
    formRef.value.resetFields();
  };
  const onSave = () => {
    formRef.value.validate((valid) => {
      if (valid) {
        onCancel();
        emit('success');
      }
    });
  };
</script>
