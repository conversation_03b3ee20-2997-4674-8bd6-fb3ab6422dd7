/*
 * @OriginalName: 数据资源系统用户管理模块
 * @Description: 管理用户、角色、权限的创建和分配和分配记录查询
 */
import { request } from '@/utils/request';

/**
 * 审核用户注册申请
 * @description 根据注册申请记录主键ID,审核一条用户注册申请。userId是审核人的主键ID，conclusion为0表示审核通过，3表示注册申请拒绝
 */
export function verifyEnroll(data: VerifyDTO) {
  return request<RMDMUserVO>(`/user/verifyEnroll`, {
    method: 'post',
    data,
  });
}

/**
 * 注册新用户信息
 * @description 注册一个新用户信息。UserDTO表示用户的信息。attachmentMaterialId是一个或多个已上传的附件证明文件的记录的主键。该操作在上传用户注册附属材料之后。
 */
export function userEnroll(data: MDMUserEnrollDTO) {
  return request<RMDMUserVO>(`/user/userEnroll`, {
    method: 'post',
    data,
  });
}

/**
 * 更新用户密码
 * @description 根据UserID，在数据库的MDMUser表中若已存在userName相同的记录，则更新表中的相应记录的passWord；不存在用户记录或旧密码不符，则返回null。
 */
export function updatePassword(data: PasswordDTO) {
  return request<RMDMUserVO>(`/user/updatePassword`, {
    method: 'post',
    data,
  });
}

/**
 * 新建或更新用户信息
 * @description 新建或更新用户信息。UserDTO表示用户的信息。若系统中已经存在ID相同的用户，用UserDTO中的内容更新原来内容，若不存在，则用UserDTO中的内容新建一个用户。
 */
export function newOrUpdateUser(data: MDMUserDTO) {
  return request<RMDMUserVO>(`/user/newOrUpdateUser`, {
    method: 'post',
    data,
  });
}

/**
 * 分派或更新用户角色
 * @description 根据用户记录主键ID和角色ID，给用户分派或更新用户角色。
 */
export function newOrUpdateUserRoleVo(data: Array<UserRoleDTO>) {
  return request<RListUserRoleVO>(`/user/newOrUpdateUserRoleVo`, {
    method: 'post',
    data,
  });
}

/**
 * 查找用户
 * @description 按动态条件，获取满足相应条件的用户的基本信息。如果有主键，按主键精确查找，若无主键，则其它条件按与操作进行模糊查询。所有条件均为空时，返回全部记录。
 */
export function findUserVOByCriteria(data: MDMUserCriteria) {
  return request<REntityVOPage>(`/user/findUserVOByCriteria`, {
    method: 'post',
    data,
  });
}

/**
 * 访问用户信息
 * @description 根据用户名，获取用户的信息。该接口为Spring Security鉴权设置的接口！
 */
export function getUserInforSecurity(userName: string, params?: { userName: string }) {
  return request<RMDMUserInfo>(`/user/userInforSecurity/${userName}`, {
    method: 'get',
    params,
  });
}

/**
 * 访问用户信息
 * @description 根据用户名，获取用户的信息
 */
export function getUserInfor(userName: string, params?: { userName: string }) {
  return request<RMDMUserVO>(`/user/userInfor/${userName}`, {
    method: 'get',
    params,
  });
}

/**
 * 解锁用户
 * @description 根据用户记录主键ID,锁定一条用户记录。
 */
export function unlockMDMUserById(userId: number, params?: { userId: number }) {
  return request<RMDMUserVO>(`/user/unlockMDMUserById/${userId}`, {
    method: 'get',
    params,
  });
}

/**
 * 请求管理员再次审核注册申请
 * @description 根据注册申请人主键ID,请求管理员再次审核注册申请人的注册申请。userId是注册申请人的主键ID
 */
export function summitEnrollAgain(userId: number, params?: { userId: number }) {
  return request<RMDMUserVO>(`/user/summitEnrollAgain/${userId}`, {
    method: 'get',
    params,
  });
}

/**
 * 锁定用户
 * @description 根据用户记录主键ID,锁定一条用户记录。
 */
export function lockMDMUserById(userId: number, params?: { userId: number }) {
  return request<RMDMUserVO>(`/user/lockMDMUserById/${userId}`, {
    method: 'get',
    params,
  });
}

/**
 * 查看用户角色分派记录
 * @description 根据用户ID，查看给用户分派角色的全部记录。
 */
export function fingUserRole(userId: number, params?: { userId: number }) {
  return request<RListUserRoleVO>(`/user/fingUserRole/${userId}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找审核结论
 * @description 用户的注册申请被审核的结论。
 */
export function findVerifyResult(userId: number, params?: { userId: number }) {
  return request<RUserEnrollmentVO>(`/user/findVerifyResult/${userId}`, {
    method: 'get',
    params,
  });
}

/**
 * 查看用户合规的角色分派记录
 * @description 根据用户ID，查看用户合规的角色分派记录。
 */
export function findValidUserRoleByMdmUserIdVO(userId: number, params?: { userId: number }) {
  return request<RListUserRoleVO>(`/user/findValidUserRoleByMdmUserIdVO/${userId}`, {
    method: 'get',
    params,
  });
}

/**
 * 查看用户合规的权限分派记录
 * @description 根据用户ID，查看用户合规的角色分派记录。
 */
export function findValidUserPrivilegeByMdmUserIdVO(userId: number, params?: { userId: number }) {
  return request<RListUserPrivilegeVO>(`/user/findValidUserPrivilegeByMdmUserIdVO/${userId}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找用户注册申请的处理记录
 * @description 根据用户主键ID,查找该用户的注册处理记录。若resolutionType为0，查找用户审核处理的记录，若为1查找用户申请记录.
 */
export function findUserEnrollmentByUserId(
  userId: number,
  resolutionType: number,
  pageNum: number,
  pageSize: number,
  params?: { userId: number; resolutionType: number; pageNum: number; pageSize: number }
) {
  return request<R>(`/user/findUserEnrollmentByUserId/${userId}/${resolutionType}/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 查用户所属的机构
 * @description 按用户的Id，该用户所属的机构。
 */
export function findOrgUserByUserId(userId: number, params?: { userId: number }) {
  return request<RListOrgUserVO>(`/user/findUserByOrgId/${userId}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找团队中的成员在团队中的角色
 * @description 按用户的Id，查找与该用户相关的团队，以及其在团队中所承担的角色。
 */
export function findTeamUserRoleVOByUserId(userId: number, params?: { userId: number }) {
  return request<RListTeamUserRoleVO>(`/user/findTeamUserRoleVOByUserId/${userId}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找支付信息
 * @description 按用户的ID，查询该该用户所有的支付记录。
 */
export function findPaymentByUserId(
  userId: number,
  pageNum: number,
  pageSize: number,
  params?: { userId: number; pageNum: number; pageSize: number }
) {
  return request<RVOPage>(`/user/findPaymentByUserId/${userId}/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找用户注册申请记录
 * @description 根据用户主键ID,查找该用户相关的用户注册记录。若resolutionType为0，查找用户审核过的注册记录，若为1查找用户申请的注册记录
 */
export function findEnrollmentByUserId(
  userId: number,
  resolutionType: number,
  pageNum: number,
  pageSize: number,
  params?: { userId: number; resolutionType: number; pageNum: number; pageSize: number }
) {
  return request<RVOPageEnrollmentEnrollmentVO>(
    `/user/findEnrollmentByUserId/${userId}/${resolutionType}/${pageNum}/${pageSize}`,
    {
      method: 'get',
      params,
    }
  );
}

/**
 * 查找注册申请已审过的用户
 * @description 查找注册申请已审核过的用户，包括审核通过和拒绝通过的两类用户
 */
export function findEnrollVerify(
  userType: string,
  pageNum: number,
  pageSize: number,
  params?: { userType: string; pageNum: number; pageSize: number }
) {
  return request<R>(`/user/findEnrollVerify/${userType}/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找注册申请未审核的用户
 * @description 查找待审核注册申请的用户
 */
export function findEnrollProcessing(
  userType: string,
  pageNum: number,
  pageSize: number,
  params?: { userType: string; pageNum: number; pageSize: number }
) {
  return request<R>(`/user/findEnrollProcessing/${userType}/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找注册申请未审核和已审核过的用户
 * @description 查找未审核和已审核过注册申请的用户包括审核通过、拒绝通过和等待审核等三类用户
 */
export function findEnrollProcessVerify(
  userType: string,
  pageNum: number,
  pageSize: number,
  params?: { userType: string; pageNum: number; pageSize: number }
) {
  return request<R>(`/user/findEnrollProcessVerify/${userType}/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 查用户能下载、操纵的数据表
 * @description 按用户的Id，该用户能下载、处理的数据表。
 */
export function findCBDDefTableByUserId(userId: number, params?: { userId: number }) {
  return request<RListCBDDefTableVO>(`/user/findCBDDefTableByUserId/${userId}`, {
    method: 'get',
    params,
  });
}

/**
 * 分页查询全部用户数据记录
 * @description 分页形式查询出系统中的全部用户数据记录。
 */
export function findAllUser(pageNum: number, pageSize: number, params?: { pageNum: number; pageSize: number }) {
  return request<REntityVOPage>(`/user/findAllUserPagable/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除用户角色分派记录
 * @description 根据用户角色分派记录ID，删除给用户分派角色的全部记录
 */
export function deleteUserRole(userRoleId: Array<number>, params?: { userRoleId: Array<number> }) {
  return request<R>(`/user/deleteUserRole/${userRoleId}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除用户角色分派记录
 * @description 根据用户记录主键ID和角色ID，删除给用户分派某个角色的记录。
 */
export function deleteUserRole_1(userId: number, roleId: number, params?: { userId: number; roleId: number }) {
  return request<R>(`/user/deleteUserRole/${userId}/${roleId}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除用户记录
 * @description 根据用户记录主键ID,删除一条或多条用户记录。
 */
export function deletePrivilegeById(userId: Array<number>, params?: { userId: Array<number> }) {
  return request<R>(`/user/deleteMDMUserById/${userId}`, {
    method: 'get',
    params,
  });
}
