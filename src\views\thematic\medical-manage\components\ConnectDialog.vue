<template>
  <el-dialog v-model="show" title="关联数据源" width="90%" @close="onCancel">
    <el-table :data="tableData" style="width: 100%" class="c-table-header" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" />
      <el-table-column prop="name" label="团队名称" min-width="100px" />
      <el-table-column prop="org" label="研究机构" />
      <el-table-column prop="leadResearcher" label="首席研究员" />
      <el-table-column label="合作者">
        <template #default="{ row }">
          <div v-for="(item, i) in row.cooperator" :key="i">
            <span>{{ item.name }}</span>
            <span v-if="row.deputy == i">(代表)</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="orgName" label="机构官方名称" />
      <el-table-column prop="orgContact" label="机构联系人" />
    </el-table>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="onCancel">取消</el-button>
        <el-button type="primary" @click="onSave">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
      required: true,
    },
  });
  const show = ref(false);
  watchEffect(() => {
    show.value = props.modelValue;
  });

  const tableData = ref([
    {
      name: '我的团队1',
      org: 'XXXXXXX大学',
      leadResearcher: '<EMAIL>',
      cooperator: [{ name: '<EMAIL>' }, { name: '<EMAIL>' }, { name: '<EMAIL>' }],
      deputy: 0,
      orgName: 'XXXXXXXXXX机构',
      orgContact: '<EMAIL>',
    },
  ]);
  const checkList = ref([]);
  const handleSelectionChange = (val) => {
    checkList.value = val;
  };

  const emit = defineEmits(['update:modelValue', 'success']);
  const onCancel = () => {
    emit('update:modelValue', false);
  };
  const onSave = () => {
    onCancel();
    emit('success', checkList.value);
  };
</script>
