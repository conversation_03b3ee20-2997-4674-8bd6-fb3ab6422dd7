<template>
  <div class="topbar">
    <div class="home-left">
      <img class="logo" src="@/assets/a52x.png" />
      <h1 class="title">
        {{ title }}
      </h1>
    </div>

    <div v-if="type === 'data-resource'" class="home-center">
      <el-menu
        :default-active="active"
        class="center-menu"
        mode="horizontal"
        text-color="#303333"
        active-text-color="#303333"
        :ellipsis="false"
        @select="onSelectMenu"
      >
        <el-menu-item v-for="(item, index) in resourceMenu" :key="index" :index="item.pathName">
          {{ item.label }}
        </el-menu-item>
      </el-menu>
    </div>

    <div class="home-right">
      <!-- <template v-if="type === 'home'">
        <el-dropdown class="dropdown-select" trigger="click" @command="languageSelect">
          <div class="dropdown-text">
            {{ language }}
            <el-icon class="el-icon--right">
              <arrow-down />
            </el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item v-for="(item, index) in languages" :key="index" :command="item">
                {{ item }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <a class="right-text" @click="onContact">联系我们</a>
      </template> -->

      <template v-if="type === 'data-resource'">
        <a class="right-text" @click="onBackHome">返回首页</a>
      </template>

      <template v-if="type === 'login'">
        <a @click="onBackHome">返回主页</a>
      </template>

      <template v-else>
        <div v-if="!isLogin" class="no-login">
          <a class="login-text" @click="onLogin">登录</a>
          <div class="line" />
          <a class="login-text" @click="onRegister">注册</a>
        </div>
        <div v-else class="login-container">
          <!-- <span class="split" /> -->
          <el-dropdown class="dropdown-select" trigger="click">
            <div class="dropdown-text">
              <el-icon class="dropdown-icon--user" :size="20">
                <User />
              </el-icon>
              <div class="dropdown-username">
                {{ username }}
              </div>
              <el-icon :size="14">
                <CaretBottom />
              </el-icon>
            </div>
            <template #dropdown>
              <TopbarDropdown />
            </template>
          </el-dropdown>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
  import TopbarDropdown from './TopbarDropdown.vue';
  import { computed } from 'vue';
  import { ElMessage } from 'element-plus';
  import { useRouter, useRoute } from 'vue-router';
  const router = useRouter();
  const route = useRoute();
  import { useUsers } from '@/store/user-info';
  let store = useUsers();
  import { useDataBrowse } from '@/store/data-browse';
  const dataStore = useDataBrowse();

  defineProps({
    title: {
      type: String,
      default: '国家脑疾病临床大数据平台',
    },
    type: {
      type: String,
      default: 'home', //home,data-resource
    },
  });

  let isLogin = computed(() => {
    return !!store.user.token;
  });

  const languages = ref(['中文']);
  const language = ref(languages.value[0]);
  function languageSelect(command) {
    language.value = command;
  }

  const username = computed(() => {
    return store.user.username;
  });
  function onLogin() {
    router.push({ name: 'Login' });
  }
  function onRegister() {
    router.push({ name: 'PersonalRegister' });
  }
  function onContact() {
    ElMessage('暂未开放');
    // TODO
  }
  function onBackHome() {
    router.replace({ name: 'Index' });
  }

  const resourceMenu = ref([
    {
      id: 1,
      pathName: 'ResourceHome',
      label: '主页',
    },
    {
      id: 2,
      pathName: 'DataBrowse',
      label: '数据浏览',
    },
    {
      id: 3,
      pathName: 'ResourceDataSearch',
      label: '数据搜索',
    },
    {
      id: 4,
      pathName: 'ResourceTools',
      label: '工具下载',
    },
    {
      id: 5,
      pathName: 'ResourceUserNotice',
      label: '用户须知',
    },
  ]);
  function onSelectMenu(e) {
    router.push({ name: e });
    if (e === 'DataBrowse') {
      dataStore.setData({ id: '' });
    }
  }
  let active = ref('');
  watchEffect(() => {
    if (route.path.includes('data-resource/browse')) {
      active.value = 'DataBrowse';
    } else if (route.path.includes('data-resource/home')) {
      active.value = 'ResourceHome';
    } else if (route.path.includes('data-resource/search')) {
      active.value = 'ResourceDataSearch';
    } else if (route.path.includes('/data-resource/tools')) {
      active.value = 'ResourceTools';
    } else if (route.path.includes('data-resource/user-notice')) {
      active.value = 'ResourceUserNotice';
    }
  });
</script>

<style lang="scss" scoped>
  .topbar {
    background: #fff;
    height: 64px;
    display: flex;
    align-items: center;
    padding: 0 40px;
    box-shadow: 0px 1px 12px rgba(0, 0, 0, 0.15);
    font-size: 14px;
    z-index: 1;
  }

  .home-left {
    display: flex;
    align-items: center;

    .logo {
      width: 44px;
      height: 41px;
    }

    .title {
      font-size: 20px;
      line-height: 29px;
      margin-left: 10px;
    }
  }

  .home-center {
    width: 0;
    flex: 1;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    .center-menu {
      position: relative;
      bottom: -2px;
    }

    .el-menu--horizontal {
      border-bottom: none;
      --el-menu-hover-bg-color: #fff !important;
    }

    .el-menu--horizontal > .el-menu-item.is-active {
      border-bottom: 3px solid #01697f;
      font-weight: bold;
    }
  }

  .home-right {
    margin-left: auto;
    display: flex;
    align-items: center;

    .right-text {
      margin: 0 28px;
    }

    .no-login {
      display: flex;
      align-items: center;
    }

    .line {
      margin: 0 12px;
      background-color: #c4c7c8;
      width: 1px;
      height: 15px;
    }
  }

  .split {
    height: 14px;
    width: 0;
    border: 1px solid #c8cbcc;
    margin-right: 20px;
  }

  .dropdown-select {
    color: $color-main-text;
    .dropdown-text {
      user-select: none;
      display: flex;
      align-items: center;
    }

    .dropdown-icon--user {
      border: 1px solid $color-main-text;
      border-radius: 100%;
    }

    .dropdown-username {
      margin-left: 8px;
      margin-right: 7px;
    }
  }
</style>
