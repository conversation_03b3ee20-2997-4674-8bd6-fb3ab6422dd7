<template>
  <ul v-if="type === 'category'" class="flex">
    <li v-for="(item, index) in list" :key="index">
      <template v-if="index !== list.length - 1">
        <span class="cursor-pointer text-tip" @click="gotoFront(item)">{{ item.name }}</span>
        <span class="mx-2 text-tip">&gt;</span>
      </template>
      <span v-else>{{ item.name }}</span>
    </li>
  </ul>

  <ul v-else-if="type === 'dataField'" class="flex">
    <li v-for="(item, index) in list" :key="index">
      <span class="cursor-pointer text-p" @click="gotoFront(item)">{{ item.name }}</span>
      <span v-if="index !== list.length - 1" class="mx-2 text-tip">&gt;</span>
    </li>
  </ul>
</template>

<script setup>
  /* 数据浏览-面包屑导航 */
  import { useRouter } from 'vue-router';
  const router = useRouter();
  import { useDataBrowse } from '@/store/data-browse';
  const store = useDataBrowse();

  const props = defineProps({
    list: {
      type: Array,
      default() {
        return [];
      },
    },
    type: {
      type: String,
      default: 'category', //category|dataField
    },
  });
  const emit = defineEmits(['click-item']);

  //跳转类别页面
  const gotoFront = (row) => {
    emit('click-item', row);
  };
</script>
