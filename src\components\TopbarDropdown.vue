<template>
  <el-dropdown-menu>
    <el-dropdown-item v-for="item in dropdownlist" :key="item.id" @click="changeMenu(item)">
      {{ item.text }}
    </el-dropdown-item>
  </el-dropdown-menu>
</template>

<script setup lang="ts">
  import { useUsers } from '@/store/user-info.js';
  const store = useUsers();
  import { useRouter } from 'vue-router';
  const router = useRouter();

  let dropdownlist = [
    {
      id: 1,
      text: '用户中心',
      pathname: 'WorkbenchBaseInfo',
    },
    // {
    //   id: 2,
    //   text: '设置',
    //   pathname: '',
    // },
    {
      id: 3,
      text: '密码',
      pathname: 'WorkbenchAccessSecurity',
    },
    // {
    //   id: 4,
    //   text: '帮助',
    //   pathname: '',
    // },
    {
      id: 5,
      text: '退出登录',
      pathname: '',
    },
  ];

  let changeMenu = (e) => {
    if (e.text === '退出登录') {
      store.logout().then(() => {
        router.replace({ name: 'Index' });
      });
    } else {
      router.push({ name: e.pathname });
    }
  };
</script>
