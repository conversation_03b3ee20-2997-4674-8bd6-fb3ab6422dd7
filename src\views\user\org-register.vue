<template>
  <div class="flex h-[100%] w-[100%] flex-col items-center pb-10">
    <h2 class="my-5 text-center text-3xl">注册账号</h2>

    <div class="flex h-0 w-[1200px] flex-1 flex-col bg-w">
      <div class="h-16 border-b border-solid px-10" style="border-color: #e1e3e6">
        <el-row class="flex h-[100%] items-center">
          <el-col :span="6">
            <div class="flex items-center" @click="goorRegister">
              <el-icon class="cursor-pointer" color="#007f99" size="14">
                <ArrowLeft />
              </el-icon>
              <span class="cursor-pointer text-sm text-p">切换个人注册</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="flex items-center justify-center text-xl">
              <span>机构注册</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="flex flex-row-reverse text-sm">
              <span class="ml-2 cursor-pointer text-p" @click="goLogin">立即登录</span>
              <span>已有账号?</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <div class="h-0 flex-1">
        <el-scrollbar class="h-full">
          <el-form ref="accountInfo" status-icon label-position="right" :rules="accountRules" :model="accountForm">
            <el-form-item label="用户名：" prop="userName">
              <div class="absolute -left-48 flex h-[20px] items-center">
                <div class="mr-1 h-[55%] w-[2.5px] bg-[#007f99]" />
                <span class="text-sm font-bold">账户信息</span>
              </div>
              <el-tooltip effect="light" placement="right-start">
                <template #content>
                  <div class="w-[255px] text-tip">
                    <div>长度为4-16位字符</div>
                    <div class="mt-1">可使用英文、数字、下划线组合，不能只有数字或下划线，必须包含英文</div>
                  </div>
                </template>
                <el-input v-model="accountForm.userName" placeholder="请输入" maxlength="16" />
              </el-tooltip>
            </el-form-item>

            <el-form-item label="密码：" prop="password">
              <el-tooltip effect="light" placement="right-start" :visible="pwdVisible">
                <template #content>
                  <div class="text-xs text-tip">
                    <div class="mb-1">安全程度：{{ safeStrength }}</div>
                    <el-progress :stroke-width="8" :percentage="pwdProgress" :show-text="false" />
                    <ul class="mt-2 pl-5">
                      <li>最少8个字符</li>
                      <li>不能全为数字、字母或特殊符号</li>
                      <li>数字、字母、特殊字符任意组合</li>
                    </ul>
                  </div>
                </template>
                <el-input
                  v-model="accountForm.password"
                  type="password"
                  placeholder="请输入"
                  maxlength="20"
                  @input="onPwdInput"
                  @focus="onPwdFocus"
                  @blur="onPwdBlur"
                />
              </el-tooltip>
            </el-form-item>

            <el-form-item label="确认密码：" prop="passwordAgain">
              <el-input v-model="accountForm.passwordAgain" type="password" placeholder="请输入" maxlength="20" />
            </el-form-item>

            <el-form-item label="邮箱：" prop="email">
              <el-tooltip effect="light" placement="right-start">
                <template #content>
                  <div class="flex h-fit w-[15.5vw] flex-col items-center justify-start text-[#565b5c]">
                    <span> 审核信息管理员以邮件形式通知，请注意查收邮件！ </span>
                  </div>
                </template>
                <el-input v-model="accountForm.email" placeholder="请输入" />
              </el-tooltip>
            </el-form-item>

            <!-- <el-form-item label="邮箱验证码：" prop="captcha" class="flex w-[100%] items-center">
              <el-input placeholder="请输入" v-model="accountForm.captcha" class="havebutton" />
              <el-button v-disable-button="30" class="b1" plain @click="getCode"> 获取验证码 </el-button>
            </el-form-item> -->
          </el-form>

          <el-form
            ref="personInfo"
            label-posts="right"
            status-icon
            :model="personForm"
            :rules="personRules"
            class="mt-10"
          >
            <el-form-item label="机构名称：" prop="name">
              <div class="absolute -left-48 flex h-[20px] items-center">
                <div class="mr-1 h-[55%] w-[2.5px] bg-[#007f99]" />
                <span class="text-sm font-bold">机构信息</span>
              </div>
              <el-input v-model="personForm.name" placeholder="请输入" maxlength="100" />
            </el-form-item>
            <!-- <el-form-item label="性别：">
              <el-select v-model="personForm.sex" class="w-[100%]" placeholder="请选择">
                <el-option v-for="item in sexOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item> -->
            <!-- <el-form-item label="证件类型：" prop="documentType">
              <el-select v-model="personForm.documentType" class="w-[100%]" placeholder="请选择">
                <el-option v-for="item in idTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item> -->
            <el-form-item label="统一社会信用代码：" prop="idCard">
              <div class="flex w-full">
                <el-input v-model="personForm.idCard" placeholder="请输入" maxlength="50" class="mr-4 w-0 flex-1" />
                <a href="https://www.cods.org.cn/gscx/" target="_blank" class="text-p">查询地址</a>
              </div>
            </el-form-item>
            <!-- <el-form-item label="学历：" prop="degree">
              <el-select v-model="personForm.degree" class="w-[100%]" placeholder="请选择">
                <el-option v-for="item in edubacOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="职称：" prop="jobTitle">
              <el-input v-model="personForm.jobTitle" class="w-[100%]" placeholder="请输入" maxlength="50"></el-input>
            </el-form-item> -->
            <el-form-item label="联系电话：" prop="phone">
              <el-input v-model="personForm.phone" placeholder="请输入" />
            </el-form-item>
            <el-form-item label="通讯地址：" prop="address">
              <el-input v-model="personForm.address" placeholder="请输入" maxlength="200" />
            </el-form-item>
            <!-- <el-form-item label="昵称：" prop="nickName">
              <el-input v-model="personForm.nickName" placeholder="请输入" maxlength="10" />
            </el-form-item> -->
          </el-form>

          <!-- <el-form v-else label-posts="right" class="mt-10">
            <el-form-item>
              <div class="absolute -left-7 flex h-[20px] items-center">
                <div class="mr-1 h-[55%] w-[2.5px] bg-[#007f99]" />
                <span class="text-sm font-bold">证明材料</span>
              </div>
            </el-form-item>
            <div class="flex justify-end">
              <div class="w-[500px]">
                <UploadButtonDrag accept=".png,.jpg,.jpeg" :limit="3" multiple tip="请上传工作证和身份证正反面" />
              </div>
            </div>
          </el-form>

          <div class="mt-10 flex justify-center">
            <el-button type="primary" @click="onApply">提交材料</el-button>
          </div> -->

          <!-- <el-form
            ref="organizationInfo"
            label-posts="right"
            status-icon
            :model="organizationForm"
            class="mt-10"
            :rules="organizationRules"
          >
            <el-form-item label="机构名称：" prop="organizationName">
              <div class="absolute -left-48 flex h-[20px] items-center">
                <div class="mr-1 h-[55%] w-[2.5px] bg-[#007f99]"></div>
                <span class="text-sm font-bold">机构信息</span>
              </div>
              <el-select filterable v-model="organizationForm.organizationName" class="w-[100%]" placeholder="请选择">
                <el-option v-for="item in ornameOptions" :key="item.id" :label="item.zhName" :value="item.zhName" />
              </el-select>
            </el-form-item>

            <el-form-item label="机构邮箱：" prop="organizationEmail">
              <el-input placeholder="请输入" v-model="organizationForm.organizationEmail" maxlength="50" />
            </el-form-item>

            <el-form-item label="部门：" prop="department">
              <el-input v-model="organizationForm.department" class="w-[100%]" placeholder="请输入" maxlength="50">
              </el-input>
            </el-form-item>

            <el-form-item label="职位：" prop="posts">
              <el-input placeholder="请输入" v-model="organizationForm.posts" maxlength="30" />
            </el-form-item>
          </el-form> -->

          <div class="h-3" />
        </el-scrollbar>
      </div>

      <div class="b z-[1] py-3 pl-[360px]">
        <div class="flex items-center">
          <el-checkbox v-model="isAgree" label="已阅读并同意" :indeterminate="false" />
          <div class="text-sm">
            <a class="text-p" @click="toProtocol()">《国家脑疾病临床大数据平台用户注册须知》</a>
          </div>
        </div>
        <el-button :loading="saveLoading" color="#007f99" type="primary" class="mt-3 w-[480px]" @click="clickRegister">
          注册
        </el-button>
      </div>
    </div>
  </div>

  <ProtocolDialog v-model="showProtocol" />
</template>

<script setup lang="ts">
  import ProtocolDialog from '@/components/ProtocolDialog.vue';
  import UploadButtonDrag from '@/components/UploadButtonDrag.vue';
  import { userEnroll } from '@/api/index';
  import { elmes } from '@/hook';
  // import { getauthcode } from '@/api/user/getcode/index';
  // import { getallorinfo, perRegister } from '@/api/user/register/index';
  import { validatePassword, validateUsername, validateIDCard, validatePhoneNumber } from '@/utils/validator';
  import { usePasswordStrength } from '@/utils/form';
  import { encryption } from '@/utils/crypto';
  const { pwdVisible, safeStrength, pwdProgress, onPwdInput, onPwdFocus, onPwdBlur } = usePasswordStrength();
  import { useRouter } from 'vue-router';
  import { FormInstance } from 'element-plus';
  let router = useRouter();

  const step = ref(1);
  const accountInfo = ref<FormInstance>();
  const personInfo = ref<FormInstance>();
  const organizationInfo = ref<FormInstance>();
  const sexOptions = [
    {
      value: '男',
      label: '男',
    },
    {
      value: '女',
      label: '女',
    },
  ];
  const idTypeOptions = [
    {
      value: '身份证',
      label: '身份证',
    },
  ];
  const edubacOptions = [
    {
      value: '博士后',
      label: '博士后',
    },
    {
      value: '博士',
      label: '博士',
    },

    {
      value: '研究生',
      label: '研究生',
    },
    {
      value: '本科',
      label: '本科',
    },
    {
      value: '专科',
      label: '专科',
    },
  ];
  const isAgree = ref(false);

  let ornameOptions = ref([]);
  onMounted(() => {
    // getallorinfo().then((res) => {
    //   ornameOptions.value = res.data.data;
    // });
  });

  let accountRules = reactive({
    userName: [
      { required: true, message: '请输入用户名', trigger: 'blur' },
      {
        validator: validateUsername,
      },
    ],
    password: [
      { required: true, message: '请输入密码', trigger: 'blur' },
      {
        validator: validatePassword,
      },
    ],
    passwordAgain: [
      { required: true, message: '请输入确认密码', trigger: 'blur' },
      {
        validator: validateConfirmPassword,
      },
    ],
    email: [
      { required: true, message: '请输入邮箱', trigger: 'blur' },
      {
        type: 'email',
        message: '请输入有效的邮箱地址',
        trigger: ['blur', 'change'],
      },
    ],
    // captcha: [{ required: true, message: '请输入验证码', trigger: 'blur' }],
  });
  let personRules = reactive({
    name: [
      {
        required: true,
        message: '请填写机构名称',
      },
    ],
    documentType: [{ required: true, message: '请选择您的证件类型' }],
    idCard: [
      { required: true, message: '请输入统一社会信用代码' },
      // {
      //   validator: validateIDCard,
      // },
    ],
    phone: [
      { required: true, message: '请输入联系电话' },
      // {
      //   validator: validatePhoneNumber,
      // },
    ],
    address: [{ required: true, message: '请输入地址' }],
  });
  let organizationRules = reactive({
    organizationName: [
      { required: false, message: '请填写您的机构名称' },
      // { min:6,max:16, message: '用户名长度最少6位', trigger: 'blur' }
    ],
    organizationEmail: [
      { required: false, message: '请输入您的机构邮箱' },
      {
        type: 'email',
        message: '请输入有效的邮箱地址',
        trigger: ['blur', 'change'],
      },
    ],
  });
  let accountForm = reactive({
    userName: '',
    password: '',
    passwordAgain: '',
    email: '',
    // captcha: '',
  });
  let personForm = reactive({
    name: '',
    idCard: '',
    phone: '',
    address: '',
    nickName: '',
    // sex: '',
    // degree: '',
    // jobTitle: '',
    // documentType: '',
  });
  let organizationForm = reactive({
    organizationName: '',
    department: '',
    organizationEmail: '',
    posts: '',
  });

  function validateConfirmPassword(rule, value, callback) {
    // 自定义校验规则函数，用于确认密码是否与密码一致
    if (value !== accountForm.password) {
      callback(new Error('两次输入的密码不一致'));
    } else {
      callback();
    }
  }

  //获取验证码
  let getCode = async () => {
    let emailrule =
      /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    if (accountForm.userName && accountForm.email) {
      if (emailrule.test(accountForm.email)) {
        let data = {
          uname: accountForm.userName,
          to: accountForm.email,
          state: '个人账号注册',
          detail: '注册账号',
        };
        // getauthcode(data).then((res) => {
        //   console.log(res);
        //   if (res.data.code == 0) {
        //     elmes('s', '验证码已发送至邮箱，请注意查收');
        //   } else {
        //     elmes('f', '验证码发送失败');
        //   }
        // });
      } else {
        elmes('w', '邮箱格式不正确');
      }
    } else {
      elmes('w', '请填写完整用户名和邮箱');
      return;
    }
  };

  const saveLoading = ref(false);
  let clickRegister = async () => {
    try {
      saveLoading.value = true;
      await Promise.all([accountInfo.value!.validate(), personInfo.value!.validate()]);

      if (!isAgree.value) {
        elmes('w', '请先阅读须知并勾选同意');
        return;
      }
      const encPassword = encryption(accountForm.password, import.meta.env.VITE_PWD_ENC_KEY);
      let params = Object.assign(
        {
          rltSybole: {
            del_flag: '0',
            lock_flag: '0',
          },
          userType: '机构用户',
        },
        accountForm,
        personForm
      );
      params.password = encPassword;
      params.passwordAgain = encPassword;
      await userEnroll(params);
      elmes('s', '注册成功');
      router.replace({ name: 'Login' });
    } catch (error) {
      console.log(error);
    } finally {
      saveLoading.value = false;
    }
  };
  let goorRegister = () => {
    router.push({ name: 'PersonalRegister' });
  };
  let goLogin = () => {
    router.push({ name: 'Login' });
  };

  const showProtocol = ref(false);
  const toProtocol = () => {
    showProtocol.value = true;
    // router.push({ name: 'Protocol' });
  };

  //申请
  const onApply = () => {};
</script>

<style scoped lang="scss">
  .b1 {
    min-width: min-content;
  }
  .el-form-item :deep(label) {
    min-width: fit-content;
    width: 25%;
  }
  .el-scrollbar :deep(.el-scrollbar__wrap) {
    width: 100%;
  }
  .el-scrollbar :deep(.el-scrollbar__view) {
    margin-top: 18px;
    width: 55%;
    position: relative;
    right: -15.675%;
  }
  .b {
    box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.12);
  }
  .el-input :deep(span) {
    font-size: small;
  }
  :deep(.el-input.havebutton) {
    width: 65%;
  }
  :deep(.el-button.b1) {
    width: 28%;
  }
  .el-form-item :deep(.el-form-item__content) {
    display: flex;
    justify-content: space-between;
  }
</style>
