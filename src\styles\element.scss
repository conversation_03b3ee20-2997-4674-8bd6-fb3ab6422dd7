/*
 * @Description: element plus 自定义主题
 */

/* 只需要重写你需要的即可 */
@forward 'element-plus/theme-chalk/src/common/var.scss' with (
  $colors: (
    'primary': (
      'base': #007f99,
    ),
  )
);

// 如果只是按需导入，则可以忽略以下内容。
// 如果你想导入所有样式:
@use 'element-plus/theme-chalk/src/index.scss' as *;

.el-table {
  --el-table-header-text-color: #303333;
  .el-table__cell {
    padding: 12px 0;
  }
}
.c-table-header {
  --el-table-header-bg-color: #f0f2f5;
}

.el-tabs {
  --el-border-color-light: transparent;
}

.el-link.el-link--primary,
.el-button--primary.is-link {
  color: #2979ff;
}
.el-link.el-link--primary:hover,
.el-link.el-link--primary:focus,
.el-button.is-link:hover,
.el-button.is-link:focus {
  color: #9bb8f0;
}

.el-drawer__header {
  margin-bottom: 0;
  padding: 0;
  border-bottom: 1px solid #e1e3e6;
  padding: 14px 20px;
  color: #c3c4c7;
}
.el-drawer__footer {
  border-top: 1px solid #e1e3e6;
}

.fixed-height-dialog {
  .el-dialog__body {
    height: 0;
    flex: 1;
  }
}
